//////////////////////////////////////////////////////////////////////
// Pano2VR 7.1.9/20995 HTML5/CSS3 & WebGL Panorama Player           //
// Trial License: For evaluation only!                              //
// (c) 2025, Garden Gnome Software, https://ggnome.com              //
//////////////////////////////////////////////////////////////////////

/*
 Copyright 2005-2024 Garden Gnome GmbH.
 All rights reserved
*/
var Yb="function"==typeof Object.defineProperties?Object.defineProperty:function(nb,ub,rb){if(nb==Array.prototype||nb==Object.prototype)return nb;nb[ub]=rb.value;return nb};function Zb(nb){nb=["object"==typeof globalThis&&globalThis,nb,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var ub=0;ub<nb.length;++ub){var rb=nb[ub];if(rb&&rb.Math==Math)return rb}throw Error("Cannot find global object");}var $b=Zb(this);
function bd(nb,ub){if(ub)a:{var rb=$b;nb=nb.split(".");for(var pb=0;pb<nb.length-1;pb++){var qb=nb[pb];if(!(qb in rb))break a;rb=rb[qb]}nb=nb[nb.length-1];pb=rb[nb];ub=ub(pb);ub!=pb&&null!=ub&&Yb(rb,nb,{configurable:!0,writable:!0,value:ub})}}bd("Array.prototype.includes",function(nb){return nb?nb:function(ub,rb){var pb=this;pb instanceof String&&(pb=String(pb));var qb=pb.length;rb=rb||0;for(0>rb&&(rb=Math.max(rb+qb,0));rb<qb;rb++){var lb=pb[rb];if(lb===ub||Object.is(lb,ub))return!0}return!1}});
(()=>{function nb(pb){var qb=rb[pb];if(void 0!==qb)return qb.exports;qb=rb[pb]={exports:{}};ub[pb](qb,qb.exports,nb);return qb.exports}var ub={768:pb=>{pb.exports=function(){return!1}}},rb={};(()=>{nb.n=pb=>{var qb=pb&&pb.__esModule?()=>pb["default"]:()=>pb;nb.d(qb,{a:qb});return qb}})();(()=>{nb.d=(pb,qb)=>{for(var lb in qb)nb.wq(qb,lb)&&!nb.wq(pb,lb)&&Object.defineProperty(pb,lb,{enumerable:!0,get:qb[lb]})}})();(()=>{nb.wq=(pb,qb)=>Object.prototype.hasOwnProperty.call(pb,qb)})();(()=>{function pb(){var a=
"perspective",b=["Webkit","Moz","O","ms","Ms"],e;for(e=0;e<b.length;e++)"undefined"!==typeof document.documentElement.style[b[e]+"Perspective"]&&(a=b[e]+"Perspective");"undefined"!==typeof document.documentElement.style[a]?"webkitPerspective"in document.documentElement.style?(a=document.createElement("style"),b=document.createElement("div"),e=document.head||document.getElementsByTagName("head")[0],a.textContent="@media (-webkit-transform-3d) {#ggswhtml5{height:5px}}",e.appendChild(a),b.id="ggswhtml5",
document.documentElement.appendChild(b),e=5===b.offsetHeight,a.parentNode.removeChild(a),b.parentNode.removeChild(b)):e=!0:e=!1;return e}function qb(){let a;if(a=!!window.WebGLRenderingContext)try{let b=document.createElement("canvas");b.width=100;b.height=100;let e=b.getContext("webgl");e||(e=b.getContext("experimental-webgl"));a=!!e}catch(b){a=!1}return a}class lb{constructor(a,b,e,f,h){this.ac=this.Ac=this.v=this.yd=this.z=this.y=this.x=0;a&&(this.x=a);b&&(this.y=b);e&&(this.z=e);f&&(this.yd=f);
h&&(this.v=h)}init(a,b,e,f,h){this.x=a;this.y=b;this.z=e;f&&(this.yd=f);h&&(this.v=h)}toString(){return"("+this.x+","+this.y+","+this.z+") - ("+this.yd+","+this.v+")"}ya(a){let b=Math.sin(a);a=Math.cos(a);let e=this.y,f=this.z;this.y=a*e-b*f;this.z=b*e+a*f}$u(){let a=this.y;this.y=-this.z;this.z=a}Zu(){let a=this.y;this.y=this.z;this.z=-a}Ca(a){let b=Math.sin(a);a=Math.cos(a);let e=this.x,f=this.z;this.x=a*e+b*f;this.z=-b*e+a*f}av(){let a=this.x;this.x=-this.z;this.z=a}Sa(a){let b=Math.sin(a);a=Math.cos(a);
let e=this.x,f=this.y;this.x=a*e-b*f;this.y=b*e+a*f}Sq(){let a=this.x;this.x=-this.y;this.y=a}bc(a){this.ya(a*Math.PI/180)}Sc(a){this.Ca(a*Math.PI/180)}ke(a){this.Sa(a*Math.PI/180)}clone(){return new lb(this.x,this.y,this.z,this.yd,this.v)}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}normalize(){let a=this.length();0<a&&(a=1/a,this.x*=a,this.y*=a,this.z*=a)}qk(a){return this.x*a.x+this.y*a.y+this.z*a.z}Fe(a,b){let e;e=Math.cos(b*Math.PI/180);this.x=e*Math.sin(a*Math.PI/180);
this.y=Math.sin(b*Math.PI/180);this.z=e*Math.cos(a*Math.PI/180)}kf(){return 180*Math.atan2(-this.x,-this.z)/Math.PI}lf(){return 180*Math.asin(this.y/this.length())/Math.PI}ge(a,b,e){this.x=a.x*e+b.x*(1-e);this.y=a.y*e+b.y*(1-e);this.z=a.z*e+b.z*(1-e);this.yd=a.yd*e+b.yd*(1-e);this.v=a.v*e+b.v*(1-e)}}var kb={create:function(a){let b;b="undefined"!=typeof Float32Array?new Float32Array(16):Array(16);a&&(b[0]=a[0],b[1]=a[1],b[2]=a[2],b[3]=a[3],b[4]=a[4],b[5]=a[5],b[6]=a[6],b[7]=a[7],b[8]=a[8],b[9]=a[9],
b[10]=a[10],b[11]=a[11],b[12]=a[12],b[13]=a[13],b[14]=a[14],b[15]=a[15]);return b},set:function(a,b){b[0]=a[0];b[1]=a[1];b[2]=a[2];b[3]=a[3];b[4]=a[4];b[5]=a[5];b[6]=a[6];b[7]=a[7];b[8]=a[8];b[9]=a[9];b[10]=a[10];b[11]=a[11];b[12]=a[12];b[13]=a[13];b[14]=a[14];b[15]=a[15];return b},identity:function(a){a[0]=1;a[1]=0;a[2]=0;a[3]=0;a[4]=0;a[5]=1;a[6]=0;a[7]=0;a[8]=0;a[9]=0;a[10]=1;a[11]=0;a[12]=0;a[13]=0;a[14]=0;a[15]=1;return a},multiply:function(a,b,e){e||(e=a);let f=a[0],h=a[1],n=a[2],r=a[3],w=a[4],
x=a[5],y=a[6],z=a[7],cb=a[8],ab=a[9],db=a[10],gb=a[11],bb=a[12],fb=a[13],ib=a[14];a=a[15];let hb=b[0],mb=b[1],ob=b[2],jb=b[3],sb=b[4],vb=b[5],yb=b[6],zb=b[7],xb=b[8],Fb=b[9],Ib=b[10],Jb=b[11],Kb=b[12],Lb=b[13],Mb=b[14];b=b[15];e[0]=hb*f+mb*w+ob*cb+jb*bb;e[1]=hb*h+mb*x+ob*ab+jb*fb;e[2]=hb*n+mb*y+ob*db+jb*ib;e[3]=hb*r+mb*z+ob*gb+jb*a;e[4]=sb*f+vb*w+yb*cb+zb*bb;e[5]=sb*h+vb*x+yb*ab+zb*fb;e[6]=sb*n+vb*y+yb*db+zb*ib;e[7]=sb*r+vb*z+yb*gb+zb*a;e[8]=xb*f+Fb*w+Ib*cb+Jb*bb;e[9]=xb*h+Fb*x+Ib*ab+Jb*fb;e[10]=
xb*n+Fb*y+Ib*db+Jb*ib;e[11]=xb*r+Fb*z+Ib*gb+Jb*a;e[12]=Kb*f+Lb*w+Mb*cb+b*bb;e[13]=Kb*h+Lb*x+Mb*ab+b*fb;e[14]=Kb*n+Lb*y+Mb*db+b*ib;e[15]=Kb*r+Lb*z+Mb*gb+b*a;return e},translate:function(a,b,e){let f=b[0],h=b[1];b=b[2];if(!e||a==e)return a[12]=a[0]*f+a[4]*h+a[8]*b+a[12],a[13]=a[1]*f+a[5]*h+a[9]*b+a[13],a[14]=a[2]*f+a[6]*h+a[10]*b+a[14],a[15]=a[3]*f+a[7]*h+a[11]*b+a[15],a;let n=a[0],r=a[1],w=a[2],x=a[3],y=a[4],z=a[5],cb=a[6],ab=a[7],db=a[8],gb=a[9],bb=a[10],fb=a[11];e[0]=n;e[1]=r;e[2]=w;e[3]=x;e[4]=
y;e[5]=z;e[6]=cb;e[7]=ab;e[8]=db;e[9]=gb;e[10]=bb;e[11]=fb;e[12]=n*f+y*h+db*b+a[12];e[13]=r*f+z*h+gb*b+a[13];e[14]=w*f+cb*h+bb*b+a[14];e[15]=x*f+ab*h+fb*b+a[15];return e},scale:function(a,b,e){let f=b[0],h=b[1];b=b[2];if(!e||a==e)return a[0]*=f,a[1]*=f,a[2]*=f,a[3]*=f,a[4]*=h,a[5]*=h,a[6]*=h,a[7]*=h,a[8]*=b,a[9]*=b,a[10]*=b,a[11]*=b,a;e[0]=a[0]*f;e[1]=a[1]*f;e[2]=a[2]*f;e[3]=a[3]*f;e[4]=a[4]*h;e[5]=a[5]*h;e[6]=a[6]*h;e[7]=a[7]*h;e[8]=a[8]*b;e[9]=a[9]*b;e[10]=a[10]*b;e[11]=a[11]*b;e[12]=a[12];e[13]=
a[13];e[14]=a[14];e[15]=a[15];return e},rotate:function(a,b,e,f){let h=e[0],n=e[1];e=e[2];let r=Math.sqrt(h*h+n*n+e*e);if(!r)return null;1!=r&&(r=1/r,h*=r,n*=r,e*=r);let w=Math.sin(b),x=Math.cos(b),y=1-x;b=a[0];r=a[1];let z=a[2],cb=a[3],ab=a[4],db=a[5],gb=a[6],bb=a[7],fb=a[8],ib=a[9],hb=a[10],mb=a[11],ob=h*h*y+x,jb=n*h*y+e*w,sb=e*h*y-n*w,vb=h*n*y-e*w,yb=n*n*y+x,zb=e*n*y+h*w,xb=h*e*y+n*w;h=n*e*y-h*w;n=e*e*y+x;f?a!=f&&(f[12]=a[12],f[13]=a[13],f[14]=a[14],f[15]=a[15]):f=a;f[0]=b*ob+ab*jb+fb*sb;f[1]=
r*ob+db*jb+ib*sb;f[2]=z*ob+gb*jb+hb*sb;f[3]=cb*ob+bb*jb+mb*sb;f[4]=b*vb+ab*yb+fb*zb;f[5]=r*vb+db*yb+ib*zb;f[6]=z*vb+gb*yb+hb*zb;f[7]=cb*vb+bb*yb+mb*zb;f[8]=b*xb+ab*h+fb*n;f[9]=r*xb+db*h+ib*n;f[10]=z*xb+gb*h+hb*n;f[11]=cb*xb+bb*h+mb*n;return f},Qs:function(a,b,e,f,h,n,r){r||(r=kb.create());let w=b-a,x=f-e,y=n-h;r[0]=2*h/w;r[1]=0;r[2]=0;r[3]=0;r[4]=0;r[5]=2*h/x;r[6]=0;r[7]=0;r[8]=(b+a)/w;r[9]=(f+e)/x;r[10]=-(n+h)/y;r[11]=-1;r[12]=0;r[13]=0;r[14]=-(n*h*2)/y;r[15]=0;return r},perspective:function(a,b,
e,f,h){a=e*Math.tan(a*Math.PI/360);b*=a;return kb.Qs(-b,b,-a,a,e,f,h)},Ow:function(a,b,e,f,h,n,r){r||(r=kb.create());let w=b-a,x=f-e,y=n-h;r[0]=2/w;r[1]=0;r[2]=0;r[3]=0;r[4]=0;r[5]=2/x;r[6]=0;r[7]=0;r[8]=0;r[9]=0;r[10]=-2/y;r[11]=0;r[12]=-(a+b)/w;r[13]=-(f+e)/x;r[14]=-(n+h)/y;r[15]=1;return r}},eb=nb(768),Nb=nb.n(eb);class Ob{constructor(){this.ba=this.value=0;this.aj=-1;this.unit="px"}rk(a,b,e){let f=0;a.hasOwnProperty("value")&&(f=a.value,a.hasOwnProperty("unit")||(a.unit="px"),"px"==a.unit||"%"==
a.unit)&&(this.unit!=a.unit&&(this.value="%"==a.unit?this.value/e*100:this.value*e/100),this.unit=a.unit);b&&b.hasOwnProperty("duration")&&0<b.duration||(this.value=f);this.aj=f}ih(a,b=-1){-1==b&&(b=this.value);this.ba="%"==this.unit?.01*b*a:b}vj(a,b){-1!=this.aj&&(this.aj==this.value?this.ih(b):(this.ih(b,this.value+(this.aj-this.value)*a),1<=a&&(this.value=this.aj)))}}class Gb{constructor(){this.fov=this.u=this.pan=0}}class fd{constructor(){this.wo=-1E7;this.Sp=!1}}const Sb={Mw:a=>a,Cw:a=>a*a,Gw:a=>
a*(2-a),zw:a=>.5>a?2*a*a:-1+(4-2*a)*a,yw:a=>a*a*a,Fw:a=>--a*a*a+1,Ho:a=>.5>a?4*a*a*a:(a-1)*(2*a-2)*(2*a-2)+1,Dw:a=>a*a*a*a,Hw:a=>1- --a*a*a*a,Aw:a=>.5>a?8*a*a*a*a:1-8*--a*a*a*a,Ew:a=>a*a*a*a*a,Iw:a=>1+--a*a*a*a*a,Bw:a=>.5>a?16*a*a*a*a*a:1+16*--a*a*a*a*a};class wb{cm(){return""}Ts(){return this.Wc}bv(a){0<a&&(this.Wc=a)}Ff(){return eb&&eb.Matrix4}rh(a){if(""!=a)try{let b;b="object"===typeof a?a:JSON.parse(a);if("object"===typeof b)for(const e in b)if("translations"==e&&this.Ra.hasOwnProperty("translations")){let f=
b[e];for(const h in f)if(this.Ra.translations.hasOwnProperty(h))for(a=0;a<f[h].length-1;a++)""!=f[h][a]&&(this.Ra.translations[h][a]=f[h][a]);else this.Ra.translations[h]=f[h]}else this.Ra[e]=b[e];this.Ra.hasOwnProperty("projectLanguage")&&(this.wi=this.Ra.projectLanguage);this.G("languagechanged",{language:this.Ch})}catch(b){console.error(b)}}Ka(a,b){let e=a;if(""!=e&&"object"===typeof this.Ra&&this.Ra.hasOwnProperty("languages")&&this.Ra.hasOwnProperty("translations")){var f=this.Ra.languages.indexOf(this.Ch);
-1!=f&&this.Ra.translations.hasOwnProperty(a)&&(a=this.Ra.translations[a],Array.isArray(a)&&a.length>f&&""!=a[f]&&(e=a[f]))}if(b&&0<b.length)for(f=b.length-1;0<=f;f--)e=e.replace(new RegExp("%"+String(f+1),"g"),b[f]);return e}hn(a){this.tm=!0;a=a.replace("_","-");if("object"===typeof this.Ra&&this.Ra.hasOwnProperty("languages")&&this.Ra.hasOwnProperty("translations")&&this.wi!=a){let b=-1,e=-1,f="",h=-1,n=-1,r=a.substring(0,2).toUpperCase();for(let w=0;w<this.Ra.languages.length;w++)if(this.Ra.languages[w].toUpperCase()==
a.toUpperCase()){b=w;break}else if(!this.wi.toUpperCase().startsWith(r)&&this.Ra.languages[w].toUpperCase().startsWith(r)){if(""==f||-1!=f.indexOf("-"))e=w,f=this.Ra.languages[w]}else{let x=window.navigator.languages;for(let y=0;y<x.length;y++)!this.wi.toUpperCase().startsWith(r)&&this.Ra.languages[w].toUpperCase().startsWith(x[y].substring(0,2).toUpperCase())&&(-1==n||y<n)&&(h=w,n=y)}a=-1!=b?b:-1!=e?e:h;a=-1!=a?this.Ra.languages[a]:this.wi}this.Ch!=a&&(this.Ch=a,this.T.setAttribute("lang",a),this.G("languagechanged",
{language:a}))}ft(){return this.Ch}yt(){return this.Nm}addListener(a,b){(this.Hh[a]=this.Hh[a]||[]).push(b)}G(a,b){if(a=this.Hh[a]){let e=a.length;for(let f=0;f<e;f++)try{a[f].apply(null,[b])}catch(h){this.Yb(h)}}}removeEventListener(a,b){let e=this.Hh[a];if(e){var f;var h=0;for(f=e.length;h<f;h++)if(e[h]===b){1===f?delete this.Hh[a]:e.splice(h,1);break}}}Wu(){this.Ma=!0}ms(a,b,e,f){if(!this.yb.hasOwnProperty(a)){let h=new fd;this.yb[a]=h;h.type=b;"undefined"!==typeof e&&this.Ze(a,e);"object"===typeof f&&
this.jr(a,f);return!0}return!1}static cp(a){let b=document.cookie.indexOf(a+"="),e="";-1!=b&&(b=b+a.length+1,a=document.cookie.indexOf(";",b),-1==a&&(a=document.cookie.length),e=decodeURIComponent(document.cookie.substring(b,a)));return e}static Av(a,b){0==a.type?a.value="string"===typeof b?b:b.toString():1==a.type?a.value="string"===typeof b?parseFloat(b):"number"===typeof b?b:b?1:0:2==a.type&&(a.value="string"===typeof b?"false"!==b&&""!==b&&"0"!==b:"number"===typeof b?0!=b:!!b)}jr(a,b){if(this.yb.hasOwnProperty(a)&&
"object"===typeof b){let f=this.yb[a];b.hasOwnProperty("cookiePath")&&(f.xo=String(b.cookiePath));b.hasOwnProperty("cookieExpireDays")&&(f.wo=parseFloat(b.cookieExpireDays));if(b.hasOwnProperty("keep")&&(f.sm=!!b.keep,f.sm&&0<document.cookie.length))if(""!=this.$g){if(-1!=document.cookie.indexOf(this.$g+"=")){var e=JSON.parse(wb.cp(this.$g));e.hasOwnProperty(a)&&this.Ze(a,e[a])}}else e="ggvar_"+a,-1!=document.cookie.indexOf(e+"=")&&(e=wb.cp(e),this.Ze(a,e));b.hasOwnProperty("ignoreInState")&&(f.Sp=
b.ignoreInState);b.hasOwnProperty("customProperty")&&"object"===typeof b.customProperty&&(b=b.customProperty,this.Ga.hasOwnProperty(a)?(this.Ga[a].variabletype!=b.variableType||this.Ga[a].propertytype!=b.propertyType)&&console.log("Custom property added as variable has different types than custom property from project."):this.Ga[a]={Rd:b.variableType,yi:b.propertyType,Kl:b.defaultValue});return!0}return!1}tv(a,b){this.$g=a;b&&"object"===typeof b&&(b.hasOwnProperty("cookiePath")&&(this.Fr=String(b.cookiePath)),
b.hasOwnProperty("cookieExpireDays")&&(this.Er=parseFloat(b.cookieExpireDays)))}static fo(a,b,e){-1<=b&&(a+="; max-age="+86400*b);""!=e&&(a+="; path="+e);return a}static fp(a,b){switch(b){case 0:return a;case 1:return a.toString();case 2:return a?"true":"false"}}static Ul(a,b){switch(b){case 0:return a;case 1:return Number(a);case 2:return"true"==a}}Ep(a,b){""==a&&(a="node1");return this.eg.hasOwnProperty(a)&&this.eg[a].hasOwnProperty(b)?this.Ij(wb.Ul(this.eg[a][b],this.Ga[b].Rd),this.Ga[b].Rd):null}fr(a,
b,e){e="string"===typeof e?e:wb.fp(e,this.Ga[b].Rd);this.eg[a][b]!=e&&(this.eg[a][b]=e,this.G("varchanged_"+b,{}))}zf(a,b,e){""==a&&(a="node1");return this.pb.hasOwnProperty(a)&&this.pb[a].hasOwnProperty(b)&&this.pb[a][b].hasOwnProperty(e)?this.Ij(wb.Ul(this.pb[a][b][e],this.Ga[e].Rd),this.Ga[e].Rd):this.Ga.hasOwnProperty(e)?this.Ij(wb.Ul(this.Ga[e].Kl,this.Ga[e].Rd),this.Ga[e].Rd):null}Ei(a,b,e,f){""==a&&(a="node1");f="string"===typeof f?f:wb.fp(f,this.Ga[e].Rd);this.pb.hasOwnProperty(a)||(this.pb[a]=
{});this.pb[a].hasOwnProperty(b)||(this.pb[a][b]={});this.pb[a][b].hasOwnProperty(e)&&this.pb[a][b][e]==f||(this.pb[a][b][e]=f,this.G("varchanged_"+e,{}))}Ws(a){return this.Ga.hasOwnProperty(a)?this.Ga[a].Kl:null}qd(){return"object"}Ze(a,b,e=!0){if(void 0===b)return!1;let f=!1;this.Ga.hasOwnProperty(a)&&(0==this.Ga[a].yi?(this.fr(this.qd(),a,b),f=!0):1==this.Ga[a].yi&&this.O&&(this.Ei(this.qd(),this.O.id,a,b),f=!0));if(this.yb.hasOwnProperty(a)){var h=this.yb[a];if(h.value!=b){wb.Av(h,b);if(h.sm&&
e){if(""!=this.$g){h={};e=Object.keys(this.yb);for(n of e)this.yb[n].sm&&(h[n]=encodeURIComponent(this.yb[n].value.toString()));var n=this.$g+"="+JSON.stringify(h);n=wb.fo(n,this.Er,this.Fr)}else n="ggvar_"+a+"="+encodeURIComponent(b.toString()),n=wb.fo(n,h.wo,h.xo?h.xo:"");document.cookie=n}f||this.G("varchanged_"+a,{value:b})}return!0}return!1}Ij(a,b){return 0==b?this.Ka(a):a}bm(a,b=""){if(this.Ga.hasOwnProperty(a)){if(0==this.Ga[a].yi)return this.Ep(this.qd(),a);if(1==this.Ga[a].yi){if(""!=b||
this.O&&this.O!=this.Va)return this.zf(this.qd(),""!=b?b:this.O.id,a);switch(this.Ga[a].Rd){case 0:return"";case 1:return 0;case 2:return!1}}}if(this.yb.hasOwnProperty(a))return this.Ij(this.yb[a].value,this.yb[a].type)}Rh(a,b){return this.yb.hasOwnProperty(a)?this.yb[a].value:b}static Dh(a){let b="";let e,f,h,n=0;a=a.replace(/[^A-Za-z0-9+\/=]/g,"");do{var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(n++));var w="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(n++));
f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(n++));h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(n++));r=r<<2|w>>4;w=(w&15)<<4|f>>2;e=(f&3)<<6|h;b+=String.fromCharCode(r);64!=f&&(b+=String.fromCharCode(w));64!=h&&(b+=String.fromCharCode(e))}while(n<a.length);return b}static Pv(a){const b=[1,1,1,1,2,2,3,0],e=a.length;let f="";for(let h=0;h<e;){let n=a.charCodeAt(h++);if(n&128){let r=b[n>>3&7];if(!(n&64&&r)||h+r>e)return null;
for(n&=63>>r;0<r;--r){let w=a.charCodeAt(h++);if(128!=(w&192))return null;n=n<<6|w&63}}f+=String.fromCharCode(n)}return f}static Do(a){return wb.Pv(wb.Dh(a))}constructor(){this.Sn="aHR0cHM6Ly9nZ25vbWUuY29t";this.Tn="Q3JlYXRlZCBieSBHbm9tZXM=";this.hu="VHJpYWwgTGljZW5zZTogRm9yIGV2YWx1YXRpb24gb25seSE=";this.lh=!0;this.H={width:10,height:10};this.crossOrigin="anonymous";this.Wc=1;this.Ra={};this.wi=this.Ch="";this.Nm=[];this.tm=!1;this.De=this.Zd=!0;this.Yj=0;this.control=this.xa=this.T=this.ia=null;this.md=[];this.Ma=this.oc=!1;this.Ae=1;this.aa=
null;this.ee=!1;this.ri=0;this.Mf={pan:-1,u:-1,fov:-1};this.ka=null;this.dg={};this.ff={};this.eg={};this.pb={};this.Ga={};this.P={mode:1,count:0,um:-1,Fa:0,kb:0,Bd:.05,ic:255,hc:1,fc:255,ec:.3,Ke:!0,Mk:{enabled:!0,width:180,height:20,Nk:0,Lk:1,background:!0,fc:16777215,ec:1,ic:0,hc:1,nl:3,lj:1,gj:!0},dc:[],Tc:[],wd:[],Ek:[]};this.fe=null;this.L=[];this.Va={};this.Ag=this.yc=!1;this.Oi=-1;this.fa=1;this.container=null;this.Hh={};this.yb={};this.Fr=this.$g="";this.Er=-1;this.margin={left:new Ob,top:new Ob,
right:new Ob,bottom:new Ob,transition:{}};this.fk=[];this.ca={x:0,y:0};this.lm=!1;this.ve="";this.Ks="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAA5JREFUeNpiYBgeACDAAADIAAE3iTbkAAAAAElFTkSuQmCC";this.Tb="";this.tk=this.Qj=this.Zh=this.Xp=this.Wh=this.Yp=this.Wj=this.Tj=this.Lc=this.Vj=this.qm=this.Rj=this.uk=this.Mb=this.Xh=this.Dg=this.om=this.Zp=this.pm=this.aq=!1;this.Me=[];this.devicePixelRatio=1;this.Am=!1;this.dk="<<LOG>>";
this.Kn=!1;this.mt=function(a){return a?this.Yh()&&(a.clientX||a.clientY)?{x:a.clientX,y:a.clientY}:a.pageX||a.pageY?{x:a.pageX,y:a.pageY}:a.clientX||a.clientY?{x:a.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,y:a.clientY+document.body.scrollTop+document.documentElement.scrollTop}:a.touches&&a.touches[0]?{x:a.touches[0].pageX,y:a.touches[0].pageY}:{x:0,y:0}:{x:0,y:0}};this.Af=function(a){a=this.mt(a);let b=this.Cf();return{x:a.x-b.x,y:a.y-b.y}};this.Jv=function(a,b,e){let f=
this;var h="<<L>>"+String(f.Tb);h=h.toUpperCase();"U"!=h.charAt(2)&&(f.K.Kj=!1);if(f.Ed)f.T.removeChild(f.Ed),f.Ed=null;else{f.Ed=document.createElement("div");var n=f.Ed;h="left: "+a+"px;top:\t "+(b+"px;z-index: 32000;");h+="position:relative;";h+="display: table;";h+="color: black;";h+="background-color: white;";h+="border: 1px solid lightgray;";h+="box-shadow: 1px 1px 3px #333;";h+="font-family: Verdana, Arial, Helvetica, sans-serif;";h+="font-size: 9pt;";h+="opacity : 0.95;";n.setAttribute("style",
h);n.setAttribute("class","gg_contextmenu");h=document.createElement("style");a=document.createTextNode(".gg_context_row:hover { background-color: #3399FF }");h.type="text/css";h.styleSheet?h.styleSheet.cssText=a.nodeValue:h.appendChild(a);n.appendChild(h);for(a=0;a<f.fk.length;a++){b=f.fk[a];if(""==b.text)continue;let r=document.createElement("div");h="text-align: left;";h+="margin: 0;";h+="padding: 5px 20px;";h+="vertical-align: left;";r.setAttribute("style",h);r.setAttribute("class","gg_context_row");
""!=b.url?(h=document.createElement("a"),h.href=b.url,h.target="_blank",h.innerHTML=this.Ka(b.text),h.setAttribute("style","color: black; text-decoration: none;"),r.appendChild(h)):(h=document.createElement("span"),h.innerHTML=this.Ka(b.text),h.setAttribute("style","color: black; text-decoration: none;"),r.appendChild(h));n.appendChild(r)}0<f.fk.length&&(0<e.length||!f.K.Kj)&&n.appendChild(document.createElement("hr"));for(h=0;h<e.length;h++)a=e[h],a.hasOwnProperty("hr")?n.appendChild(document.createElement("hr")):
(b=document.createElement("div"),b.setAttribute("class","gg_context_row"),a.hasOwnProperty("style")&&b.setAttribute("style",a.style),a.hasOwnProperty("onclick")&&(b.onclick=a.onclick),b.innerHTML=a.innerHTML,n.appendChild(b));f.K.Kj||(e=document.createElement("div"),h="text-align: left;margin: 0;padding: 5px 20px;",h+="vertical-align: left;",e.setAttribute("style",h),e.setAttribute("class","gg_context_row"),h=document.createElement("a"),h.href=wb.Dh(f.Sn),h.target="_blank",h.innerHTML=wb.Dh(f.Tn),
7<this.dk.length&&(h.innerHTML+="<br/>"+wb.Do(f.dk).replace(/./gm,function(r){return"&#"+r.charCodeAt(0)+";"})),h.setAttribute("style","color: black; text-decoration: none;"),e.appendChild(h),n.appendChild(e));f.T.insertBefore(f.Ed,f.T.firstChild);n.onclick=function(){f.Ed&&(f.T.removeChild(f.Ed),f.Ed=null)};n.oncontextmenu=n.onclick}};this.pn=0;this.zi="";this.xc=!1;this.Fo();this.checkLoaded=this.md;this.isLoaded=!1}Fo(){this.devicePixelRatio=window.devicePixelRatio||1;this.aq=!!navigator.userAgent.match(/(Windows|Win)/g);
this.pm=!!navigator.userAgent.match(/(Mac|Macintosh|Mac_powerpc)/g)&&!navigator.userAgent.match(/(like Mac)/g);this.Zp=!!navigator.userAgent.match(/(Linux|Ubuntu)/g);this.om=!!navigator.userAgent.match(/(MSIE)/g);this.Dg=!!navigator.userAgent.match(/(Edge|EdgA|Edg)/g);this.Xh=!!navigator.userAgent.match(/(Firefox)/g);if(this.Mb=!!navigator.userAgent.match(/(Safari)/g)){var a=navigator.userAgent.indexOf("Safari");this.Ob=navigator.userAgent.substring(a+7);a=navigator.userAgent.indexOf("Version");-1!=
a&&(this.Ob=navigator.userAgent.substring(a+8));this.Ob=this.Ob.substring(0,this.Ob.indexOf(" "));this.Ob=this.Ob.substring(0,this.Ob.indexOf("."));this.uk=!0}if(this.Rj=!!navigator.userAgent.match(/(Chrome|CriOS)/g))this.Mb=!1;this.qm=!!navigator.userAgent.match(/(SamsungBrowser)/g);if(this.Vj=!!navigator.userAgent.match(/(OculusBrowser)/g))this.Mb=this.Rj=this.qm=!1;this.Lc=!!navigator.userAgent.match(/(iPad|iPhone|iPod)/g);this.Wj=(this.Tj=!!navigator.userAgent.match(/(iPad)/g)||this.pm&&2<navigator.maxTouchPoints)&&
void 0!==navigator.xr&&void 0!==navigator.xr.requestSession;this.Yp=!!navigator.userAgent.match(/(iPhone|iPod)/g);this.Wh=!!navigator.userAgent.match(/(android)/i);this.Xp=!!navigator.userAgent.match(/(IEMobile)/i);this.Zh=this.Lc||this.Wh||this.Xp||this.Tj;/iP(hone|od|ad)/.test(navigator.platform)&&(a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),this.Me=[parseInt(a[1],10),parseInt(a[2],10),parseInt(a[3]||"0",10)]);this.Qj=!window.requestAnimationFrame;this.Tb="";a=["Webkit","Moz","O","ms",
"Ms"];for(let b=0;b<a.length;b++)"undefined"!==typeof document.documentElement.style[a[b]+"Transform"]&&(this.Tb="-"+a[b].toLowerCase()+"-")}Ev(){try{window.AudioContext=window.AudioContext||window.webkitAudioContext,this.ga=new AudioContext}catch(a){this.ga=null}this.Mb&&(!this.uk||9>Number(this.Ob))&&(this.ga=null);this.Mb&&!this.Lc&&12<=Number(this.Ob)&&13>Number(this.Ob)&&(this.ga=null);this.Mb&&this.Lc&&13==Number(this.Me[0])&&(1==Number(this.Me[1])||2<=Number(this.Me[1]))&&(this.ga=null)}Yb(a){let b=
document.getElementById("gg_debug");b&&(b.innerHTML=a+"<br />");window.console&&window.console.log(a)}setCrossOrigin(a){this.crossOrigin=a}dv(a){this.Le=a}vt(){return this.ri}cv(a){this.ve=a}bp(){return this.ve}Yl(){return this.Zh}hp(){return this.lm}gv(a){this.Zh=!!a}Oh(){return this.isLoaded}kp(){return!this.isLoaded}Li(a,b){try{this.oc&&(a=window.innerWidth,b=window.innerHeight);let h=a-this.margin.left.ba-this.margin.right.ba,n=b-this.margin.top.ba-this.margin.bottom.ba;if(!(10>h||10>n)){this.ia.style.width=
h+"px";this.ia.style.height=n+"px";this.ia.style.left=this.margin.left.ba+"px";this.ia.style.top=this.margin.top.ba+"px";this.xa&&(this.xa.style.width=a+"px",this.xa.style.height=b+"px",this.aa&&this.aa!=this.xa&&(this.aa.style.width=a+"px",this.aa.style.height=b+"px"));this.ee&&(this.Ma=!0);var e=this.ia.offsetWidth,f=this.ia.offsetHeight;if(this.H.width!=e||this.H.height!=f)this.H.width=e,this.H.height=f;this.aa&&this.aa.ggUpdateSize&&this.aa.ggUpdateSize(a,b);this.G("sizechanged",{w:a,h:b});this.G("playerstatechanged",
{})}}catch(h){}}qe(){this.Kn=!0}fd(){this.Tr();this.Li(this.container.offsetWidth,this.container.offsetHeight)}Tr(){this.margin.left.ih(this.container.offsetWidth);this.margin.top.ih(this.container.offsetHeight);this.margin.right.ih(this.container.offsetWidth);this.margin.bottom.ih(this.container.offsetHeight)}Lt(a=!1){let b={width:0,height:0};b.width=this.H.width+(a?this.margin.left.ba+this.margin.right.ba:0);b.height=this.H.height+(a?this.margin.top.ba+this.margin.bottom.ba:0);b.margins={left:this.margin.left,
top:this.margin.top,right:this.margin.right,bottom:this.margin.bottom};return b}Cf(){let a={x:0,y:0},b=this.ia.getBoundingClientRect();a.x=b.left;a.y=b.top;this.Mb&&14>Number(this.Ob)&&(a.x=b.left+window.scrollX,a.y=b.top+window.scrollY);return a}sv(a){this.ka=a}hv(a,b,e,f){let h=!0;"object"===typeof a?(a.hasOwnProperty("left")&&this.margin.left.rk(a.left,a.transition,this.container.offsetWidth),a.hasOwnProperty("top")&&this.margin.top.rk(a.top,a.transition,this.container.offsetHeight),a.hasOwnProperty("right")&&
this.margin.right.rk(a.right,a.transition,this.container.offsetWidth),a.hasOwnProperty("bottom")&&this.margin.bottom.rk(a.bottom,a.transition,this.container.offsetHeight),a.hasOwnProperty("transition")&&a.transition.hasOwnProperty("duration")&&0<a.transition.duration&&(h=!1,this.margin.transition.duration=a.transition.duration,a.transition.hasOwnProperty("timingfunction")?this.margin.transition.timingfunction=a.transition.timingfunction:this.margin.transition.timingfunction="ease")):(this.margin.left.value=
a,this.margin.top.value=b,this.margin.right.value=e,this.margin.bottom.value=f);this.ka=this.skinObj;h?this.fd():(b=0,a.transition.hasOwnProperty("delay")&&(b=a.transition.delay),setTimeout(()=>{this.Zv(this)},b))}Zv(a){a.margin.transition.starttime=(new Date).getTime();a.margin.transition.interval&&clearInterval(a.margin.transition.interval);a.margin.transition.interval=setInterval(()=>{a.$v(a)},20)}$v(a){var b=((new Date).getTime()-a.margin.transition.starttime)/a.margin.transition.duration;b=Math.max(b,
0);b=Math.min(b,1);switch(a.margin.transition.timingfunction){case "ease-in":b=1-Math.cos(b*Math.PI/2);break;case "ease-out":b=Math.sin(b*Math.PI/2);break;case "ease":b=-(Math.cos(Math.PI*b)-1)/2;break;case "ease-in-out":b=.5>b?4*b*b*b:1-Math.pow(-2*b+2,3)/2;break;case "step-end":b=1==b?1:0}b=Math.round(100*b)/100;a.margin.left.vj(b,a.container.offsetWidth);a.margin.top.vj(b,a.container.offsetHeight);a.margin.right.vj(b,a.container.offsetWidth);a.margin.bottom.vj(b,a.container.offsetHeight);a.Li(a.container.offsetWidth,
a.container.offsetHeight);1<=b&&(clearInterval(a.margin.transition.interval),a.margin.transition.interval=0)}po(a){0==a&&(this.K.mc=!1);1==a&&(this.K.mc=!0);2==a&&(this.K.mc=!this.K.mc);this.G("viewmodechanged",{});this.G("playerstatechanged",{})}Gp(){return 1==this.K.mc?1:0}ii(){this.Ma=!0}oo(a,b){this.P.mode=1==b&&0<this.P.mode?0:Math.round(a);-1==this.P.mode&&(this.P.dc=[]);this.ii();this.G("polymodechanged",{})}Ur(a){let b=this.Cf();this.Yh()?(this.ca.x=a.clientX-this.margin.left.ba,this.ca.y=
a.clientY-this.margin.top.ba):(this.ca.x=a.clientX-b.x,this.ca.y=a.clientY-b.y)}Vr(a){a=a.touches;let b=this.Cf();this.ca.x=a[0].clientX-b.x;this.ca.y=a[0].clientY-b.y}Ih(a){let b=[];a="*"==a?"^.*$":"#"==a.substring(0,1)?a.substring(1):"^"+a+"$";a=new RegExp(a,"");for(var e=0;e<this.L.length;e++)"poly"==this.L[e].type&&a.test(this.L[e].id)&&b.push(this.L[e]);return b}mr(a){a=this.Ih(a);for(let b=0;b<a.length;b++){let e=this.P.dc.indexOf(a[b].id);-1==e?(this.P.dc.push(a[b].id),this.P.Tc.push(0),this.P.wd.push(1)):
this.P.wd[e]=1}this.ii()}Pp(a){a=this.Ih(a);for(let b=0;b<a.length;b++){let e=this.P.dc.indexOf(a[b].id);-1!=e&&(this.P.wd[e]=0,this.P.Ek.push(a[b].id),this.update())}this.ii()}Uv(a){a=this.Ih(a);for(let b=0;b<a.length;b++){let e=this.P.dc.indexOf(a[b].id);-1==e||-1!=e&&0==this.P.wd[e]?this.mr(a[b].id):this.Pp(a[b].id)}this.ii()}ys(a,b,e,f,h){let n;n=""==a?this.L:this.Ih(a);for(let r=0;r<n.length;r++){let w=n[r];"poly"==w.type&&(w.fc=b,w.ec=e,w.ic=f,w.hc=h)}""==a&&(this.P.fc=b,this.P.ec=e,this.P.ic=
f,this.P.hc=h);this.ii()}lv(a,b){let e;e=""==a?this.L:this.Ih(a);for(let f=0;f<e.length;f++){let h=e[f];"poly"==h.type&&(h.Ke=b)}""==a&&(this.P.Ke=b);this.update()}xp(){return this.P.mode}qo(){this.G("viewstatechanged",{})}Hp(){return 0}Nq(a,b,e){var f=(new RegExp("%0*"+b,"i")).exec(a.toString());if(f){f=f.toString();let h=e.toString();f.charAt(f.length-1)!=b&&(h=(1+e).toString());if(!isNaN(parseFloat(e)))for(;h.length<f.length-1;)h="0"+h;a=a.replace(f,h)}return a}Sk(){this.G("hotspotsupdated",{})}ep(){let a=
[];for(let b=0;b<this.L.length;b++){let e=this.L[b];"point"==e.type&&e.obj&&e.obj.__div&&a.push(e.obj.__div)}return a}Gb(a,b){a=Number(a);isNaN(b)&&(b=0);0>b&&(b=0);1<b&&(b=1);return"rgba("+(a>>16&255)+","+(a>>8&255)+","+(a&255)+","+b+")"}static Qp(a,b,e){let f;var h;let n=!1;f=0;for(h=a.length-1;f<a.length;h=f++){let r=a[f];h=a[h];r.ac>e!=h.ac>e&&b<(h.Ac-r.Ac)*(e-r.ac)/(h.ac-r.ac)+r.Ac&&(n=!n)}return n}aspect(){return this.H.width/this.H.height}Xd(){}update(a=0){this.Ma=!0;a&&(this.Ae=Math.max(1*
a,this.Ae))}Zl(){return!1}mv(a){this.zi=a}getQueryParameter(){return this.zi}Ha(a){if(a){if("{"==a.charAt(0)||"/"==a.charAt(0)||0<a.indexOf(":"))return a;""!=this.zi&&(a=-1==a.indexOf("?")?a+("?"+this.zi):a+("&"+this.zi));return this.ve+a}return this.ve}be(){return 0}vp(){return 0}Nd(){}wh(){}Bf(){return 0}Fp(){return 0}Od(){}xh(){}rd(){return 0}Jb(){return!1}yk(){}vh(){return!1}mo(){}xk(){}setLocked(a){this.ar(a);this.cr(a);this.$q(a)}ar(a){this.K.qc=!!a}$q(a){this.K.fi=!!a}it(){return this.K.fi}cr(a){this.K.Qe=
!!a}Ip(){return this.K.re}kr(a){this.K.re=a}zk(a){this.xc=!!a}ut(){return this.Wh?5:this.Wj?7:this.Tj?6:this.Lc?4:this.aq?1:this.pm?2:this.Zp?3:0}Us(){return this.om?5:this.Dg?4:this.Xh?2:this.Mb?3:this.Rj?1:this.Vj?6:0}Vs(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?1:0}moveTo(){this.ra(!1)}mi(){this.ra(!1)}nq(){}oq(){}Wn(){}Rr(a,b,e){for(let f=0;f<this.L.length;f++){let h=this.L[f];h.id==a&&(h.pan=b,h.tilt=e,h.xf())}this.Ma=!0}Lq(a){let b=-1,e;for(let f=
0;f<this.L.length;f++)e=this.L[f],e.id==a&&(b=f);-1<b&&(e=this.L.splice(b,1).pop(),e.j&&e.j.__div&&this.xa.removeChild(e.j.__div))}xt(){let a=[];for(let b=0;b<this.L.length;b++){let e=this.L[b];"point"==e.type&&a.push(String(e.id))}return a}ip(){}gh(a,b,e){const f=this.P.Mk,h=this.fe;f.enabled&&(this.O!=this.Va&&0<=a&&0<=b&&""!=this.O.title?(h.innerHTML=this.Ka(this.O.title),h.style.color=this.Gb(f.Nk,f.Lk),h.style.backgroundColor=f.background?this.Gb(f.fc,f.ec):"transparent",h.style.border="solid "+
this.Gb(f.ic,f.hc)+" "+f.lj+"px",h.style.borderRadius=f.nl+"px",h.style.textAlign="center",0<f.width?(h.style.left=a-f.width/2+this.margin.left.ba+"px",h.style.width=f.width+"px"):(h.style.width="auto",h.style.left=a-h.offsetWidth/2+this.margin.left.ba+"px"),h.style.height=0<f.height?f.height+"px":"auto",h.style.top=b+(e?-60:25)+this.margin.top.ba+"px",h.style.visibility="inherit",h.style.whiteSpace=f.gj?"pre-wrap":"nowrap",h.style.overflow="hidden"):(h.style.visibility="hidden",h.innerHTML=""))}Lr(a){this.ka&&
this.ka.hotspotProxyClick&&this.ka.hotspotProxyClick(a.id,a.url);this.G("hsproxyclick",{id:this.O.id,url:this.O.url});""!=a.url&&this.kk(a.url,a.target);this.gh(-1,-1,!1)}Mr(a){this.ka&&this.ka.hotspotProxyDoubleClick&&this.ka.hotspotProxyDoubleClick(a.id,a.url);this.G("hsproxydblclick",{id:this.O.id,url:this.O.url})}ra(){this.Jf=(new Date).getTime()}gt(){return this.Jf}Yh(){return document.webkitIsFullScreen||document.mozFullScreen||document.msFullscreenElement&&null!=document.msFullscreenElement||
document.fullScreen}rm(){}al(){let a,b=document.createElement("fakeelement"),e={OTransition:"oTransitionEnd",MSTransition:"msTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd",transition:"transitionEnd"};for(a in e)if(void 0!==b.style[a])return e[a]}pp(){return null}op(){return null}isPlaying(){return!1}Md(){}Jm(){}Fq(){}Jg(){}Un(){}Ti(){}sr(){return 0}ur(){}tr(){}setVolume(){}ro(){}Ck(){}ru(a){this.Ck(a,1)}bw(a){this.Ck(a,0)}Tv(a){this.Ck(a,-1)}wt(){return this.yc}$s(){return this.Ag}Xt(){this.ho=
document.createElement("audio");this.ho.src=this.Ha("media/testsound.mp3")}wr(){let a=this;this.ga&&"suspended"===this.ga.state&&this.ga.resume();let b=a.ho.play();void 0!==b&&b.then(()=>{a.Ug(!0)}).catch(()=>{a.Ug(!1)})}yr(){this.Ag&&1!=this.Oi?this.wr():this.Zd=this.De=!1}At(){return this.Oi}Ug(a){if(1!=this.Oi){let b=a?1:0;this.Oi!=b&&(this.Oi=b,this.G("soundspermittedchanged",{permitted:a}))}}zr(){}Bk(){}rn(){}Ym(){}Xl(){return!0}Vg(){}Di(a){let b=this.oc!==a;this.oc!==a&&(this.oc=a,this.update(100));
if(this.oc){if(this.lh)try{this.T.webkitRequestFullScreen?this.T.webkitRequestFullScreen():this.T.mozRequestFullScreen?this.T.mozRequestFullScreen():this.T.msRequestFullscreen?this.T.msRequestFullscreen():this.T.requestFullScreen?this.T.requestFullScreen():this.T.requestFullscreen&&this.T.requestFullscreen()}catch(e){}this.T.style.position="absolute";a=this.Cf();this.T.style.left=window.pageXOffset-a.x+this.margin.left.ba+"px";this.T.style.top=window.pageYOffset-a.y+this.margin.top.ba+"px";this.Vg(10);
document.body.style.overflow="hidden";b&&(this.aa&&this.aa.ggEnterFullscreen&&this.aa.ggEnterFullscreen(),this.G("fullscreenenter",{}),this.G("playerstatechanged",{}))}else{if(this.lh)try{document.webkitIsFullScreen?document.webkitCancelFullScreen():document.mozFullScreen?document.mozCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():document.fullScreen&&(document.cancelFullScreen?document.cancelFullScreen():document.exitFullscreen&&document.exitFullscreen())}catch(e){}this.T.style.position=
"relative";this.T.style.left="0px";this.T.style.top="0px";this.Vg(0);document.body.style.overflow="";b&&(this.aa&&this.aa.ggExitFullscreen&&this.aa.ggExitFullscreen(),this.G("fullscreenexit",{}),this.G("playerstatechanged",{}))}this.qe()}Pk(){this.Di(!this.oc)}Ls(){this.Di(!0)}exitFullscreen(){this.Di(!1)}dt(){return this.oc}oi(){this.oc&&(this.Yh()||this.exitFullscreen(),this.Yh()&&(this.T.style.left="0px",this.T.style.top="0px"))}Ne(){return!1}Bg(){return!1}ir(){return!1}Hj(){return!1}Ar(){}Dr(){}Ir(){}Dq(){this.ra(!1)}Qq(){}Vq(){}Xq(){}Cl(){}Cp(){let a=
1,b=-1!=navigator.userAgent.indexOf("Mac");window.devicePixelRatio&&b&&(a=window.devicePixelRatio);return{w:screen.width*a,h:screen.height*a}}np(){let a=this.Cp();return a.w>a.h?a.w:a.h}Jo(a){try{let b=a.getAttributeNode("variablename"),e=b.nodeValue.toString();b=a.getAttributeNode("variabletype");let f=Number(b.nodeValue);b=a.getAttributeNode("propertytype");let h=Number(b.nodeValue);b=a.getAttributeNode("defaultvalue");let n;b&&(n=b.nodeValue.toString());this.Ga[e]={Rd:f,yi:h,Kl:n}}catch(b){console.error(b)}}Mo(a){let b=
{},e=a.firstChild;for(;e;){if("custompropertyvalue"==e.nodeName){a=e.getAttributeNode("value");let f=a.nodeValue.toString();a=e.getAttributeNode("variablename");b[a.nodeValue.toString()]=f}e=e.nextSibling}return b}Nl(a,b){let e,f="";(e=b.getAttributeNode("id"))&&(f=e.nodeValue.toString());if(!this.pb.hasOwnProperty(a)||!this.pb[a].hasOwnProperty(f)){var h={};for(b=b.firstChild;b;){if("custompropertyvalue"==b.nodeName){e=b.getAttributeNode("variablename");let n=e.nodeValue;e=b.getAttributeNode("value");
h[n]=e.nodeValue}b=b.nextSibling}this.pb[a]||(this.pb[a]={});this.pb[a][f]=h}}Rm(a,b){a=(new DOMParser).parseFromString(a,"text/xml");this.Sm(a,b)}Iq(a,b,e){try{let f;f=new XMLHttpRequest;f.open("GET",a,!1);f.send(null);if(f.responseXML){let h=a.lastIndexOf("/");0<=h&&(this.ve=a.slice(0,h+1));2<=arguments.length&&null!=b&&(this.ve=b);this.Rm(f.responseText,e)}else alert("Error loading panorama XML")}catch(f){alert("Error:"+f)}}Uu(a,b,e,f){let h;h=new XMLHttpRequest;let n=this;const r=arguments;h.onload=
function(){if(4<=h.readyState)if(h.responseXML){let w=a.lastIndexOf("/");0<=w&&(n.ve=a.slice(0,w+1));3<=r.length&&null!=e&&(n.ve=e);n.Rm(h.responseText,f);b&&b()}else alert("Error loading panorama XML");else console.error("Wrong state loading XML:"+h.statusText)};h.onerror=function(){console.error("Error loading XML:"+h.statusText)};h.open("GET",a,!0);h.send(null)}Sm(){}kk(a,b){0<a.length&&".xml"!=a.slice(-4)&&".swf"!=a.slice(-4)&&"{"!=a.charAt(0)&&window.open(this.Ha(a),b)}Go(){this.Am=!0}wp(){return{}}er(){return!0}}
class gd{constructor(a){this.Xg=new Gb;this.qg=new Gb;this.Fn=this.Rk=null;this.Gn=this.offset=0;this.Za=kb.create();this.il=1;this.o=a;this.enabled=!1;this.tf=1;this.hg=0;this.type="crossdissolve";this.Vc=this.ea=this.dd=0;this.fl=20;this.cl=90;this.el=20;this.qh=1;this.ph=!1;this.An=this.zn=this.un=0;this.Ui=70;this.hs=0;this.Lb=this.gs=1;this.oh=this.nh=.5;this.Ud=this.ue=!1;this.Gl=1}Vh(){let a=this.o.N,b=a.createShader(a.VERTEX_SHADER);a.shaderSource(b,"attribute vec3 aVertexPosition;\nattribute vec2 aTextureCoord;\nvarying vec2 vTextureCoord;\nuniform bool uZoomIn;\nuniform float uZoomFactor;\nuniform vec2 uZoomCenter;\nvoid main(void) {\n\t gl_Position = vec4(aVertexPosition, 1.0);\n\t if(!uZoomIn) {\n\t \n\t   vTextureCoord = aTextureCoord;\n\t }\n\t else {\n\t   vTextureCoord = (aTextureCoord - vec2(0.5, 0.5)) * (1.0/uZoomFactor) + uZoomCenter;\n\t }\n}\n");
a.compileShader(b);a.getShaderParameter(b,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(b)),b=null);var e=a.createShader(a.FRAGMENT_SHADER);a.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform float uAlpha;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, uAlpha);\n}\n");
a.compileShader(e);a.getShaderParameter(e,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(e)),e=null);this.wa=a.createProgram();a.attachShader(this.wa,b);a.attachShader(this.wa,e);a.linkProgram(this.wa);a.getProgramParameter(this.wa,a.LINK_STATUS)||alert("Could not initialise shaders");this.wa.la=a.getAttribLocation(this.wa,"aVertexPosition");a.enableVertexAttribArray(this.wa.la);this.wa.Ea=a.getAttribLocation(this.wa,"aTextureCoord");a.enableVertexAttribArray(this.wa.Ea);e=a.createShader(a.FRAGMENT_SHADER);
a.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform float uColorPercent;\nuniform float uAlpha;\nuniform vec3 uDipColor;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n gl_FragColor = vec4(textureColor.x * (1.0 - uColorPercent) + uDipColor.x * uColorPercent, textureColor.y * (1.0 - uColorPercent) + uDipColor.y * uColorPercent, textureColor.z * (1.0 - uColorPercent) + uDipColor.z * uColorPercent, uAlpha);\n}\n");
a.compileShader(e);a.getShaderParameter(e,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(e)),e=null);this.jb=a.createProgram();a.attachShader(this.jb,b);a.attachShader(this.jb,e);a.linkProgram(this.jb);a.getProgramParameter(this.jb,a.LINK_STATUS)||alert("Could not initialise shaders");this.jb.la=a.getAttribLocation(this.jb,"aVertexPosition");a.enableVertexAttribArray(this.jb.la);this.jb.Ea=a.getAttribLocation(this.jb,"aTextureCoord");a.enableVertexAttribArray(this.jb.Ea);e=a.createShader(a.FRAGMENT_SHADER);
a.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform bool uRound;\nuniform float uRadius;\nuniform vec2 uRectDim;\nuniform vec2 uIrisCenter;\nuniform float uSoftEdge;\nuniform sampler2D uSampler;\nvoid main(void) {\n float alpha = 0.0;\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n if (uRound) {\n\t  vec2 diff = uIrisCenter - gl_FragCoord.xy;\n\t   float distFromCenter = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n\t   if (distFromCenter > uRadius) {\n      alpha = 1.0;\n    } else {\n      alpha = 1.0 - ((uRadius - distFromCenter) / uSoftEdge);\n    };\n }\n else {\n    float alphaFromLeft = 1.0 - ((gl_FragCoord.x -(uIrisCenter.x - uRectDim.x)) / uSoftEdge);\n    float alphaFromRight = 1.0 - (((uIrisCenter.x + uRectDim.x) - gl_FragCoord.x) / uSoftEdge);\n    float alphaFromTop = 1.0 - ((gl_FragCoord.y -(uIrisCenter.y - uRectDim.y)) / uSoftEdge);\n    float alphaFromBottom = 1.0 - (((uIrisCenter.y + uRectDim.y) - gl_FragCoord.y) / uSoftEdge);\n    alpha = max(max(alphaFromLeft, alphaFromRight), max(alphaFromTop, alphaFromBottom));\n }\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, alpha);\n}\n");
a.compileShader(e);a.getShaderParameter(e,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(e)),e=null);this.Ta=a.createProgram();a.attachShader(this.Ta,b);a.attachShader(this.Ta,e);a.linkProgram(this.Ta);a.getProgramParameter(this.Ta,a.LINK_STATUS)||alert("Could not initialise shaders");this.Ta.la=a.getAttribLocation(this.Ta,"aVertexPosition");a.enableVertexAttribArray(this.Ta.la);this.Ta.Ea=a.getAttribLocation(this.Ta,"aTextureCoord");a.enableVertexAttribArray(this.Ta.Ea);e=a.createShader(a.FRAGMENT_SHADER);
a.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\nuniform float uPercent;\nuniform int uDirection;\nuniform vec2 uCanvasDimensions;\nuniform float uSoftEdge;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec4 textureColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n float alpha = 0.0;\n if (uDirection == 1) {\n\t if (gl_FragCoord.x > uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((uPercent - gl_FragCoord.x) / uSoftEdge);\n  }\n }\n if (uDirection == 2) {\n\t if (gl_FragCoord.x < uCanvasDimensions.x - uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((gl_FragCoord.x - (uCanvasDimensions.x - uPercent)) / uSoftEdge);\n  }\n }\n if (uDirection == 3) {\n\t if (gl_FragCoord.y < uCanvasDimensions.y - uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((gl_FragCoord.y - (uCanvasDimensions.y - uPercent)) / uSoftEdge);\n  }\n }\n if (uDirection == 4) {\n\t if (gl_FragCoord.y > uPercent) {\n    alpha = 1.0; \n  } else {\n    alpha = 1.0 - ((uPercent - gl_FragCoord.y) / uSoftEdge);\n  }\n }\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, alpha);\n}\n");
a.compileShader(e);a.getShaderParameter(e,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(e)),e=null);this.cb=a.createProgram();a.attachShader(this.cb,b);a.attachShader(this.cb,e);a.linkProgram(this.cb);a.getProgramParameter(this.cb,a.LINK_STATUS)||alert("Could not initialise shaders");this.cb.la=a.getAttribLocation(this.cb,"aVertexPosition");a.enableVertexAttribArray(this.cb.la);this.cb.Ea=a.getAttribLocation(this.cb,"aTextureCoord");a.enableVertexAttribArray(this.cb.Ea);this.Cs()}Cs(){const a=this.o;
let b=a.N;const e=b.createShader(b.VERTEX_SHADER);a.Jc(e,"precision highp float;\n#define M_PI 3.14159265358979323846\nattribute vec4 aVertexPosition;\nvarying vec2 dst;\nuniform mat4 matRotate; // = mat4( 1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0 );\nuniform mat4 matProject;\nvarying vec3 srcTexDir;\nvarying vec3 dstTexDir;\nuniform float transitionBlend;\nuniform float alphaBlend;\nuniform vec2 rotOffset;\nvoid main(void) {\nfloat srcPan=(aVertexPosition.x) * 2.0 * M_PI;float srcTilt=-(aVertexPosition.y - 0.5) * M_PI; vec3 srcV = vec3(-sin(srcPan) * cos (srcTilt), sin(srcTilt),-cos(srcPan) * cos(srcTilt));float dstPan=(aVertexPosition.z) * 2.0 * M_PI;float dstTilt=-(aVertexPosition.a - 0.5) * M_PI; vec3 dstV = vec3(-sin(dstPan) * cos (dstTilt), sin(dstTilt),-cos(dstPan) * cos(dstTilt));srcTexDir=srcV;srcTexDir.z=-srcV.z;vec3 tmp=srcV;srcV.x= cos(rotOffset.x)*tmp.x - sin(rotOffset.x)*tmp.z;srcV.y= tmp.y;srcV.z= sin(rotOffset.x)*tmp.x + cos(rotOffset.x)*tmp.z;dstTexDir=dstV;dstTexDir.z=-dstV.z;tmp=dstV;dstV.x= cos(rotOffset.y)*tmp.x - sin(rotOffset.y)*tmp.z;dstV.y= tmp.y;dstV.z= sin(rotOffset.y)*tmp.x + cos(rotOffset.y)*tmp.z; gl_Position =   matProject * matRotate *  vec4(mix(srcV, dstV, transitionBlend), 1.0) ;\ngl_Position.y=-gl_Position.y;}\n");
const f=b.createShader(b.FRAGMENT_SHADER);a.Jc(f,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec3 srcTexDir;\nvarying vec3 dstTexDir;\nuniform float transitionBlend;\nuniform samplerCube cubeTexture;uniform samplerCube cubeTexture2;uniform float alphaBlend;\nuniform vec2 irisRamp;\nuniform vec2 uCanvasDimensions;\nvoid main()\n{\nvec4 c1 = textureCube(cubeTexture, srcTexDir);vec4 c2 = textureCube(cubeTexture2, dstTexDir);vec2 fromCenter=(2.0 * vec2(gl_FragCoord.x / uCanvasDimensions.x,gl_FragCoord.y / uCanvasDimensions.y)) - vec2(1.0,1.0);gl_FragColor=mix(c1,c2,clamp(mix((irisRamp.x + 1.0)*transitionBlend - irisRamp.x,(irisRamp.y + 1.0)*transitionBlend - irisRamp.y,length(fromCenter)),0.0, 1.0));gl_FragColor.a=alphaBlend;}\n");
this.Kq=b.createProgram();a.Nf(this.Kq,e,f)}If(){let a=this.o.N;if(!a)return!1;if(this.Pb=a.createFramebuffer()){a.bindFramebuffer(a.FRAMEBUFFER,this.Pb);this.Pb.width=1024;this.Pb.height=1024;this.$f=a.createTexture();a.bindTexture(a.TEXTURE_2D,this.$f);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MIN_FILTER,a.LINEAR);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MAG_FILTER,a.LINEAR);a.texImage2D(a.TEXTURE_2D,0,a.RGBA,this.Pb.width,this.Pb.height,0,a.RGBA,a.UNSIGNED_BYTE,null);let b=a.createRenderbuffer();a.bindRenderbuffer(a.RENDERBUFFER,
b);a.renderbufferStorage(a.RENDERBUFFER,a.DEPTH_COMPONENT16,this.Pb.width,this.Pb.height);a.renderbufferStorage(a.RENDERBUFFER,a.STENCIL_INDEX8,this.Pb.width,this.Pb.height);a.framebufferTexture2D(a.FRAMEBUFFER,a.COLOR_ATTACHMENT0,a.TEXTURE_2D,this.$f,0);a.framebufferRenderbuffer(a.FRAMEBUFFER,a.STENCIL_ATTACHMENT,a.RENDERBUFFER,b);a.bindTexture(a.TEXTURE_2D,null);a.bindRenderbuffer(a.RENDERBUFFER,null);a.bindFramebuffer(a.FRAMEBUFFER,null);this.vb=a.createBuffer();a.bindBuffer(a.ARRAY_BUFFER,this.vb);
a.bufferData(a.ARRAY_BUFFER,new Float32Array([-1,-1,0,1,-1,0,-1,1,0,1,1,0]),a.STATIC_DRAW);this.vb.itemSize=3;this.vb.numberOfItems=4;this.Og=a.createBuffer();a.bindBuffer(a.ARRAY_BUFFER,this.Og);a.bufferData(a.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,1,1]),a.STATIC_DRAW);return!0}return!1}Fm(a){let b=this.o.N;var e=this.o.Xa;const f=this.o.D;if(this.Qd){b.useProgram(this.wa);b.bindBuffer(b.ARRAY_BUFFER,this.vb);b.vertexAttribPointer(this.wa.la,this.vb.itemSize,b.FLOAT,!1,0,0);b.bindBuffer(b.ARRAY_BUFFER,
this.Og);b.vertexAttribPointer(this.wa.Ea,2,b.FLOAT,!1,0,0);b.enableVertexAttribArray(this.wa.la);b.enableVertexAttribArray(this.wa.Ea);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_2D,this.$f);e=1+(this.Lb-1)*a;b.uniform1f(b.getUniformLocation(this.wa,"uAlpha"),1);b.uniform1i(b.getUniformLocation(this.wa,"uZoomIn"),1);var h=b.getUniformLocation(this.wa,"uZoomCenter"),n=.5+(this.nh-.5)*Math.sqrt(a);a=.5+(this.oh-.5)*Math.sqrt(a);0>n-.5/e&&(n=.5/e);0>a-.5/e&&(a=.5/e);1<n+.5/e&&(n=1-.5/e);1<a+
.5/e&&(a=1-.5/e);b.uniform2f(h,n,a);b.uniform1f(b.getUniformLocation(this.wa,"uZoomFactor"),e);b.uniform1i(b.getUniformLocation(this.wa,"uSampler"),0);b.drawArrays(b.TRIANGLE_STRIP,0,this.vb.numberOfItems);b.useProgram(f.Ig.R)}else{this.o.bj();b.blendFuncSeparate(b.SRC_ALPHA,b.ONE_MINUS_SRC_ALPHA,b.SRC_ALPHA,b.ONE);b.enable(b.BLEND);b.disable(b.DEPTH_TEST);h=.5+(this.nh-.5);n=.5+(this.oh-.5);0>h-.5/this.Lb&&(h=.5/this.Lb);0>n-.5/this.Lb&&(n=.5/this.Lb);1<h+.5/this.Lb&&(h=1-.5/this.Lb);1<n+.5/this.Lb&&
(n=1-.5/this.Lb);b.enableVertexAttribArray(this.wa.la);b.enableVertexAttribArray(this.wa.Ea);if("crossdissolve"==this.type)b.useProgram(this.wa),b.bindBuffer(b.ARRAY_BUFFER,this.vb),b.vertexAttribPointer(this.wa.la,this.vb.itemSize,b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,this.Og),b.vertexAttribPointer(this.wa.Ea,2,b.FLOAT,!1,0,0),b.activeTexture(b.TEXTURE0),b.bindTexture(b.TEXTURE_2D,this.$f),b.uniform1f(b.getUniformLocation(this.wa,"uAlpha"),1-a),b.uniform1i(b.getUniformLocation(this.wa,"uZoomIn"),
1==this.ea||2==this.ea||6==this.ea||7==this.ea?1:0),b.uniform2f(b.getUniformLocation(this.wa,"uZoomCenter"),h,n),b.uniform1f(b.getUniformLocation(this.wa,"uZoomFactor"),this.Lb),b.uniform1i(b.getUniformLocation(this.wa,"uSampler"),0);else if("diptocolor"==this.type)b.useProgram(this.jb),b.bindBuffer(b.ARRAY_BUFFER,this.vb),b.vertexAttribPointer(this.jb.la,this.vb.itemSize,b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,this.Og),b.vertexAttribPointer(this.jb.Ea,2,b.FLOAT,!1,0,0),b.activeTexture(b.TEXTURE0),
b.bindTexture(b.TEXTURE_2D,this.$f),b.uniform1f(b.getUniformLocation(this.jb,"uColorPercent"),Math.min(2*a,1)),b.uniform1f(b.getUniformLocation(this.jb,"uAlpha"),1-Math.max(2*(a-.5),0)),b.uniform3f(b.getUniformLocation(this.jb,"uDipColor"),(this.hg>>16&255)/255,(this.hg>>8&255)/255,(this.hg&255)/255),b.uniform1i(b.getUniformLocation(this.jb,"uZoomIn"),1==this.ea||2==this.ea||6==this.ea||7==this.ea?1:0),b.uniform2f(b.getUniformLocation(this.jb,"uZoomCenter"),h,n),b.uniform1f(b.getUniformLocation(this.jb,
"uZoomFactor"),this.Lb),b.uniform1i(b.getUniformLocation(this.jb,"uSampler"),0);else if("irisround"==this.type||"irisroundcenter"==this.type||"irisrectangular"==this.type||"irisrectangularcenter"==this.type){b.useProgram(this.Ta);b.bindBuffer(b.ARRAY_BUFFER,this.vb);b.vertexAttribPointer(this.Ta.la,this.vb.itemSize,b.FLOAT,!1,0,0);b.bindBuffer(b.ARRAY_BUFFER,this.Og);b.vertexAttribPointer(this.Ta.Ea,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_2D,this.$f);let x,y;1==this.ea||
2==this.ea||6==this.ea||7==this.ea||"irisroundcenter"==this.type||"irisrectangularcenter"==this.type?y=x=.5:(x=this.nh,y=this.oh);var r=x*e.width,w=y*e.height;r=Math.max(r,e.width-r);w=Math.max(w,e.height-w);"irisround"==this.type||"irisroundcenter"==this.type?b.uniform1f(b.getUniformLocation(this.Ta,"uRadius"),(Math.sqrt(r*r+w*w)+this.dd)*a):(r>w?(w=e.height/e.width*r+this.dd,r+=this.dd):(r=e.width/e.height*w+this.dd,w+=this.dd),b.uniform2f(b.getUniformLocation(this.Ta,"uRectDim"),r*a,w*a));b.uniform1f(b.getUniformLocation(this.Ta,
"uSoftEdge"),this.dd);b.uniform1i(b.getUniformLocation(this.Ta,"uRound"),"irisround"==this.type||"irisroundcenter"==this.type?1:0);b.uniform2f(b.getUniformLocation(this.Ta,"uIrisCenter"),x*e.width,y*e.height);b.uniform1i(b.getUniformLocation(this.Ta,"uZoomIn"),1==this.ea||2==this.ea||6==this.ea||7==this.ea?1:0);b.uniform2f(b.getUniformLocation(this.Ta,"uZoomCenter"),h,n);b.uniform1f(b.getUniformLocation(this.Ta,"uZoomFactor"),this.Lb);b.uniform1i(b.getUniformLocation(this.Ta,"uSampler"),0)}else if("wipeleftright"==
this.type||"wiperightleft"==this.type||"wipetopbottom"==this.type||"wipebottomtop"==this.type||"wiperandom"==this.type)b.useProgram(this.cb),b.bindBuffer(b.ARRAY_BUFFER,this.vb),b.vertexAttribPointer(this.cb.la,this.vb.itemSize,b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,this.Og),b.vertexAttribPointer(this.cb.Ea,2,b.FLOAT,!1,0,0),b.activeTexture(b.TEXTURE0),b.bindTexture(b.TEXTURE_2D,this.$f),b.uniform1f(b.getUniformLocation(this.cb,"uPercent"),3>this.Gl?a*(e.width+this.dd):a*(e.height+this.dd)),
b.uniform1f(b.getUniformLocation(this.cb,"uSoftEdge"),this.dd),b.uniform1i(b.getUniformLocation(this.cb,"uDirection"),this.Gl),b.uniform2f(b.getUniformLocation(this.cb,"uCanvasDimensions"),e.width,e.height),b.uniform1i(b.getUniformLocation(this.cb,"uZoomIn"),1==this.ea||2==this.ea||6==this.ea||7==this.ea?1:0),b.uniform2f(b.getUniformLocation(this.cb,"uZoomCenter"),h,n),b.uniform1f(b.getUniformLocation(this.cb,"uZoomFactor"),this.Lb),b.uniform1i(b.getUniformLocation(this.cb,"uSampler"),0);b.drawArrays(b.TRIANGLE_STRIP,
0,this.vb.numberOfItems);b.useProgram(f.Ig.R);b.disable(b.BLEND);b.enable(b.DEPTH_TEST)}}lr(){const a=this.o.N;a.texParameteri(a.TEXTURE_CUBE_MAP,a.TEXTURE_WRAP_S,a.CLAMP_TO_EDGE);a.texParameteri(a.TEXTURE_CUBE_MAP,a.TEXTURE_WRAP_T,a.CLAMP_TO_EDGE);a.texParameteri(a.TEXTURE_CUBE_MAP,a.TEXTURE_MIN_FILTER,a.LINEAR);a.texParameteri(a.TEXTURE_CUBE_MAP,a.TEXTURE_MAG_FILTER,a.LINEAR)}Ju(a){var b=this.o;const e=b.N,f=b.da[0];b.da[0].Dl();b.da[1].Dl();if(null!=b.da[0].hb&&null!=b.da[1].hb){var h=Sb.Ho(a);
f.D.pan.m=this.Xg.pan*(1-h)+this.qg.pan*h;f.D.u.m=this.Xg.u*(1-h)+this.qg.u*h;f.D.fov.m=this.Xg.fov*(1-h)+this.qg.fov*h;f.D.Xd();h=parseInt(b.Rh("__mesh_mb_iter",5));b=parseFloat(b.Rh("__mesh_mb_width",.05));1<b&&(b=1);e&&e.clear(e.COLOR_BUFFER_BIT);a=(a-.5)*(1+2*b)+.5-b;for(let n=0;n<h;n++){this.il=0==n?1:.5;let r=a+n/h*b;1<r&&(r=1);0>r&&(r=0);0<=r&&1>=r&&(f.En=Sb.Ho(r),e&&e.clear(e.STENCIL_BUFFER_BIT),this.Ku())}}}setup(){const a=this.o;this.xd||this.Qd||this.ed||a.zk(!0);var b;"wipeleftright"==
this.type?b=1:"wiperightleft"==this.type?b=2:"wipetopbottom"==this.type?b=3:"wipebottomtop"==this.type?b=4:"wiperandom"==this.type&&(b=Math.ceil(4*Math.random()));this.Gl=b;"mesh"!=this.type&&this.Ds();b=new Date;a.O!=a.Va&&6!=this.ea?7==this.ea?(this.nh=a.O.Ac/a.H.width,this.oh=.5):(this.nh=a.O.Ac/a.H.width,this.oh=1-a.O.ac/a.H.height):this.oh=this.nh=.5;if(1==this.ea||2==this.ea||6==this.ea||7==this.ea)this.hs=b.getTime()+100,this.Qd=!0,this.Lb=Math.sin(a.rd()/2*Math.PI/180)/Math.sin(this.fl/2*
Math.PI/180),this.Lb=Math.max(this.Lb,1),this.gs=1/this.qh*this.Lb*.3}Su(){const a=this.o,b=a.D;this.tf=parseFloat(a.Rh("__trans_blend_time",this.tf));a.Nr(null);if(0!=this.Vc){this.zn=b.be();this.An=b.Bf();this.Ui=b.rd();this.ne=b.Z();if(1==this.Vc||3==this.Vc)b.Jb(this.el);else if(2==this.Vc)b.Jb(this.cl);else if(4==this.Vc){let f=0;for(var e=0;e<a.za.length;e++)a.za[e].eb&&0==a.za[e].eb.indexOf("__FlyIn")&&(f=a.za[e]);0!=f?(b.$a(a.Cj(3,f).value),b.Nd(a.Cj(0,f).value),b.Od(a.Cj(1,f).value),b.Jb(a.Cj(2,
f).value)):(b.$a(b.Zc.rb),b.Nd(b.Zc.pan),b.Od(b.Zc.u),b.Jb(b.Zc.fov))}this.ph||1==this.ea||2==this.ea||6==this.ea||7==this.ea||a.tn()}}Ds(){const a=this.o.N,b=this.o;a.bindFramebuffer(a.FRAMEBUFFER,this.Pb);a.viewport(0,0,this.Pb.width,this.Pb.height);this.Zi=!0;b.bj();this.Zi=!1;a.bindFramebuffer(a.FRAMEBUFFER,null);a.viewport(0,0,b.Xa.width,b.Xa.height)}Fv(a){const b=this.o.N;this.Rk||(this.Rk=b.createBuffer(),this.Fn=b.createBuffer());b.bindBuffer(b.ARRAY_BUFFER,this.Rk);let e=[],f=[];const h=
a.pan,n=a.pan-this.offset,r=new lb,w=new lb;r.Fe(0,0);w.Fe(0,0);r.bc(a.u);w.bc(a.u);r.Sc(h);w.Sc(n);e.push(r.kf()/360);e.push((r.lf()+90)/180);e.push(w.kf()/360);e.push((w.lf()+90)/180);r.Fe(180,0);w.Fe(180,0);r.bc(a.u);w.bc(a.u);r.Sc(h);w.Sc(n);e.push(r.kf()/360);e.push((r.lf()+90)/180);e.push(w.kf()/360);e.push((w.lf()+90)/180);for(let x=0;64>x;x++)r.Fe(120,0),r.ke(360*x/64),w.Fe(60,0),w.ke(360*x/64),r.bc(a.u),w.bc(a.u),r.Sc(h),w.Sc(n),e.push(r.kf()/360),e.push((r.lf()+90)/180),e.push(w.kf()/360),
e.push((w.lf()+90)/180),f.push(2+x),f.push(2+(x+1)%64),f.push(0),f.push(1),f.push(2+(x+1)%64),f.push(2+x);this.Gn=f.length;b.bufferData(b.ARRAY_BUFFER,new Float32Array(e),b.STATIC_DRAW);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.Fn);a=new Uint16Array(f);b.bufferData(b.ELEMENT_ARRAY_BUFFER,a,b.STATIC_DRAW)}Ku(){const a=this.o,b=a.N;var e=a.D,f=a.da[0];let h=this.Kq;h&&(b.useProgram(h),b.disable(b.DEPTH_TEST),b.enable(b.STENCIL_TEST),b.blendFuncSeparate(b.SRC_ALPHA,b.ONE_MINUS_SRC_ALPHA,b.SRC_ALPHA,b.ONE),
b.enable(b.BLEND),b.stencilOp(b.KEEP,b.KEEP,b.INVERT),b.stencilFunc(b.EQUAL,0,1),b.enable(b.CULL_FACE),b.cullFace(b.FRONT),f.hh(0,h),kb.identity(this.Za),kb.perspective(e.Eb(),a.Xa.width/a.Xa.height,.001,10,this.Za),b.uniformMatrix4fv(b.getUniformLocation(h,"matProject"),!1,this.Za),b.uniform2f(b.getUniformLocation(h,"uCanvasDimensions"),a.Xa.width,a.Xa.height),e=b.getUniformLocation(h,"alphaBlend"),b.uniform1f(e,this.il),e=b.getUniformLocation(h,"rotOffset"),b.uniform2f(e,this.offset*Math.PI/180,
0*Math.PI/180),e=b.getUniformLocation(h,"irisRamp"),b.uniform2f(e,parseFloat(a.Rh("__mesh_iris_center",0)),parseFloat(a.Rh("__mesh_iris_outer",0))),f.Tk(h),f.hh(0,h),e=b.getUniformLocation(h,"cubeTexture"),b.uniform1i(e,0),e=b.getUniformLocation(h,"cubeTexture2"),b.uniform1i(e,1),f=b.getAttribLocation(h,"aVertexPosition"),b.disableVertexAttribArray(1),b.disableVertexAttribArray(2),b.enableVertexAttribArray(f),b.bindBuffer(b.ARRAY_BUFFER,this.Rk),b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.Fn),b.vertexAttribPointer(f,
4,b.FLOAT,!1,0,0),b.activeTexture(b.TEXTURE0),a.da[1].hb&&(b.bindTexture(b.TEXTURE_CUBE_MAP,a.da[1].hb),this.lr()),b.activeTexture(b.TEXTURE1),a.da[0].hb&&(b.bindTexture(b.TEXTURE_CUBE_MAP,a.da[0].hb),this.lr()),b.drawElements(b.TRIANGLES,this.Gn,b.UNSIGNED_SHORT,0),b.disable(b.STENCIL_TEST))}}class Pb{constructor(a){this.Ge=this.Na=!1;this.j=null;this.Xb=!0;this.Gm=this.vd=!1;this.Sa=this.Ca=this.ya=0;this.fov=70;this.Xc=0;this.ti=100;this.mk=0;this.autoplay=this.zg=this.Gf=!1;this.ad="";this.o=
null;this.Jp=!1;this.id=this.Lp="";this.u=this.pan=0;this.o=a;this.$c=this.lc=100;this.Kc=1}wb(a){let b;(b=a.getAttributeNode("id"))&&b.nodeValue&&(this.id=b.nodeValue.toString());if(b=a.getAttributeNode("pan"))this.pan=Number(b.nodeValue);if(b=a.getAttributeNode("tilt"))this.u=Number(b.nodeValue)}Jn(a){var b="";let e=this.o;var f=e.D;let h=!0;if(e.Xh){var n=new lb(0,0,-100);n.ya(-this.u*Math.PI/180);n.Ca(this.pan*Math.PI/180);n.Ca(-f.pan.m*Math.PI/180);n.ya(f.u.m*Math.PI/180);n.Sa(f.Y.m*Math.PI/
180);.01<=n.z&&(h=!1)}e.Xe&&(b+="perspective("+a+"px) ");b=b+("translate3d(0px,0px,"+a+"px) rotateZ(")+(f.Y.m.toFixed(10)+"deg) ");b+="rotateX("+f.u.m.toFixed(10)+"deg) ";b+="rotateY("+(-f.pan.m).toFixed(10)+"deg) ";b+="rotateY("+this.pan.toFixed(10)+"deg) ";b+="rotateX("+(-this.u).toFixed(10)+"deg) ";a=1E4;f=this.j.videoWidth;n=this.j.videoHeight;if(0==f||0==n)f=640,n=480;0<this.lc&&(f=this.lc);0<this.$c&&(n=this.$c);0<f&&0<n&&(this.j.width=f,this.j.height=n,this.j.style.width=f+"px",this.j.style.height=
n+"px");0<this.fov&&(a=f/(2*Math.tan(this.fov/2*Math.PI/180)));b+="translate3d(0px,0px,"+(-a).toFixed(10)+"px) ";b+="rotateZ("+this.Sa.toFixed(10)+"deg) ";b+="rotateY("+(-this.Ca).toFixed(10)+"deg) ";b+="rotateX("+this.ya.toFixed(10)+"deg) ";this.Kc&&1!=this.Kc&&(b+="scaleY("+this.Kc+") ");b+="translate3d("+-f/2+"px,"+-n/2+"px,0px) ";this.j.style[e.Be+"Origin"]="0% 0%";if(this.Na){b="";let r;1==this.Xc&&(r=Math.min(e.H.width/f,e.H.height/n));4!=this.Xc&&(r=r*this.ti/100,b+="scale("+r+") ");b+="translate3d("+
-f/2+"px,"+-n/2+"px,0px) "}this.Lp!=b&&(this.Lp=b,this.j.style[e.Be]=b,this.j.style.visibility=h&&this.Xb&&(!this.Gf||this.Ge||this.Na)?"visible":"hidden",this.Ge&&this.Jp==this.Na&&(this.j.style[e.Hc]="all 0s linear 0s"),this.Jp=this.Na)}Vf(a){this.j&&(this.j.style.visibility=a?"visible":"hidden")}qe(){let a=this.o;a.pa?(this.j.style.left=a.H.width/2+"px",this.j.style.top=a.H.height/2+"px"):(this.j.style.left=a.margin.left.ba+a.H.width/2+"px",this.j.style.top=a.margin.top.ba+a.H.height/2+"px")}We(){this.j.parentNode&&
this.j.parentNode.removeChild(this.j);this.j=null}Wd(){}je(){return 4==this.Xc||1==this.Xc}}class Bb extends Pb{constructor(a){super(a);this.ta=!1;this.gb=0;this.zd=this.Pl=this.sq=this.muted=this.wn=this.stopped=this.Cr=!1;this.kg=this.Db=this.Cb=this.Bb=this.lb=this.Cd=this.Ab=this.audioBuffer=this.Rb=this.source=null;this.Sf=this.Si=0;this.Kg=!1;this.j=null;this.cj=[];this.Km=this.Lm=this.fh=!1;this.url=[];this.loop=0;this.level=1;this.Ec=0;this.mode=1;this.zj=10;this.eh=this.Hb=0;this.va=1;this.ld=
this.kd=this.jd=this.hd=0;this.Qf=-1}se(){var a=this.o;a&&this.j&&(this.zd=!1,a=a.ga)&&(this.ta||7==this.mode||(this.source=a.createMediaElementSource(this.j)),2==this.mode||3==this.mode||5==this.mode?(this.Cd=a.createChannelSplitter(2),this.lb=a.createGain(),this.Bb=a.createGain(),this.Cb=a.createGain(),this.Db=a.createGain(),this.kg=a.createChannelMerger(2),this.ta||this.source.connect(this.Cd),this.Cd.connect(this.lb,0),this.Cd.connect(this.Bb,0),this.Cd.connect(this.Cb,1),this.Cd.connect(this.Db,
1),this.lb.connect(this.kg,0,0),this.Bb.connect(this.kg,0,1),this.Cb.connect(this.kg,0,0),this.Db.connect(this.kg,0,1),this.kg.connect(a.destination),1==this.Qf&&this.xn()):7!=this.mode&&(this.Ab=a.createGain(),this.ta||this.source.connect(this.Ab),this.Ab.connect(a.destination)))}jn(a){var b;let e=null===(b=this.o)||void 0===b?void 0:b.ga;(-1==a?this.muted:1!=a)?this.muted&&(this.zd&&this.se(),this.muted=this.j.muted=!1,this instanceof Qb||!this.wn||this.o.Md(this.id)):(a=!1,this.muted=this.j.muted=
!0,this.o.isPlaying(this.id)?(this.lb?(this.lb.gain.setValueAtTime(0,e.currentTime+1),this.Db.gain.setValueAtTime(0,e.currentTime+1),this.Bb.gain.setValueAtTime(0,e.currentTime+1),this.Cb.gain.setValueAtTime(0,e.currentTime+1)):this.Ab&&this.Ab.gain.setValueAtTime(0,e.currentTime+1),this instanceof Qb||(this.o.Jg(this.id),a=!0)):0<=this.loop&&this.autoplay&&!this.stopped&&(a=!0),a&&(this.wn=!0))}Uk(){if(!this.zd||4==this.mode||6==this.mode){var a=this.o,b=this.o.D,e=this.o.ga;if((this.j||this.ta)&&
!this.muted){var f,h=this.pan-b.pan.m;for(f=this.u-b.u.m;-180>h;)h+=360;for(;180<h;)h-=360;let x=this.Ec;var n=this.zj;0==n&&(n=.01);0>n&&(n=b.fov.m);this.v||(this.v=new lb,this.v.Fe(this.pan,this.u));0!=this.mode&&1!=this.mode||!e||this.Ab&&this.Ab.gain.setValueAtTime(this.level*a.fa*this.va,e.currentTime);2==this.mode&&e&&(b=.5*Math.cos(h*Math.PI/180)+.5,this.hd=Math.sqrt(b)*this.va*this.level*a.fa,this.ld=Math.sqrt(b)*this.va*this.level*a.fa,this.jd=Math.sqrt(1-b)*this.va*this.level*a.fa,this.kd=
Math.sqrt(1-b)*this.va*this.level*a.fa,this.Gi());if(3==this.mode){h=0>h?h<-this.Hb?h+this.Hb:0:h>this.Hb?h-this.Hb:0;b=this.level;f=Math.abs(f);f=f<this.eh?0:f-this.eh;var r=1-f/n;if(Math.abs(h)>n||0>r)r=b*x*a.fa,e?(this.hd=r*this.va,this.ld=r*this.va,this.kd=this.jd=0,this.Gi()):this.j.volume=b*x*a.fa;else{var w=1-Math.abs(h/n);if(e){let y=b*(x+(1-x)*r*w)*a.fa;w=b*x*a.fa;0<=h?(this.hd=y*this.va,this.ld=w*this.va):(this.hd=w*this.va,this.ld=y*this.va);2*Math.abs(h)<n?(w=1-Math.abs(2*h)/n,y=b*(x+
(1-x)*r*w)*a.fa,w=.5*b*(1-x)*r*(1-w)*a.fa,0<=h?(this.ld=y*this.va,this.kd=w*this.va,this.jd=0):(this.hd=y*this.va,this.jd=w*this.va,this.kd=0)):(w=1-(Math.abs(2*h)-n)/n,y=.5*b*(1-x)*r*w*a.fa,0<=h?(this.kd=y*this.va,this.jd=0):(this.jd=y*this.va,this.kd=0));this.Gi()}else this.j.volume=b*(x+(1-x)*r*w)*a.fa}}4==this.mode&&(Math.abs(h)<this.Hb&&Math.abs(f)<this.eh?this.fh||(this.fh=!0,this.gb=this.loop-1,a.Zd||(this.j.play(),this.zd&&this.se())):this.fh=!1);5==this.mode&&(f=180*Math.acos(a.Fl.qk(this.v))/
Math.PI,f<this.Hb?e?(this.hd=this.level*a.fa*this.va,this.ld=this.level*a.fa*this.va,this.kd=this.jd=0,this.Gi()):this.j.volume=this.level*a.fa:e?(f<this.Hb+n?(h=0>h?h>-this.Hb?0:h+this.Hb:h<this.Hb?0:h-this.Hb,e=1-Math.max(f-this.Hb,0)/n,n=Math.max(1-Math.abs(h)*Math.cos(this.u*Math.PI/180)/n,0),0<h?(this.hd=this.level*(e*(1-this.Ec)+this.Ec)*a.fa*this.va,this.ld=this.level*(e*n*(1-this.Ec)+this.Ec)*a.fa*this.va,this.jd=0,this.kd=this.level*e*(1-n)*a.fa*this.va):(this.hd=this.level*(e*n*(1-this.Ec)+
this.Ec)*a.fa*this.va,this.ld=this.level*(e*(1-this.Ec)+this.Ec)*a.fa*this.va,this.jd=this.level*e*(1-n)*a.fa*this.va,this.kd=0)):(h=this.level*this.Ec*a.fa,this.hd=h*this.va,this.ld=h*this.va,this.kd=this.jd=0),this.Gi()):(f-=this.Hb,this.j.volume=f<n&&0<n?this.level*(x+(1-x)*(1-Math.abs(f/n)))*a.fa:x*a.fa));6==this.mode&&(f=180*Math.acos(a.Fl.qk(this.v))/Math.PI,Math.abs(f)<this.Hb?this.fh||(this.fh=!0,this.gb=this.loop-1,this.ta?this.Kg||this.Vd():this.j.play()):this.fh=!1)}}}Vd(){let a=this.o.ga,
b=this.Sf;this.audioBuffer?(null==this.Rb&&(this.zo(),this.Rb.buffer=this.audioBuffer),this.Si=a.currentTime-b,this.Sf=0,this.Kg=!0,this.stopped=!1,this.Rb.start(0,b)):this.Km=!0}ol(){let a=this.o.ga.currentTime-this.Si;this.ig();this.Sf=a}ig(){this.Rb&&this.Kg&&(this.stopped=!0,this.Rb.disconnect(),this.Rb.stop(0),this.Rb=null);this.Si=this.Sf=0;this.Kg=!1}ss(){let a=this.o.ga;return this.Sf?this.Sf:this.Si?a.currentTime-this.Si:0}ts(a){this.ig();this.Sf=a;this.Vd()}addElement(){let a=-1,b=this,
e=this.o,f=this.o.ga;try{var h=!1;for(let n=0;n<e.X.length;n++)e.X[n].id==b.id&&(a=n,null==e.X[n].j&&!e.X[n].ta||e.X[n].url.join()!=b.url.join()||e.X[n].loop!=b.loop||e.X[n].mode!=b.mode||(h=!0,e.X[n].pan=b.pan,e.X[n].u=b.u,e.X[n].level=b.level,e.X[n].Ec=b.Ec,e.X[n].zj=b.zj,e.X[n].Hb=b.Hb,e.X[n].eh=b.eh));if(!h){if(0<=a){let n=e.X[a];if(null!=n.j||n.ta)if(f&&e.ab.enabled)e.ab.$i.push(n),1!=e.F.ea&&2!=e.F.ea&&6!=e.F.ea&&e.ab.No(n);else{try{n.ta?n.ol():n.j.pause()}catch(r){}try{n.We()}catch(r){}}}f&&
(this.zd=!0);b.j=document.createElement("audio");b.j.crossOrigin=e.crossOrigin;b.j.setAttribute("class","ggmedia");e.Le&&b.j.setAttribute("id",e.Le+b.id);for(h=0;h<b.url.length;h++){let n;n=document.createElement("source");if(""!=b.url[h]&&"#"!=b.url[h]){n.crossOrigin=e.crossOrigin;let r=e.Ha(e.Ka(b.url[h]));n.setAttribute("src",r);b.j.appendChild(n);b.cj.push(r)}}b.j.volume=b.level*e.fa;if(0<b.j.childNodes.length&&(e.T.appendChild(b.j),b.j.addEventListener("ended",function(){b.rr()},!1),f&&(b.Pl=
!1,0==b.loop&&b.source&&b.source.mediaElement&&(b.source.mediaElement.loop=!0),e.kw))){let n=new XMLHttpRequest;n.open("GET",e.Ha(b.url[0]),!0);n.responseType="arraybuffer";n.onload=function(){let r=n.response;f.decodeAudioData(r,function(w){b.Qf=w.numberOfChannels;b.ta=!0;b.se();b.Tu(r);1==b.Qf&&b.xn()})};n.send()}1<=b.loop&&(b.gb=b.loop-1);0<=a?e.X[a]=b:e.X.push(b);b.Uk();this.o.yc||(1!=b.mode&&2!=b.mode&&3!=b.mode&&5!=b.mode||!(0<=b.loop)||f&&e.ab.enabled||(b.ta||(b.j.autoplay=!0,b.Hn()),b.autoplay=
!0),0==b.mode&&0<=b.loop&&(b.autoplay=!0,b.Hn()))}}catch(n){}}Wd(){let a=this.o.isPlaying(this.id),b=0,e=!1;for(let f=0;f<this.j.children.length&&!(this.url.length<=b);f++){let h=this.j.children[f];if("SOURCE"==h.nodeName){let n=this.o.Ha(this.o.Ka(this.url[b]));this.cj[b]!=n&&(a&&!e&&this.o.Jg(this.id),h.setAttribute("src",n),this.cj[b]=n,e=!0);b++}}e&&(this.j.load(),a&&this.o.Md(this.id))}We(){try{this.ta||(this.o.T.removeChild(this.j),this.j=null)}catch(a){}}wb(a){super.wb(a);let b;(b=a.getAttributeNode("url"))&&
this.url.push(b.nodeValue.toString());if(b=a.getAttributeNode("level"))this.level=Number(b.nodeValue);if(b=a.getAttributeNode("loop"))this.loop=Number(b.nodeValue);if(b=a.getAttributeNode("mode"))this.mode=Number(b.nodeValue);if(b=a.getAttributeNode("nodechangekeep"))this.sq=1==Number(b.nodeValue);if(b=a.getAttributeNode("field"))this.zj=Number(b.nodeValue);if(b=a.getAttributeNode("ambientlevel"))this.Ec=Number(b.nodeValue);if(b=a.getAttributeNode("pansize"))this.Hb=Number(b.nodeValue);if(b=a.getAttributeNode("tiltsize"))this.eh=
Number(b.nodeValue);for(a=a.firstChild;a;)"source"==a.nodeName&&((b=a.getAttributeNode("url"))&&this.url.push(b.nodeValue.toString()),b=a.getAttributeNode("mono"))&&(this.Qf=1==Number(b.nodeValue)?1:2),a=a.nextSibling}Hn(){let a=this;var b=a.j.play();void 0!==b&&b.then(()=>{a.se()}).catch(()=>{a.j.pause();a.Cr&&(a.j.muted=!0,a.j.play())})}xn(){1!=this.Qf||this.zd||!this.Cd||2!=this.mode&&3!=this.mode&&5!=this.mode||(this.Cd.connect(this.Cb,0),this.Cd.connect(this.Db,0))}Gi(){const a=this.o.ga;this.Na||
this.Lm||(this.lb?(this.lb.gain.setValueAtTime(this.hd,a.currentTime),this.Db.gain.setValueAtTime(this.ld,a.currentTime),this.Bb.gain.setValueAtTime(this.jd,a.currentTime),this.Cb.gain.setValueAtTime(this.kd,a.currentTime)):this.Ab&&this.Ab.gain.setValueAtTime((this.hd+this.ld)/2,a.currentTime+1))}rr(){this.ta&&this.stopped?this.stopped=!1:0==this.loop?this.ta?(this.Rb=null,this.Vd()):this.j.play():0<this.gb?(this.gb--,this.ta||(this.j.currentTime=0),this.Pl&&(this.Ab&&0==this.Ab.gain.value||0==this.lb.gain.value&&
0==this.Db.gain.value&&0==this.Bb.gain.value&&0==this.Cb.gain.value)||(this.ta?(this.Rb=null,this.Vd()):this.j.play())):this.ta&&(this.Rb=null,this.Kg=!1)}zo(){let a=this;a.Rb=this.o.ga.createBufferSource();a.Rb.addEventListener("ended",function(){a.rr()},!1);2==a.mode||3==a.mode||5==a.mode?a.Rb.connect(a.Cd):a.Rb.connect(a.Ab)}Tu(a){let b=this,e=this.o.ga;b.zo();e.decodeAudioData(a,function(f){b.Qf=f.numberOfChannels;1==b.Qf&&b.xn();b.audioBuffer=f;b.Rb.buffer=f;b.Km&&(b.Vd(),b.Km=!1)})}}class Qb extends Bb{constructor(a){super(a);
this.poster="";this.Sa=this.Ca=this.ya=0;this.fov=50;this.Xc=0;this.ti=100;this.mk=0;this.zg=this.Gf=!1}Ie(){this.je()&&this.Df(!this.Na);2==this.Xc&&this.o.Jm(this.id)}Df(a){let b=this.o;var e=b.ga;if(this.je()){if(this.Na=a)this.j.style.pointerEvents="auto",this.j.style.cursor="pointer",this.j.style.zIndex=(b.fg+8E4).toString(),this.j.style[this.o.Hc]="all 1s ease 0s",this.j.muted=!1,this.o.isPlaying(this.id)||b.Md(this.id);else{this.j.style.pointerEvents="none";this.j.style.cursor="default";this.j.style.zIndex=
b.fg.toString();this.j.style[this.o.Hc]="all 1s ease 0s";this.o.isPlaying(this.id)&&(0==this.mk?b.Ti(this.id):2==this.mk&&(this.j.muted=!0));this.Lm=!0;let f=this;setTimeout(function(){f.Lm=!1},1E3)}if(e&&(2==this.mode||3==this.mode||5==this.mode)&&(e=e.currentTime,this.lb&&this.Db&&this.Bb.gain&&this.Bb&&this.Cb)){let f=this.lb.gain.value,h=this.Db.gain.value,n=this.Bb.gain.value,r=this.Cb.gain.value;this.Na?(this.lb.gain.linearRampToValueAtTime(f,e),this.lb.gain.linearRampToValueAtTime(this.level*
b.fa,e+1),this.Db.gain.linearRampToValueAtTime(h,e),this.Db.gain.linearRampToValueAtTime(this.level*b.fa,e+1),this.Bb.gain.linearRampToValueAtTime(n,e),this.Bb.gain.linearRampToValueAtTime(0,e+1),this.Cb.gain.linearRampToValueAtTime(r,e),this.Cb.gain.linearRampToValueAtTime(0,e+1)):(this.lb.gain.linearRampToValueAtTime(f,e),this.lb.gain.linearRampToValueAtTime(this.hd,e+1),this.Db.gain.linearRampToValueAtTime(h,e),this.Db.gain.linearRampToValueAtTime(this.ld,e+1),this.Bb.gain.linearRampToValueAtTime(n,
e),this.Bb.gain.linearRampToValueAtTime(this.jd,e+1),this.Cb.gain.linearRampToValueAtTime(r,e),this.Cb.gain.linearRampToValueAtTime(this.kd,e+1))}this.Ge=!0;this.o.Wr()}2==this.Xc&&(a?this.o.Md(this.id):this.o.Jg(this.id))}wb(a){super.wb(a);let b;if(b=a.getAttributeNode("poster"))this.poster=String(b.nodeValue);if(b=a.getAttributeNode("maskimage"))this.ad=String(b.nodeValue);if(b=a.getAttributeNode("rotx"))this.ya=Number(b.nodeValue);if(b=a.getAttributeNode("roty"))this.Ca=Number(b.nodeValue);if(b=
a.getAttributeNode("rotz"))this.Sa=Number(b.nodeValue);if(b=a.getAttributeNode("fov"))this.fov=Number(b.nodeValue);if(b=a.getAttributeNode("width"))this.lc=Number(b.nodeValue);if(b=a.getAttributeNode("height"))this.$c=Number(b.nodeValue);this.Kc=(b=a.getAttributeNode("stretch"))?Number(b.nodeValue):1;if(b=a.getAttributeNode("clickmode"))this.Xc=Number(b.nodeValue);if(b=a.getAttributeNode("popoutscaling"))this.ti=Number(b.nodeValue);if(b=a.getAttributeNode("popbackmode"))this.mk=Number(b.nodeValue);
if(b=a.getAttributeNode("handcursor"))this.zg=1==Number(b.nodeValue);if(b=a.getAttributeNode("startmutedmobile"))this.Cr=1==Number(b.nodeValue)}addElement(){let a=this,b=this.o,e=this.o.ga;try{a.j=document.createElement("video"),a.j.setAttribute("class","ggmedia"),a.j.setAttribute("disablePictureInPicture","true"),a.j.setAttribute("controlsList","nodownload"),a.j.crossOrigin=b.crossOrigin,a.j.hidden=!0,a.j.addEventListener("click",function(f){f.stopPropagation()}),b.Le&&a.j.setAttribute("id",b.Le+
a.id),b.ej?(a.j.setAttribute("playsinline","playsinline"),a.j.setAttribute("style","display: none; max-width:none;")):(a.j.setAttribute("style","max-width:none;pointer-events:none;"),a.j.setAttribute("playsinline","playsinline"),this.je()&&(a.j.addEventListener(b.al(),function(){a.Je()},!1),a.j.addEventListener("transitionend",function(){a.Je()},!1))),b.Yn(a.j,a.url,a),""!=a.poster&&(a.j.poster=b.Ha(b.Ka(a.poster)),0>a.loop&&(a.j.preload="none")),a.ad&&""!=a.ad&&(a.j.style.ad="url('"+b.Ha(a.ad)+"')",
a.j.style.webkitMaskImage="url('"+b.Ha(a.ad)+"')",a.j.style.maskSize="100% 100%",a.j.style.webkitMaskSize="100% 100%"),a.j.volume=a.level*b.fa,1<=a.loop&&(a.gb=a.loop-1),e&&(this.zd=!0),(1==a.mode||2==a.mode||3==a.mode||5==a.mode)&&0<=a.loop&&(a.j.autoplay=!0,a.isRunning=!0,a.autoplay=!0,this.o.yc&&(a.j.muted=!0),a.Hn()),b.V.push(this),b.ej?b.T.appendChild(a.j):(a.j.style.position="absolute",a.lc&&(a.j.width=a.lc),a.$c&&(a.j.height=a.$c),b.ia.appendChild(a.j)),a.j.addEventListener("ended",function(){a.lw()},
!1)}catch(f){}}Wd(){this.j.poster=this.o.Ha(this.o.Ka(this.poster));super.Wd()}registerElement(a,b){this.vd=!0;this.j=b;this.id=a;this.level=1;this.o.V.push(this)}We(){let a=this.o;a.ej&&(a.N.deleteTexture(this.texture),this.texture=0);this.j.parentNode&&this.j.parentNode.removeChild(this.j);this.j=null}Je(){this.Ge=!1;this.j.style[this.o.Hc]="none"}lw(){0==this.loop?this.j.play():0<this.gb?(this.gb--,this.j.currentTime=0,this.j.play()):this.isRunning=!1}}class id extends Pb{constructor(a){super(a);
this.url="";this.Sa=this.Ca=this.ya=0;this.fov=50;this.Xc=0;this.zg=!1;this.$c=this.lc=100;this.Kc=1}wb(a){super.wb(a);let b;if(b=a.getAttributeNode("url"))this.url=b.nodeValue.toString();if(b=a.getAttributeNode("maskimage"))this.ad=String(b.nodeValue);if(b=a.getAttributeNode("rotx"))this.ya=Number(b.nodeValue);if(b=a.getAttributeNode("roty"))this.Ca=Number(b.nodeValue);if(b=a.getAttributeNode("rotz"))this.Sa=Number(b.nodeValue);if(b=a.getAttributeNode("fov"))this.fov=Number(b.nodeValue);if(b=a.getAttributeNode("width"))this.lc=
Number(b.nodeValue);if(b=a.getAttributeNode("height"))this.$c=Number(b.nodeValue);this.Kc=(b=a.getAttributeNode("stretch"))?Number(b.nodeValue):1;if(b=a.getAttributeNode("clickmode"))this.Xc=Number(b.nodeValue);if(b=a.getAttributeNode("popoutscaling"))this.ti=Number(b.nodeValue);if(b=a.getAttributeNode("hidepinned"))this.Gf=1==Number(b.nodeValue);if(b=a.getAttributeNode("handcursor"))this.zg=1==Number(b.nodeValue);for(a=a.firstChild;a;)"source"==a.nodeName&&(b=a.getAttributeNode("url"))&&(this.url=
b.nodeValue.toString()),a=a.nextSibling}Ie(){this.je()&&this.Df(!this.Na)}Df(a){let b=this.o;this.je()&&(this.Na=a,this.Gf&&(this.j.style.visibility="visible",this.j.style.opacity=this.Na?"0.0":"1.0"),this.Na?(this.j.style.pointerEvents="auto",this.j.style.cursor="pointer",this.j.style.zIndex=(b.fg+8E4).toString(),this.j.style[b.Hc]="all 1s ease 0s",this.j.style.opacity="1.0"):(this.j.style.pointerEvents="none",this.j.style.cursor="default",this.j.style.zIndex=b.fg.toString(),this.j.style[b.Hc]="all 1s ease 0s",
this.Gf&&(this.j.style.opacity="0.0")),this.Ge=!0,b.In())}addElement(){let a=this,b=this.o;try{a.j=document.createElement("img"),a.j.setAttribute("style","-webkit-user-drag:none; max-width:none; pointer-events:none;"),a.j.setAttribute("class","ggmedia"),a.j.hidden=!0,a.j.addEventListener("click",function(e){e.stopPropagation()}),b.Le&&a.j.setAttribute("id",b.Le+a.id),a.j.ondragstart=function(){return!1},this.je()&&(a.j.addEventListener(b.al(),function(){a.Je()},!1),a.j.addEventListener("transitionend",
function(){a.Je()},!1)),a.j.setAttribute("src",b.Ha(b.Ka(a.url))),a.lc&&(a.j.width=a.lc),a.$c&&(a.j.height=a.$c),b.Ya.push(a),a.j.style.position="absolute",a.ad&&""!=a.ad&&(a.j.style.ad="url('"+b.Ha(a.ad)+"')",a.j.style.webkitMaskImage="url('"+b.Ha(a.ad)+"')",a.j.style.maskSize="100% 100%",a.j.style.webkitMaskSize="100% 100%"),b.ia.appendChild(a.j)}catch(e){}}Wd(){this.j.setAttribute("src",this.o.Ha(this.o.Ka(this.url)))}Je(){this.Ge=!1;this.j.style[this.o.Hc]="none";this.Gf&&!this.Na&&(this.j.style.visibility=
"hidden")}}class jd extends Pb{constructor(a){super(a);this.code=""}wb(a){super.wb(a);let b;if(b=a.getAttributeNode("rotx"))this.ya=Number(b.nodeValue);if(b=a.getAttributeNode("roty"))this.Ca=Number(b.nodeValue);if(b=a.getAttributeNode("rotz"))this.Sa=Number(b.nodeValue);if(b=a.getAttributeNode("fov"))this.fov=Number(b.nodeValue);if(b=a.getAttributeNode("width"))this.lc=Number(b.nodeValue);if(b=a.getAttributeNode("height"))this.$c=Number(b.nodeValue);this.Kc=(b=a.getAttributeNode("stretch"))?Number(b.nodeValue):
1;if(b=a.getAttributeNode("clickmode"))this.Xc=Number(b.nodeValue);if(b=a.getAttributeNode("popoutscaling"))this.ti=Number(b.nodeValue);if(b=a.getAttributeNode("handcursor"))this.zg=1==Number(b.nodeValue);if(b=a.getAttributeNode("code"))this.code=b.nodeValue.toString();if(b=a.getAttributeNode("permeable"))this.Gm=1==Number(b.nodeValue)}Ie(){this.je()&&this.Df(!this.Na)}Df(a){let b=this.o;this.je()&&(this.Na=a,this.Gf&&(this.j.style.visibility="visible",this.j.style.opacity=this.Na?"0.0":"1.0"),this.Na?
(this.j.style.cursor="pointer",this.j.style.zIndex=(b.fg+8E4).toString(),this.j.style[b.Hc]="all 1s ease 0s",this.j.style.opacity="1.0"):(this.j.style.cursor="default",this.j.style.zIndex=b.fg.toString(),this.j.style[b.Hc]="all 1s ease 0s"),this.Ge=!0,b.In())}addElement(){let a=this,b=this.o;try{a.j=document.createElement("div");this.Pr();let e="max-width:none;";this.Gm&&(e+="pointer-events:none;");a.j.setAttribute("style",e);a.j.setAttribute("class","ggmedia");b.Le&&a.j.setAttribute("id",b.Le+a.id);
this.je()&&(a.j.addEventListener(b.al(),function(){a.Je()},!1),a.j.addEventListener("transitionend",function(){a.Je()},!1));b.Qb.push(a);a.j.style.position="absolute";a.j.style.visibility="hidden";0==a.Xc?b.xa.insertBefore(a.j,b.xa.firstChild):b.ia.appendChild(a.j)}catch(e){}}Pr(){let a=this.j.innerHTML,b=this.o.Ka(this.code);b!=a&&(this.j.innerHTML=b)}Wd(){this.Pr()}Je(){this.Ge=!1;this.j.style[this.o.Hc]="none"}}class kd extends Pb{constructor(a){super(a);this.alpha=this.jo=50;this.type=0;this.color=
16777215}wb(a){super.wb(a);let b;if(b=a.getAttributeNode("blinding"))this.jo=Number(b.nodeValue);if(b=a.getAttributeNode("alpha"))this.alpha=Number(b.nodeValue);if(b=a.getAttributeNode("type"))this.type=Number(b.nodeValue);if(b=a.getAttributeNode("color"))this.color=1*Number(b.nodeValue)}}class Rb{constructor(a){this.o=a;this.type="empty";this.nn=this.id=this.target=this.description=this.title=this.url="";this.w=100;this.h=20;this.gj=!1;this.j=null;this.Pm=this.Om=this.ac=this.Ac=this.kb=this.Fa=
this.distance=this.u=this.pan=0;this.visible=!0;this.ic=a.P.ic;this.fc=a.P.fc;this.hc=a.P.hc;this.ec=a.P.ec;this.Ke=a.P.Ke;this.od="";this.Dd=this.uf=0;this.ag=!1;this.uj=0;this.vertices=[]}xf(){this.id=this.id;this.pan=this.pan;this.tilt=this.u;this.url=this.url;this.target=this.target;this.title=this.title;this.distance=this.distance;this.description=this.description;this.skinid=this.nn;this.obj=this.j;this.customimage=this.od;this.customimagewidth=this.uf;this.customimageheight=this.Dd;this.use3D=
this.ag;this.distance3D=this.uj;this.j&&this.j.__div&&(this.div=this.j.__div)}Qv(){let a=this.url;return"{"==a.charAt(0)?a.substring(1,a.length-1):!1}wb(a){var b;(b=a.getAttributeNode("url"))&&b.nodeValue&&(this.url=b.nodeValue.toString());(b=a.getAttributeNode("target"))&&b.nodeValue&&(this.target=b.nodeValue.toString());(b=a.getAttributeNode("title"))&&b.nodeValue&&(this.title=b.nodeValue.toString());(b=a.getAttributeNode("description"))&&b.nodeValue&&(this.description=b.nodeValue.toString());(b=
a.getAttributeNode("id"))&&b.nodeValue&&(this.id=b.nodeValue.toString());(b=a.getAttributeNode("skinid"))&&b.nodeValue&&(this.nn=b.nodeValue.toString());if(b=a.getAttributeNode("width"))this.w=Number(b.nodeValue);if(b=a.getAttributeNode("height"))this.h=Number(b.nodeValue);if(b=a.getAttributeNode("wordwrap"))this.gj=1==Number(b.nodeValue);b=a.getAttributeNode("pan");this.pan=1*(b?Number(b.nodeValue):0);b=a.getAttributeNode("tilt");this.u=1*(b?Number(b.nodeValue):0);b=a.getAttributeNode("fov");this.fov=
1*(b?Number(b.nodeValue):0);b=a.getAttributeNode("distance");this.distance=1*(b?Number(b.nodeValue):0);if(b=a.getAttributeNode("bordercolor"))this.ic=1*Number(b.nodeValue);if(b=a.getAttributeNode("backgroundcolor"))this.fc=1*Number(b.nodeValue);if(b=a.getAttributeNode("borderalpha"))this.hc=1*Number(b.nodeValue);if(b=a.getAttributeNode("backgroundalpha"))this.ec=1*Number(b.nodeValue);if(b=a.getAttributeNode("handcursor"))this.Ke=1==Number(b.nodeValue);if(b=a.getAttributeNode("customimage"))this.od=
this.o.bp()+b.nodeValue;if(b=a.getAttributeNode("customimagewidth"))this.uf=Number(b.nodeValue);if(b=a.getAttributeNode("customimageheight"))this.Dd=Number(b.nodeValue);if(b=a.getAttributeNode("use3D"))this.ag=1==Number(b.nodeValue);if(b=a.getAttributeNode("distance3D"))this.uj=Number(b.nodeValue);for(a=a.firstChild;a;){if("polystring"==a.nodeName){var e=a.textContent.toString().split("|");for(b=0;b<e.length;b++){let f=e[b].split("/");if(2==f.length){let h={pan:0,u:0};h.pan=Number(f[0]);h.u=Number(f[1]);
this.vertices.push(h)}}}"vertex"==a.nodeName&&(e={pan:0,u:0},b=a.getAttributeNode("pan"),e.pan=1*(b?Number(b.nodeValue):0),b=a.getAttributeNode("tilt"),e.u=1*(b?Number(b.nodeValue):0),this.vertices.push(e));a=a.nextSibling}this.xf()}}let Db=2E3;class md{Fs(){this.init();this.animate()}constructor(a){this.xb=null;this.controller=[];this.line=this.jg=this.rl=null;this.Qa=[];this.jk=[];this.ca={x:0,y:0};this.bd={x:0,y:0};this.li=0;this.session=null;this.Aj=!1;this.Mi=!0;this.Zk=this.Pn=this.Rf=0;this.mm=
this.bg=!1;this.mn=0;this.Xf=-4;this.Dn=0;this.yj=this.ug=this.ek=!1;this.o=null;this.an=!0;this.Ee=1;this.Mj=[];this.frustumCulled=!1;this.Lj=0;this.Yk=null;this.Io=!0;this.Fk=0;this.Jl=1;this.Rn=this.Dc=eb.SRGBColorSpace;this.Mp=this.Al=null;this.Uj=-1;this.pl=this.ds=!1;this.Oe=[-1,-1,-1,-1];this.Pe=[-1,-1,-1,-1];this.Lf=[!1,!1,!1,!1];this.Vb=[];this.Zj=[];this.Kf=[];this.ql=new Map;this.o=a;var b=this;setTimeout(function(){if(window.WebXRPolyfill&&window.Promise){let e={cardboardConfig:{BUFFER_SCALE:1}};
try{new window.WebXRPolyfill(e)}catch(f){a.Yb(f)}}b.Zt()},0);a.Ff()?this.io():console.log("Three.js not available!")}io(){this.Vi=new eb.Matrix4;this.vq=new eb.Euler;this.Eq=new eb.Ray}init(){const a=this;var b=this.o;"xr"in navigator||console.log("no WebXR support");if(b.Ff())if(this.xb)console.log("already set up!");else{this.Vi||this.io();var e=this.container=b.container,f=this.vk=new eb.Scene,h=new eb.Group,n=this.camera=new eb.PerspectiveCamera(100,window.innerWidth/window.innerHeight,.001,Db);
f.add(n);f.add(h);108>parseFloat(eb.REVISION)&&(this.frustumCulled=!0);n.position.z=-.01;n.position.y=0;n.position.x=0;n=new eb.WebGLRenderer({depth:!0,logarithmicDepthBuffer:!1,alpha:!0,antialias:!0,stencil:!1,powerPreference:"high-performance"});eb.ColorManagement.enabled=!0;b.Wj&&17==parseInt(b.Ob)&&this.Wq(eb.SRGBColorSpace,eb.LinearSRGBColorSpace);n.outputColorSpace=this.Rn;n.setPixelRatio(window.devicePixelRatio);this.xb=n;n.setClearColor(new eb.Color(0),1);n.setSize(window.innerWidth,window.innerHeight);
n.shadowMap.enabled=!1;e.appendChild(n.domElement);"xr"in navigator&&(n.xr.enabled=!0,n.xr.setReferenceSpaceType("local"));n=this.oj=new eb.Group;n.name="controllerGroup";n.renderOrder=3E3;f.add(new eb.HemisphereLight(12369084,10855845,3));e=new eb.DirectionalLight(16777215,3);e.position.set(0,6,0);f.add(e);e=(new eb.BufferGeometry).setFromPoints([new eb.Vector3(0,0,0),new eb.Vector3(0,0,-1)]);var r=new eb.LineBasicMaterial({color:14737632});e=new eb.Line(e,r);e.name="line";e.scale.z=5;this.line=
e;this.Vn(0);this.Vn(1);n.visible=!1;e=this.$e=new eb.Group;e.name="skinGroup";e.translateZ(this.Xf);e.visible=!1;e.renderOrder=1E3;h.add(e);e=this.Cc=new eb.Group;e.name="toggleSkinGroup";e.translateZ(this.Xf+.1);e.position.y=0==this.Fk?3.17:-3.17;e.renderOrder=1100;e.visible=!1;h.add(e);h.add(n);h=(new eb.TextureLoader).load(b.Ha("webxr/vrSkinShow.svg"));e=new eb.PlaneGeometry(.3,.3);h=new eb.MeshBasicMaterial({map:h,side:eb.DoubleSide,transparent:!0});e=new eb.Mesh(e,h);e.name="showSkinButton";
e.userData.onclick=function(){a.le()};e.userData.setOpacity=function(w){a.Wf.material.opacity=w};e.visible=!0;this.Wf=e;this.Cc.add(e);h=(new eb.TextureLoader).load(b.Ha("webxr/vrSkinHide.svg"));e=new eb.PlaneGeometry(.3,.3);h=new eb.MeshBasicMaterial({map:h,side:eb.DoubleSide,transparent:!0});e=new eb.Mesh(e,h);e.name="hideSkinButton";e.userData.onclick=function(){a.le()};e.userData.setOpacity=function(w){a.Cg.material.opacity=w};e.visible=!1;this.Cg=e;this.Cc.add(e);this.Ng=new eb.Raycaster;this.ca=
new eb.Vector2;this.Te=new eb.Group;this.Te.name="panoGroup";b.Lc||(this.Te.renderOrder=1);this.Hf=new eb.Group;this.Hf.name="imageGroup";this.Id=new eb.Group;this.Id.name="hotspotGroup";this.pg=new eb.Group;this.pg.name="elementGroup";this.jg=new eb.Group;this.jg.name="centerGroup";b.Lc||(this.jg.renderOrder=2E3);this.Te.add(this.pg);this.Te.add(this.Id);this.Te.add(this.Hf);f.add(this.Te);e=new eb.BufferGeometry;b=[];f=[];for(h=0;20>h;h++)r=2*h*Math.PI/20,n=Math.cos(r),r=Math.sin(r),f.push(.03*
n,.03*r,0),f.push(.05*n,.05*r,0);for(h=0;20>h;h++){n=2*h+1;r=(h+1)%20*2+1;const w=(h+1)%20*2;b.push(2*h,n,w);b.push(n,r,w)}e.setIndex(b);e.setAttribute("position",new eb.Float32BufferAttribute(f,3));b=new eb.MeshBasicMaterial({color:"black",side:eb.DoubleSide,transparent:!0,opacity:.25});b=new eb.Mesh(e,b);b.position.z=-5;this.rl=b;this.jg.add(b);window.addEventListener("resize",function(){a.Du()},!1);document.addEventListener("mouseup",function(){a.zu()},!1);document.addEventListener("mousedown",
function(w){a.xu(w)},!1);document.addEventListener("mousemove",function(w){a.yu(w)},!1)}else console.log("Three.js not available!")}Vn(a){const b=this,e=b.vk;var f=b.xb;this.Al||(this.Al=new eb.uw,this.Mp=new eb.vw);let h=f.xr.getController(a);h.addEventListener("selectstart",function(){-1==b.Uj&&b.yq()});h.addEventListener("selectend",function(r){b.xq(r)});h.visible=!1;h.userData.pj=a;this.oj.add(h);h.add(this.line.clone());var n=f.xr.getControllerGrip(a);n.add(this.Al.ww(n));e.add(n);f=f.xr.getHand(a);
n=this.Mp.xw(f);f.add(n);f.addEventListener("pinchstart",()=>{b.Uj=a;b.Hm=Date.now();const r=Date.now()-b.Im;b.Im=Date.now();b.yq();b.Vi.identity().extractRotation(h.matrixWorld);const w=b.Eq;w.origin.setFromMatrixPosition(h.matrixWorld);w.direction.set(0,0,-1).applyMatrix4(this.Vi);500>b.gu&&800>r&&(this.le(),b.Im=0)});f.addEventListener("pinchend",r=>{b.Uj=-1;b.gu=Date.now()-b.Im;b.xq(r)});e.add(f);this.controller[a]=h}nr(){var a=this.o;a.G("vrchanged",{});a.G("playerstatechanged",{})}Kv(){this.mm=
!0;let a=this.o;a.G("vrchanged",{});a.G("playerstatechanged",{})}Zt(){if(!this.ds){this.ds=!0;var a=this;this.o.Ff()&&"xr"in navigator&&navigator.xr.isSessionSupported("immersive-vr").then(function(b){b?a.Kv():a.nr()}).catch(a.nr)}}Hj(){return this.$e}Ne(){return this.Aj}Bg(){return this.mm}Bu(a){let b=this;this.hj();a.addEventListener("end",function(){b.Au()});a.addEventListener("inputsourceschange",()=>{});console.log("XR Session started.");this.xb.xr.setSession(a);this.Aj=!0;this.session=a;b.o.G("entervr",
{session:a})}Au(){console.log("XR Session ended.");this.Aj=!1;this.session=null;this.xl()}rg(a){let b=this.o;if(this.mm&&!a){if(null===this.session){let e=this;navigator.xr.requestSession("immersive-vr",{optionalFeatures:["hand-tracking"]}).then(f=>{e.Bu(f)}).catch(function(f){console.log(f);console.warn("'immersive-vr' isn't supported, or an error occurred activating VR!");e.xl()})}this.Id&&this.Id.translateY(-this.Lj);this.Lj=0;1==this.Ee&&(a=navigator.userAgent,0<=a.indexOf("OculusBrowser")&&(0<=
a.indexOf("Pacific")&&(this.Ee=1.7),0<=a.indexOf("Quest")&&(this.Ee=1.5),0<=a.indexOf("Quest 3")&&(this.Ee=1.5)));this.xb.xr.setFramebufferScaleFactor(this.Ee);b.Yb("Enter VR - scaled "+this.Ee)}else this.hj(),this.o.G("entervr",{session:null})}Eu(){const a=this.o;a.Ff()&&this.xb&&(a.ef=!0,a.Mb&&(Db=2E3),a.Xh&&(Db=2E3),a.T.style.display="none",a.La.style.display="none",this.xb.domElement.style.display="inline",a.G("vrchanged",{}),a.G("playerstatechanged",{}))}xl(){const a=this.o;a.Ff()&&this.xb&&
(a.ef=!1,a.T.style.display="inline",a.La.style.display="inline",this.xb.domElement.style.display="none",this.o.gr(!1),a.Tg(0),a.fd(),a.update(),a.G("vrchanged",{}),a.G("playerstatechanged",{}),this.o.G("exitvr",{session:null}),this.to())}sg(){null!=this.session&&this.session.end()}Jr(){this.Ne()?this.sg():this.rg()}Ps(){let a=0,b=this.o.D.M,e;e=1536;this.xb&&(e*=this.Ee);e*=Math.pow(2,0);if(0<b.levels.length)for(;b.levels.length>=a+2&&!b.levels[a+1].Mg&&b.levels[a+1].width>e;)a++;return a}Ap(a){let b=
this.o;var e=b.D.M,f=b.df[a];a="";if(f){f=f.firstChild;for(var h;f;)"input"==f.nodeName&&(h=f)&&(h=0<e.levels.length?h.getAttributeNode("leveltileurl"):h.getAttributeNode("tilevrurl"))&&(a=h.nodeValue),f=f.nextSibling}else a=0<e.levels.length?e.xm:b.El;e=a;a=b.Nq;for(f=0;3>f;f++)e=a(e,"c","vr"),e=a(e,"s","vr"),e=a(e,"r",0),e=a(e,"l",0),e=a(e,"x",0),e=a(e,"y",0),e=a(e,"v",0),e=a(e,"h",0);return b.Ha(e)}ceilPowerOfTwo(a){return Math.pow(2,Math.ceil(Math.log(a)/Math.LN2))}jq(a,b,e){let f=new eb.ImageLoader;
f.setCrossOrigin(this.o.crossOrigin);let h=this;f.load(b,function(n){let r=h.ceilPowerOfTwo(n.width),w=h.ceilPowerOfTwo(n.height);a.colorSpace=h.Dc;if(r==n.width&&w==n.height||!e)a.image=n;else{var x=document.createElement("canvas");x.width=r;x.height=w;x.getContext("2d").drawImage(n,0,0,r,w);a.image=x}a.needsUpdate=!0;a.Kp=!0;a.He&&a.He.Kp&&(a.Sh.visible=!0,a.He.Sh.visible=!0)});this.Mj.push(a)}hj(){const a=this.o;if(a.Ff()){var b=a.D;if(!this.pl){var e=this.Hf,f=this;this.Uf();this.camera.layers.enable(1);
e.setRotationFromEuler(this.vq);e.rotateX(b.qb.pitch*Math.PI/180);e.rotateZ(-b.qb.Y*Math.PI/180);var h=new eb.SphereGeometry(Db/3*1.5,16,16),n=new eb.Texture,r=new eb.ImageLoader;r.setCrossOrigin(this.o.crossOrigin);r.load(this.Ap(a.qd()),function(w){n.colorSpace=f.Dc;n.image=w;n.needsUpdate=!0});r=new eb.MeshBasicMaterial({map:n,side:eb.DoubleSide});h=new eb.Mesh(h,r);h.scale.x=-1;h.rotateZ(b.qb.Y*Math.PI/180);h.rotateX(-b.qb.pitch*Math.PI/180);h.rotateY(-90*Math.PI/180);e.add(h);f.pk=h;f.pl=!0;
setTimeout(function(){f.pl=!1;f.ks();a.B.j?f.ns():f.ls();f.js();f.Mi&&(f.Mi=!1,a.G("vrconfigloaded",{}));window.__igloo__&&f.vk.traverse(w=>{w.frustumCulled=!1})});this.Xu();this.an=!0}}}ns(){function a(){var y=0;let z=2*Math.PI,cb=0,ab=Math.PI;if(15==f.format&&(y=Math.PI/2,z=Math.PI,0<b.B.width&&0<b.B.height)){let db;db=.5*b.B.width/b.B.height;1<db&&(ab=Math.PI/db,cb=(Math.PI-ab)/2)}y=new eb.SphereGeometry(h,60,40,y,z,cb,ab);y.scale(-1,1,1);return y}let b=this.o,e=this.Hf,f=b.B,h=Db/3;if(b.pc){var n=
new eb.VideoTexture(b.B.j);n.wrapS=eb.ClampToEdgeWrapping;n.wrapT=eb.ClampToEdgeWrapping;n.minFilter=eb.LinearFilter;n.colorSpace=this.Dc;var r=a(),w=r.attributes.uv.array;for(var x=0;x<w.length;x+=2)15==f.format?(w[x]*=.5,w[x]+=.5):w[x+1]*=.5;w=new eb.MeshBasicMaterial({map:n});r=new eb.Mesh(r,w);r.rotateY(-90*Math.PI/180);r.layers.set(b.Zf?1:2);e.add(r);r=a();w=r.attributes.uv.array;for(x=0;x<w.length;x+=2)15==f.format?w[x]*=.5:(w[x+1]*=.5,w[x+1]+=.5);w=new eb.MeshBasicMaterial({map:n});r=new eb.Mesh(r,
w);r.rotateY(-90*Math.PI/180);r.layers.set(b.Zf?2:1);e.add(r)}else n=a(),r=new eb.VideoTexture(b.B.j),r.colorSpace=this.Dc,r.wrapS=eb.ClampToEdgeWrapping,r.wrapT=eb.ClampToEdgeWrapping,r.minFilter=eb.LinearFilter,r=new eb.MeshBasicMaterial({map:r}),n=new eb.Mesh(n,r),n.rotateY(-90*Math.PI/180),e.add(n);try{b.B.j.play()}catch(y){b.Yb(y)}}ls(){let a=this.o;var b=this.o.D.M;let e=[];var f=this.Ps();let h=this.Hf,n=Db/3,r=a.pc?2:1,w;if(b.levels.length>f){var x=b.levels[f];for(var y=0;6>y;y++){e[y]=new eb.Group;
for(var z=0;z<x.sa;z++)for(var cb=0;cb<x.$;cb++)for(let bb=0;bb<r;bb++){let fb=new eb.Texture;fb.colorSpace=this.Dc;this.jq(fb,a.D.bh(y,f,cb,z,1==bb),!0);fb.wrapS=eb.ClampToEdgeWrapping;fb.wrapT=eb.ClampToEdgeWrapping;fb.minFilter=eb.LinearMipMapLinearFilter;fb.anisotropy=1;var ab=b.W+2*b.ib;let ib=b.W+2*b.ib;cb==x.$-1&&(ab=x.width-b.W*(x.$-1)+2*b.ib);z==x.sa-1&&(ib=x.width-b.W*(x.sa-1)+2*b.ib);var db=new eb.PlaneGeometry(n*ab/x.width,n*ib/x.width,8),gb=new eb.MeshBasicMaterial({map:fb});db=new eb.Mesh(db,
gb);gb.transparent=!0;gb.side=eb.FrontSide;gb=1*b.W;db.position.x=n*(cb*gb+ab/2-b.ib-x.width/2)/x.width;db.position.y=n*-(z*gb+ib/2-b.ib-x.width/2)/x.width;db.position.z=-.5*n;a.pc&&(db.visible=!1,db.layers.set(1==bb?1:2),fb.Sh=db,0==bb?w=db:(ab=w.material.map,fb.He=ab,ab.He=fb));e[y].add(db);db.frustumCulled=this.frustumCulled}4>y?e[y].rotation.y=-y*Math.PI/2:e[y].rotation.x=(4==y?1:-1)*Math.PI/2;h.add(e[y])}}else for(b=a.dh,f=0;6>f;f++)for(e[f]=new eb.Group,x=0;x<r;x++)z=f,a.pc&&(z=f+(1==x?6:0)),
y=new eb.Texture,y.colorSpace=this.Dc,this.jq(y,a.Ha(a.Ah[z]),!1),y.wrapS=eb.ClampToEdgeWrapping,y.wrapT=eb.ClampToEdgeWrapping,y.minFilter=eb.LinearFilter,cb=new eb.PlaneGeometry(b*n,b*n,64),z=new eb.MeshBasicMaterial({map:y}),cb=new eb.Mesh(cb,z),z.transparent=!0,z.side=eb.FrontSide,cb.position.x=0,cb.position.y=0,cb.position.z=-.5*n,a.pc&&(cb.layers.set(1==x?1:2),cb.visible=!1,y.Sh=cb,0==x?w=cb:(z=w.material.map,y.He=z,z.He=y)),e[f].add(cb),4>f?e[f].rotation.y=-f*Math.PI/2:e[f].rotation.x=(4==
f?1:-1)*Math.PI/2,h.add(e[f])}ks(){let a=this.o,b=this.Id;new lb(0,0,-100);let e=a.P.Mk;for(let x=0;x<a.L.length;x++){let y=a.L[x];if("poly"==y.type)continue;var f=null,h=y.Qv(),n=!1;let z=y.ag?100:5;a.vrSkinObj&&a.vrSkinObj.addSkinHotspot&&(f=a.vrSkinObj.addSkinHotspot(y))&&(n=!0,f.userData.Lw&&(z=100));if(!n&&(n=.22,!1!==h)){z=!y.distance||0>=y.distance?5:1>y.distance?1:5>y.distance?y.distance:5;y.od?(n=(new eb.TextureLoader).load(y.od),n.colorSpace=this.Dc,f=new eb.MeshBasicMaterial({map:n}),y.ag?
(n=2*z*Math.tan(y.fov/2*Math.PI/180),h=new eb.PlaneGeometry(n,n*y.Dd/y.uf,1,1),f.transparent=!0,f.opacity=1):(h=new eb.PlaneGeometry(.005*y.uf,.005*y.Dd,1,1),f.transparent=!0,f.opacity=.25),n=.005*y.Dd/2,f.side=eb.DoubleSide,f=new eb.Mesh(h,f)):(f=new eb.SphereGeometry(.2*this.Jl,16,16),h=(new eb.TextureLoader).load(this.Ap(h)),h.colorSpace=this.Dc,h=new eb.MeshBasicMaterial({map:h}),h.transparent=!0,h.opacity=.25,f=new eb.Mesh(f,h));var r=y.title;if(e.enabled&&""!=r){h=document.createElement("canvas");
h.width=256;h.height=32;let cb=h.getContext("2d");this.Yi||(this.Yi=document.createElement("canvas"),this.Yi.width=h.width,this.Yi.height=h.height);let ab=this.Yi.getContext("2d");ab.clearRect(0,0,h.width,h.height);ab.font="16px Helvetica";ab.textAlign="center";ab.textBaseline="top";var w=ab.measureText(r);let db=w.width;0<e.height&&(db=Math.min(e.width,h.width-6),db=Math.max(db,w.width));w=db+6;e.background&&(ab.fillStyle=this.o.Gb(e.fc,e.ec),ab.fillRect((h.width-db)/2-3,2,db+6,24));0<e.lj&&(ab.strokeStyle=
this.o.Gb(e.ic,e.hc),ab.strokeRect((h.width-db)/2-3,2,db+6,24));ab.fillStyle=this.o.Gb(e.Nk,e.Lk);ab.fillText(r,h.width/2,5,h.width-6);cb.drawImage(this.Yi,(h.width-w)/2,2,w,24,0,0,h.width,h.height);r=new eb.Texture(h);r.colorSpace=this.Dc;r.needsUpdate=!0;h=new eb.PlaneGeometry(1.5*w/h.width,4.5/h.height,1,1);r=new eb.MeshBasicMaterial({map:r,side:eb.DoubleSide});h=new eb.Mesh(h,r);h.translateY(-(n*this.Jl)-.09375);h.frustumCulled=this.frustumCulled;f.add(h);r.transparent=!0;r.opacity=.5}f.userData=
{O:y}}f&&(f.rotateY(y.pan*Math.PI/180),f.rotateX(y.u*Math.PI/180),f.translateZ(-z),f.castShadow=!1,f.receiveShadow=!1,f.frustumCulled=this.frustumCulled,b.add(f))}}js(){var a=this.o;for(var b=0;b<a.Ya.length;b++){var e=a.Ya[b];if(e.j){var f=(new eb.TextureLoader).load(e.j.src);f.colorSpace=this.Dc;this.yo(e,f)}}for(b=0;b<a.V.length;b++)e=a.V[b],e.j&&"_videopanorama"!=e.id&&!e.vd&&(e.j.play(),f=new eb.VideoTexture(e.j),f.colorSpace=this.Dc,f.wrapS=eb.ClampToEdgeWrapping,f.wrapT=eb.ClampToEdgeWrapping,
f.minFilter=eb.LinearFilter,this.yo(e,f))}yo(a,b){const e=this.pg;let f=Db/8;var h=2*Math.tan(a.fov*Math.PI/360)*f;let n;n=0<a.lc?h*a.$c/a.lc:h;a.Kc&&1!=a.Kc&&(n*=a.Kc);h=new eb.PlaneGeometry(h,n,5,5);b=new eb.MeshBasicMaterial({map:b,side:eb.DoubleSide,transparent:!0});b=new eb.Mesh(h,b);b.rotateY(a.pan*Math.PI/180);b.rotateX(a.u*Math.PI/180);b.translateY(-0);b.translateZ(-f);b.rotateZ(-a.Sa*Math.PI/180);b.rotateY(-a.Ca*Math.PI/180);b.rotateX(-a.ya*Math.PI/180);b.userData=a;b.castShadow=!1;b.receiveShadow=
!1;e.add(b)}Bk(a,b,e){for(let f=0;f<this.pg.children.length;f++){let h=this.pg.children[f];h.userData.id==a&&this.kv(h,b,e)}}kv(a,b,e){b=2==b?a.visible?0:1:b;0<e?this.Ns(a,b,e):a.visible=1==b}Ns(a,b,e){1==b&&(a.material.opacity=0,a.visible=!0);let f=(new Date).getTime(),h=setInterval(()=>{let n=(new Date).getTime()-f,r=n/e;a.material.opacity=0==b?1-r:r;n>e&&(0==b&&(a.visible=!1,a.material.opacity=1),clearInterval(h))},20)}Fh(a){for(let b=a.children.length-1;0<=b;b--){let e=a.children[b];this.Fh(e);
a.remove(e)}a.geometry&&a.geometry.dispose();a.material&&(a.material.texture&&(a.material.texture.dispose(),a.material.texture.He=null,a.material.texture.Sh=null),a.material.map&&(a.material.map.dispose(),a.material.map.He=null,a.material.map.Sh=null),a.material.dispose())}Uf(){eb&&(this.Mj=[],this.Fh(this.Hf),this.Fh(this.Id),this.Fh(this.pg))}Du(){if(eb){var a=this.camera;a.aspect=window.innerWidth/window.innerHeight;a.updateProjectionMatrix();this.xb.setSize(window.innerWidth,window.innerHeight)}}yq(){if(eb){var a=
this.o;if(0<this.Qa.length){for(var b=0,e;b<this.Qa.length&&!e;)this.Dm(this.Qa[b])&&(e=this.Qa[0]),b++;e&&(b=e.userData,b.O&&!b.fromSkin?(e=b.O,a.Rc(e.url,e.target)):(this.we(e,"onmousedown"),this.we(e,"onclick")))}}}xq(a){if(eb){a=a.target;var b=this.jp(a);0<b.length&&this.ij(b,a.userData.pj)}}jp(a){this.Vi.identity().extractRotation(a.matrixWorld);this.Ng.ray.origin.setFromMatrixPosition(a.matrixWorld);this.Ng.ray.direction.set(0,0,-1).applyMatrix4(this.Vi);return this.nm(this.Ng)}nm(a){let b=
[],e;this.Id.visible&&(e=a.intersectObjects(this.Id.children,!0),b=b.concat(e));this.$e.visible&&(e=a.intersectObjects(this.$e.children,!0),b=b.concat(e));e=a.intersectObjects(this.Cc.children,!0);return b=b.concat(e)}ij(a,b=-1){for(let e=0;e<a.length;e++){const f=a[e].object;f&&f.userData&&(void 0!==f.userData.isVisible&&f.userData.isVisible()||void 0===f.userData.isVisible&&this.Dm(f)||f.userData.clickInvisible&&this.Dm(f))&&(f.selected=!0,f.distance=a[e].distance,f.pj=b,this.Qa.push(f))}}au(a){var b=
this.controller[a],e=b.getObjectByName("line");let f=this.jp(b);0<f.length&&this.ij(f,b.userData.pj);e&&(e.scale.z=0<this.Qa.length?this.Qa[0].distance:5);this.Uj==a&&(a=this.Eq.direction,b=this.Ng.ray.direction,e=a.clone(),e.sub(b),e.normalize(),a.angleTo(b)>Math.PI/4?Math.abs(e.x)+Math.abs(e.z)>Math.abs(e.y)&&this.Hm<Date.now()&&(this.Qg(0<e.x?20:-20),this.Hm=Date.now()+500):this.Hm=Date.now())}to(){for(;this.Qa.length;)this.Qa.pop().selected=!1}yu(a){a.preventDefault();let b=this.ca,e=this.xb;
b.x=a.clientX/e.domElement.clientWidth*2-1;b.y=2*-(a.clientY/e.domElement.clientHeight)+1;1==a.buttons&&(this.li=this.bd.x-b.x)}$t(){var a=this.Ng;a.setFromCamera(this.ca,this.camera);a=this.nm(a);0<a.length&&this.ij(a)}bu(){var a=this.Ng;this.camera.updateProjectionMatrix();a.setFromCamera({x:0,y:0},this.camera);a=this.nm(a);0<a.length&&this.ij(a)}zu(){this.li=0;if(0<this.Qa.length){let a=this.Qa[0];a.userData.O||this.we(a,"onmouseup")}}we(a,b,e=!1){let f=a;do if(f.userData[b])if(e){var h=a.pj;0<=
h&&this.Vb&&this.Vb[h]&&(h=this.Vb[h],h.hapticActuators&&0<h.hapticActuators.length&&h.hapticActuators[0].pulse(.8,5))}else f.userData[b]();while(!f.userData.stopPropagation&&(f=f.parent))}ko(a,b){const e=this.rl;do if(a.userData.onclick||a.userData.O&&!a.userData.fromSkin)if(b)a.userData.Pi=-1;else if(a.userData.Pi&&-1!=a.userData.Pi){const f=(Date.now()-a.userData.Pi)/1E3;1<=f?a.userData.onclick?(a.userData.onclick(),a.userData.Pi=-1):a.userData.O&&a.userData.O.url&&this.o.Rc(a.userData.O.url,a.userData.O.target):
e.material instanceof eb.Material&&(e.material.opacity=.5+.5*f)}else a.userData.Pi=Date.now();while(!a.userData.stopPropagation&&(a=a.parent))}xu(a){var b=this.o;50>a.clientX&&50>a.clientY&&b.sg();if(0<this.Qa.length){a=this.Qa[0];for(b=1;b<this.Qa.length;b++)this.Qa[b].position.z>a.position.z&&(a=this.Qa[b]);b=a.userData;b.O?(a=b.O,this.o.Rc(a.url,a.target)):(this.we(a,"onclick"),this.we(a,"onmousedown"))}else this.bd.x=this.ca.x,this.bd.y=this.ca.y}animate(){let a=this.lq=this;this.xb&&this.xb.setAnimationLoop(function(){a.render();
a.o.Qr()})}render(){if(eb){var a=null;try{a=this.xb.xr.getCamera()}catch(n){}!this.Yk&&a&&(a.add(this.jg),this.vk.add(a));if(this.Yk=a)this.Id.translateY(a.position.y-this.Lj),this.Lj=a.position.y;var b=this.lq,e=b.o;if(e.ef){b.bg=!0;b.controller.forEach(n=>{n.visible&&(b.bg=!1)});var f=this.Rt();this.St();this.pk&&0<this.Mj.length&&this.Mj.every(function(n){return n.Kp})&&(this.Fh(this.pk),this.Hf.remove(this.pk),this.pk=null);var h=this.Id;for(let n=h.children.length-1;0<=n;n--){let r=h.children[n],
w=1,x=1;r.selected?(w=1.5,x=1):f?(w=.6,x=.7):(w=.4,x=.5);let y=r.scale.x;y+=.1*(w-y);r.userData.fromSkin?r.userData.ggUse3d&&(y=10):(r.material.opacity+=.1*(x-r.material.opacity),r.userData&&r.userData.O&&r.userData.O.od?(r.userData.O.ag&&(y=1,r.material.opacity=1),0<r.children.length&&(r.children[0].material.opacity=r.material.opacity)):(r.rotateY(1*Math.PI/180),0<r.children.length&&(r.children[0].rotateY(-1*Math.PI/180),r.children[0].material.opacity=r.material.opacity)));r.scale.x=r.scale.y=r.scale.z=
y}f=b.camera.rotation.clone();f.reorder("YXZ");this.Pn=180*f.y/Math.PI;this.Zk=180*f.x/Math.PI;e.vrSkinObj&&!this.$e.visible&&this.Io&&(0==this.Fk&&10<this.Zk||1==this.Fk&&-10>this.Zk?this.ek||(this.ek=!0,this.ju=new Date):(this.ek=!1,!this.ug&&!this.Cc.visible||this.yj||(this.yj=!0,this.Ol=new Date),this.ug=!1),this.ek&&!this.ug&&!this.Cc.visible&&500<(new Date).getTime()-this.ju.getTime()&&(this.ug=!0,this.Ol=new Date),this.ug?(this.Cc.visible=!0,h=((new Date).getTime()-this.Ol.getTime())/5,100<=
h?(this.Ii(1),this.ug=!1):this.Ii(h/100)):this.yj&&(h=((new Date).getTime()-this.Ol.getTime())/5,100<=h?(this.Ii(0),this.yj=!1):this.Ii(1-h/100)));this.an&&(this.Qg(this.Pn-e.D.pan.m),this.an=!1);e.kn(this.Pn-this.Rf,this.Zk,180*f.z/Math.PI);e=this.jg;b.bg?a&&(e.visible=!0):e.visible=!1;e=this.Cc;a&&!this.$e.visible&&(e.translateZ(-(this.Xf+.1)),e.rotateY(-this.Dn),f=new eb.Vector3(0,0,1),f.applyEuler(a.rotation),this.Dn=Math.atan2(f.x,f.z),e.rotateY(this.Dn),e.translateZ(this.Xf+.1));b.xb.autoClear=
!0;b.xb.render(b.vk,b.camera);0==this.li||isNaN(this.li)||this.Qg(this.li)}}}Rt(){function a(x,y){if(b.Vb[x].buttons[y].pressed){if(!b.ql.get(x+"_"+y))return b.ql.set(x+"_"+y,!0),!0}else b.ql.set(x+"_"+y,!1);return!1}let b=this;const e=b.o;this.Vb=[];let f=!0;this.session?(this.session.inputSources.forEach(x=>{let y=x.profiles;"gaze"==x.targetRayMode?b.bg=!0:(this.Vb.push(x.gamepad),0<=y.indexOf("oculus-go")&&(f=!1),0<=y.indexOf("generic-trigger-squeeze-thumbstick")&&(f=!0),0<=y.indexOf("oculus-touch")&&
(f=!0))}),this.Vb.push(...navigator.getGamepads())):this.Vb=navigator.getGamepads&&navigator.getGamepads();let h=!1;if(this.Vb)for(let x=0;x<this.Vb.length;x++){var n=this.Vb[x];if(n&&0<n.buttons.length)if(6<n.buttons.length&&(a(x,4)&&e.Rc("{"+e.zp()+"}"),a(x,5)&&e.Rc("{"+e.rp()+"}")),2<=n.axes.length&&2<n.buttons.length&&!f)if(n.buttons[2].pressed)this.Lf[x]||(this.Lf[x]=!0,this.le());else if(n.buttons[2].touched){h=!0;this.Lf[x]=!1;var r=n.axes[0];n=n.axes[1];if(-1==this.Oe[x])this.Oe[x]=r;else{var w=
this.Oe[x]-r;-.3>w&&(this.Qg(20),this.Oe[x]=r,this.Pe[x]=n);.3<w&&(this.Qg(-20),this.Oe[x]=r,this.Pe[x]=n)}-1==this.Pe[x]?this.Pe[x]=n:(w=this.Pe[x]-n,-.5>w&&(this.le(!0),this.Pe[x]=n,this.Oe[x]=r),.5<w&&(this.le(!1),this.Pe[x]=n,this.Oe[x]=r))}else this.Oe[x]=-1,this.Pe[x]=-1,this.Lf[x]=!1;else 4<=n.axes.length&&(r=n.axes[2],.8<r?(h=!0,1!=this.Kf[x]&&(this.Kf[x]=1,this.Zj[x]=Date.now()-1E4)):-.8>r?(h=!0,-1!=this.Kf[x]&&(this.Kf[x]=-1,this.Zj[x]=Date.now()-1E4)):this.Kf[x]=0,0!=this.Kf[x]&&400<Date.now()-
this.Zj[x]&&(this.Zj[x]=Date.now(),this.Qg(20*this.Kf[x])),n.buttons[1].pressed?this.Lf[x]||(this.Lf[x]=!0,this.le()):this.Lf[x]=!1)}b.oj.visible==(!this.Vb||0==this.Vb.length)&&(b.oj.visible=!b.oj.visible);return h}Ru(a){for(a=a.parent;a;){if(a.selected)return!0;a=a.parent}return!1}Dm(a){let b=a.visible||a.userData.clickInvisible;for(a=a.parent;b&&a;)b=b&&a.visible,a=a.parent;return b}St(){let a=this.lq;a.jk=a.Qa.slice();a.to();for(var b=0;b<a.controller.length;b++)a.controller[b].visible&&a.au(b);
a.Aj||a.$t();if(a.bg){a.bu();b=this.rl;b.position.z=0<a.Qa.length?-a.Qa[0].distance+.1:-5;var e=-.2*b.position.z;b.scale.set(e,e,e);b.material instanceof eb.Material&&(b.material.opacity=.2)}for(b=0;b<a.Qa.length;b++)e=a.Qa[b],a.bg&&this.ko(e,!1),-1==a.jk.indexOf(e)&&(this.we(e,"onmouseenter"),this.Vb&&0<this.Vb.length&&this.we(e,"onclick",!0));for(b=0;b<a.jk.length;b++)e=a.jk[b],-1==a.Qa.indexOf(e)&&(this.Ru(e)||this.we(e,"onmouseleave"),a.bg&&this.ko(e,!0))}le(a){let b=this.$e;void 0===a&&(a=!b.visible);
var e=this.Yk;a?(b.translateZ(-this.Xf),b.rotateY(-this.mn),e&&(a=new eb.Vector3(0,0,1),a.applyEuler(e.rotation),this.mn=Math.atan2(a.x,a.z)),b.rotateY(this.mn),b.translateZ(this.Xf),e=-this.Xf/5,b.scale.set(e,e,e),this.$e.visible=!0,this.Cc.visible=!0,this.Ii(1),this.Wf.visible=!1,this.Cg.visible=!0):(this.$e.visible=!1,this.Cc.visible=!1,this.Wf.visible=!0,this.Cg.visible=!1)}Ii(a){this.Wf.userData.setOpacity(a);0==a&&(this.Cc.visible=!1)}Pq(a){a.position.x=0;a.position.y=0;a.position.z=0;a.userData.x=
0;a.userData.y=0;a.userData.z=0}rv(a){this.Pq(a);this.Cc.remove(this.Wf);a.userData.setOpacity(0);this.Wf=a;this.Cc.add(this.Wf)}ev(a){this.Pq(a);this.Cc.remove(this.Cg);a.userData.setOpacity(1);a.visible=!1;this.Cg=a;this.Cc.add(this.Cg)}Xu(){this.Rf=0;this.Te.setRotationFromEuler(this.vq)}Qg(a){this.Rf+=a;this.Te.rotateY(a*Math.PI/180)}Wq(a,b){a&&(this.Dc=a);b&&(this.Rn=b)}}class Ab{constructor(){this.value=this.time=0;this.Nn="";this.qf=this.pf=this.nf=this.mf=this.sb=this.type=this.Kb=0;this.Ad=
"";this.Zn=0}}class Tb{constructor(){this.aw=this.os=this.length=0}}class Ub{}class Cb{Zq(){this.sc=1;this.Pc=this.tc=this.Nb=0;this.uc=1;this.vc=this.Zb=this.Qc=0;this.$b=1}constructor(){this.Zq()}clone(a){this.sc=a.sc;this.Nb=a.Nb;this.tc=a.tc;this.Pc=a.Pc;this.uc=a.uc;this.Qc=a.Qc;this.Zb=a.Zb;this.vc=a.vc;this.$b=a.$b}ov(a){let b=Math.cos(a);a=Math.sin(a);this.sc=1;this.Pc=this.tc=this.Nb=0;this.uc=b;this.Qc=-a;this.Zb=0;this.vc=a;this.$b=b}pv(a){let b=Math.cos(a);a=Math.sin(a);this.sc=b;this.Nb=
0;this.tc=a;this.Pc=0;this.uc=1;this.Qc=0;this.Zb=-a;this.vc=0;this.$b=b}qv(a){let b=Math.cos(a);a=Math.sin(a);this.sc=b;this.Nb=-a;this.tc=0;this.Pc=a;this.uc=b;this.vc=this.Zb=this.Qc=0;this.$b=1}bc(a){this.ya(a*Math.PI/180)}Sc(a){this.Ca(a*Math.PI/180)}ke(a){this.Sa(a*Math.PI/180)}ya(a){this.ud||(this.ud=new Cb,this.Re=new Cb);this.ud.ov(a);this.Re.clone(this);this.multiply(this.ud,this.Re)}Ca(a){this.ud||(this.ud=new Cb,this.Re=new Cb);this.ud.pv(a);this.Re.clone(this);this.multiply(this.ud,this.Re)}Sa(a){this.ud||
(this.ud=new Cb,this.Re=new Cb);this.ud.qv(a);this.Re.clone(this);this.multiply(this.ud,this.Re)}multiply(a,b){this.sc=a.sc*b.sc+a.Nb*b.Pc+a.tc*b.Zb;this.Nb=a.sc*b.Nb+a.Nb*b.uc+a.tc*b.vc;this.tc=a.sc*b.tc+a.Nb*b.Qc+a.tc*b.$b;this.Pc=a.Pc*b.sc+a.uc*b.Pc+a.Qc*b.Zb;this.uc=a.Pc*b.Nb+a.uc*b.uc+a.Qc*b.vc;this.Qc=a.Pc*b.tc+a.uc*b.Qc+a.Qc*b.$b;this.Zb=a.Zb*b.sc+a.vc*b.Pc+a.$b*b.Zb;this.vc=a.Zb*b.Nb+a.vc*b.uc+a.$b*b.vc;this.$b=a.Zb*b.tc+a.vc*b.Qc+a.$b*b.$b}pu(a){let b,e,f;b=a.x;e=a.y;f=a.z;a.x=b*this.sc+
e*this.Nb+f*this.tc;a.y=b*this.Pc+e*this.uc+f*this.Qc;a.z=b*this.Zb+e*this.vc+f*this.$b}}class qd{constructor(){this.levels=[];this.nk="0x000000";this.Hq=!1;this.iq=this.hq=.4;this.W=512;this.ib=1;this.wm=0;this.xm="";this.Mq=this.height=this.width=0}}class Vb{constructor(){this.height=this.width=0;this.Mg=this.cache=!1;this.sa=this.$=0;this.loaded=!1;this.Oa={}}}class Hb{constructor(a){this.id=0;this.Yd=this.ub=this.M=null;this.loaded=this.visible=!1;this.Ef=this.Fb=null;this.Ve=this.Vm=0;this.yg=
[];this.fm=0;this.mb=[];this.ak=this.Pa=0;this.level=null;this.Xj=0;this.id=a}}class Wb{constructor(a,b){this.Aa=kb.create();this.Za=kb.create();this.de=0;this.Ai=this.nd=this.xe=this.rj=this.wg=this.Bi=this.Wm=this.Xm=null;this.fb=[];this.hb=null;this.$h=!1;this.vn=this.Ql=this.Um=1;this.ng=1E6;this.qj=[!1,!1,!1,!1,!1,!1];this.En=0;this.il=1;this.Gk=!1;this.Il=[];this.Gh=!1;this.Qn=8;this.ku=new Cb;this.offset=this.Gn=0;this.ze=[];this.On=null;this.o=a;this.D=b;b.Ig=this;if(a.Lc||a.Wh)b.Cn=2}Vh(){const a=
this.o;let b=a.N;if(b){var e=b.createShader(b.FRAGMENT_SHADER);if(e){b.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\n\t\t\t\t\tuniform sampler2D uSampler;\n\t\t\t\t\tvoid main(void) {\n\t\t\t\t\t\tgl_FragColor = texture2D(uSampler, vec2(vTextureCoord.s, vTextureCoord.t));\n\t\t\t\t\t}");b.compileShader(e);b.getShaderParameter(e,b.COMPILE_STATUS)||(console&&console.log(b.getShaderInfoLog(e)),alert(b.getShaderInfoLog(e)),
e=null);var f=b.createShader(b.VERTEX_SHADER);a.Jc(f,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\n\t\t\t\tattribute vec2 aTextureCoord;\n\t\t\t\tuniform mat4 uMVMatrix;\n\t\t\t\tuniform mat4 uPMatrix;\n\t\t\t\tuniform float uZoffset;\n\t\t\t\tvarying vec2 vTextureCoord;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tgl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n\t\t\t\t\tgl_Position.z += uZoffset;\n\t\t\t\t\tvTextureCoord = aTextureCoord;\n\t\t\t\t}");
this.R=b.createProgram();a.Nf(this.R,f,e);this.R.la=b.getAttribLocation(this.R,"aVertexPosition");b.enableVertexAttribArray(this.R.la);this.R.Ea=b.getAttribLocation(this.R,"aTextureCoord");b.enableVertexAttribArray(this.R.Ea);this.R.Se=b.getUniformLocation(this.R,"uPMatrix");this.R.ni=b.getUniformLocation(this.R,"uMVMatrix");this.R.Rg=b.getUniformLocation(this.R,"uSampler");this.R.hl=b.getUniformLocation(this.R,"uZoffset");e=b.createShader(b.VERTEX_SHADER);a.Jc(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\n\t\t\t\tuniform vec2 uCanvasDimensions;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tvec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n\t\t\t\t\tgl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, 0.0, 1.0);\n\t\t\t\t}");
f=b.createShader(b.FRAGMENT_SHADER);a.Jc(f,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nuniform vec3 uColor;\n\t\t\t\tuniform float uAlpha;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tgl_FragColor = vec4(uColor, uAlpha);\n\t\t\t\t}");this.ie=b.createProgram();a.Nf(this.ie,e,f);this.ie.la=b.getAttribLocation(this.ie,"aVertexPosition");b.enableVertexAttribArray(this.ie.la);f=b.createShader(b.VERTEX_SHADER);a.Jc(f,"precision highp float;\n\t\t\t\tattribute vec3 aVertexPosition;\n\t\t\t\tvarying vec2 vTextureCoord;\n\t\t\t\tuniform vec2 uCanvasDimensions;\n\t\t\t\tuniform vec4 uRect;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tvec2 pos = vec2(uRect.x + uRect.z*aVertexPosition.x,uRect.y + uRect.w*aVertexPosition.y);\n\t\t\t\t\tvec2 pointNorm = (pos / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n\t\t\t\t\tgl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, 1.0, 1.0);\n\t\t\t\t\tvTextureCoord.s=aVertexPosition.x;\n\t\t\t\t\tvTextureCoord.t=1.0-aVertexPosition.y;\n\t\t\t\t}");
e=b.createShader(b.FRAGMENT_SHADER);a.Jc(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec2 vTextureCoord;\n\t\t\t\tuniform sampler2D uSampler;\n\t\t\t\tvoid main(void) {\n\t\t\t\t\tgl_FragColor = texture2D(uSampler,vTextureCoord);\n\t\t\t\t}");this.wg=b.createProgram();a.Nf(this.wg,f,e)}else console.log("error creating shader")}}Pj(){let a=this.o,b=a.N,e;if(b){e=b.createShader(b.FRAGMENT_SHADER);var f=this.am(13);a.Jc(e,f);f=b.createShader(b.VERTEX_SHADER);
a.Jc(f,"precision highp float;\nattribute vec3 aVertexPosition;\nuniform vec2 uCanvasDimensions;\nvarying vec2 dst;\nuniform vec2 dstSize;\nuniform float zOffset;\nvoid main(void) {\n vec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, zOffset, 1.0);\n dst.x= -1.0 + 2.0*((aVertexPosition.x + 0.5) / uCanvasDimensions.x);\n dst.y= (-1.0 * uCanvasDimensions.y + 2.0*(aVertexPosition.y + 0.5)) / uCanvasDimensions.x;\n}\n");
this.Wm=b.createProgram();a.Nf(this.Wm,f,e);e=b.createShader(b.FRAGMENT_SHADER);f=this.am(4);a.Jc(e,f);f=b.createShader(b.VERTEX_SHADER);a.Jc(f,"precision highp float;\nattribute vec3 aVertexPosition;\nuniform vec2 uCanvasDimensions;\nvarying vec2 dst;\nuniform vec2 dstSize;\nuniform float zOffset;\nvoid main(void) {\n vec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, zOffset, 1.0);\n dst.x= -1.0 + 2.0*((aVertexPosition.x + 0.5) / uCanvasDimensions.x);\n dst.y= (-1.0 * uCanvasDimensions.y + 2.0*(aVertexPosition.y + 0.5)) / uCanvasDimensions.x;\n}\n");
this.Xm=b.createProgram();a.Nf(this.Xm,f,e);this.Ao();this.Ai||(this.Ai=b.createBuffer())}}Ao(){const a=this.o,b=a.N;const e=b.createShader(b.FRAGMENT_SHADER);var f=this.am(a.B.format);a.Jc(e,f);f=b.createShader(b.VERTEX_SHADER);a.Jc(f,"precision highp float;\nattribute vec3 aVertexPosition;\nuniform vec2 uCanvasDimensions;\nvarying vec2 dst;\nuniform vec2 dstSize;\nvoid main(void) {\n vec2 pointNorm = (aVertexPosition.xy / uCanvasDimensions) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(pointNorm.x, pointNorm.y * -1.0, 0.0, 1.0);\n dst.x= -1.0 + 2.0*((aVertexPosition.x + 0.5) / uCanvasDimensions.x);\n dst.y= (-1.0 * uCanvasDimensions.y + 2.0*(aVertexPosition.y + 0.5)) / uCanvasDimensions.x;\n}\n");
this.Bi&&b.deleteProgram(this.Bi);this.Bi=b.createProgram();a.Nf(this.Bi,f,e);b.deleteShader(f);b.deleteShader(e)}am(a){let b=this.o,e;e="#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\n#define M_PI 3.14159265358979323846\nvarying vec2 dst;\nuniform vec2 srcScale;\nuniform vec2 srcOffset;\nuniform float rectDstDistance;\nuniform float fisheyeDistance;\nuniform float stereoDistance;\nuniform float directionBlend;\nuniform float transitionBlend;\nuniform mat4 matRotate; // = mat4( 1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0 );\nconst float rectSrcDistance = 1.0;\nuniform vec2 tonemap;\n";
e=(13==a?e+"uniform samplerCube cubeTexture;":e+"uniform sampler2D tileTexture;\n")+"void main()\n{\n";e+="vec4 direction;\n";e+="vec2 src;\n";e+="vec2 srcCord;\n";e+="vec2 texc;\n";let f=this.Dp(b.Z());if(b.Z()!=b.Ic()&&0!=b.Ic()){let h=this.Dp(b.Ic());e+="vec4 direction1,direction2;\n";e+=f.replace("direction=","direction1=");e+=h.replace("direction=","direction2=");e+="direction=normalize(mix(direction1, direction2,1.0-directionBlend));\n"}else e+=f;e+="direction=direction*matRotate;\n";13==a&&
(e+="direction.z=-direction.z;",e+="gl_FragColor = textureCube(cubeTexture, direction.xyz);",e+="gl_FragColor.a *= transitionBlend;\n");4==a&&(e+="float iz=1.0/(direction.z * rectSrcDistance);\n",e+="src.x=-direction.x*iz;\n",e+="src.y= direction.y*iz;\n",e+="texc=src * srcScale + srcOffset;\n",e+="if (",e+="(direction.z<0.0) && ",e+="(texc.x>=0.0) && (texc.x<=1.0) && (texc.y>=0.0) && (texc.y<=1.0)) {\n",this.Gh?e+=" gl_FragColor = vec4(0.5,0.5,0,1);\n":(e+="  gl_FragColor = texture2D(tileTexture, texc);\n",
e+="  gl_FragColor.a*= transitionBlend;\n"),e+="} else {\n",e=this.Gh?e+" gl_FragColor = vec4(0.5,0,0,0.3);\n":e+"  discard;\n",e+="}\n");1==a&&(e+="src.x=atan(float(-direction.x), float(-direction.z));",e+="src.y=asin(direction.y);\n",e+="texc=src * srcScale + srcOffset;\n",e+="gl_FragColor = texture2D(tileTexture, texc);\n");15==a&&(e+="src.x=atan(float(-direction.x), float(-direction.z));",e+="src.y=asin(direction.y);\n",e+="texc=src * srcScale + srcOffset;\n",e=b.pc?e+"vec2 s=step(vec2(0.5,0.0), texc) - step(vec2(1.0,1.0), texc);\n":
e+"vec2 s=step(vec2(0.0,0.0), texc) - step(vec2(1.0,1.0), texc);\n",e+="gl_FragColor = s.x*s.y*texture2D(tileTexture, texc);\n");14==a&&(e+="vec2 cf;\n",e+="if ((direction.z<0.0) && (direction.z<=-abs(direction.x)) && (direction.z<=-abs(direction.y))) {\n",e+="  src.x=-direction.x/direction.z;\n",e+="  src.y=+direction.y/direction.z;\n",e+="  cf.x=1.0;cf.y=3.0;\n",e+="}\n",e+="if ((direction.x>=0.0) && (direction.x>=abs(direction.y)) && (direction.x>=abs(direction.z))) {\n",e+="  src.x=+direction.z/direction.x;\n",
e+="  src.y=-direction.y/direction.x;\n",e+="  cf.x=3.0;cf.y=3.0;\n",e+="}\n",e+="if ((direction.z>=0.0) && (direction.z>=abs(direction.x)) && (direction.z>=abs(direction.y))) {\n",e+="  src.x=-direction.x/direction.z;\n",e+="  src.y=-direction.y/direction.z;\n",e+="  cf.x=5.0;cf.y=3.0;\n",e+="}\n",e+="if ((direction.x<=0.0) && (direction.x<=-abs(direction.y)) && (direction.x<=-abs(direction.z))) {\n",e+="  src.x=+direction.z/direction.x;\n",e+="  src.y=+direction.y/direction.x;\n",e+="  cf.x=1.0;cf.y=1.0;\n",
e+="}\n",e+="if ((direction.y>=0.0) && (direction.y>=abs(direction.x)) && (direction.y>=abs(direction.z))) {\n",e+="  src.x=+direction.x/direction.y;\n",e+="  src.y=-direction.z/direction.y;\n",e+="  cf.x=5.0;cf.y=1.0;\n",e+="}\n",e+="if ((direction.y<=0.0) && (direction.y<=-abs(direction.x)) && (direction.y<=-abs(direction.z))) {\n",e+="  src.x=-direction.x/direction.y;\n",e+="  src.y=-direction.z/direction.y;\n",e+="  cf.x=3.0;cf.y=1.0;\n",e+="}\n",e+="texc.x=(cf.x+src.x*srcScale.x) / 6.0;\n",e+=
"texc.y=(cf.y+src.y*srcScale.y) / 4.0;\n",e+="gl_FragColor = texture2D(tileTexture, texc);\n");return e+="}\n"}Dp(a){let b="";switch(a){case 4:b+="direction.x=dst.x*rectDstDistance;\ndirection.y=dst.y*rectDstDistance;\ndirection.z=-1.0;\n";break;case 12:b+="float r,ph,ro;\nr=length(dst.xy)*0.5;\nro=atan(float(dst.x),float(-dst.y));\nph=r / fisheyeDistance;\ndirection.x= sin(ph) * sin(ro);\ndirection.y=-sin(ph) * cos(ro);\ndirection.z=-cos(ph);\n";break;case 9:b+="float n;\nvec2 ind;\nind=dst*stereoDistance;\nn=1.0 + ind.x*ind.x + ind.y*ind.y;\ndirection.x=2.0*ind.x/n;\ndirection.y=2.0*ind.y/n;\ndirection.z=(n-2.0)/n;\n"}return b+
"direction.w=0.0;\ndirection=normalize(direction);\n"}Tp(a){let b,e,f,h,n=this.o,r=this.o.N;this.rj=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,this.rj);let w=[-1,-1,1,1,-1,1,1,1,1,-1,1,1];for(b=0;12>b;b++)2>b%3&&(w[b]*=a);r.bufferData(r.ARRAY_BUFFER,new Float32Array(w),r.STATIC_DRAW);this.xe=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,this.xe);let x=[1,0,0,0,0,1,1,1];r.bufferData(r.ARRAY_BUFFER,new Float32Array(x),r.STATIC_DRAW);this.nd=r.createBuffer();r.bindBuffer(r.ELEMENT_ARRAY_BUFFER,this.nd);
let y=[0,1,2,0,2,3];r.bufferData(r.ELEMENT_ARRAY_BUFFER,new Uint16Array(y),r.STATIC_DRAW);w=[];y=[];x=[];let z=new lb;for(a=0;6>a;a++){f=a%3;h=3>a?1:0;for(e=0;4>e;e++){z.x=-1;z.y=-1;z.z=1;for(b=0;b<e;b++)z.Sq();x.push((0>z.x?.33:0)+.33*f,(0>z.y?0:.5)+.5*h);if(4>a)for(b=0;b<a;b++)z.av();else 5==a?z.$u():z.Zu();w.push(z.x,z.y,z.z)}b=4*a;y.push(0+b,1+b,2+b,0+b,2+b,3+b)}n.B.Vk=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,n.B.Vk);r.bufferData(r.ARRAY_BUFFER,new Float32Array(w),r.STATIC_DRAW);n.B.Wi=r.createBuffer();
r.bindBuffer(r.ARRAY_BUFFER,n.B.Wi);r.bufferData(r.ARRAY_BUFFER,new Float32Array(x),r.STATIC_DRAW);n.B.Oj=r.createBuffer();r.bindBuffer(r.ELEMENT_ARRAY_BUFFER,n.B.Oj);r.bufferData(r.ELEMENT_ARRAY_BUFFER,new Uint16Array(y),r.STATIC_DRAW);this.Qt=r.createBuffer();this.Pt=r.createBuffer()}hm(a){let b=this;return function(){try{if(a.vu)return;let e=b.o.N;e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,1);let f=!1;null!=a.Of&&a.Of.complete?a.Op||(e.bindTexture(e.TEXTURE_2D,a),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,
e.UNSIGNED_BYTE,a.Of),f=a.Op=!0):null!=a.Tf&&a.Tf.complete&&(e.bindTexture(e.TEXTURE_2D,a),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,a.Tf),f=!0);f&&(a.loaded=!0,b.o.F.ed&&("previewtrack"==b.o.F.delay||"videopano"==b.o.F.delay&&!b.o.B.j)&&b.Gq()&&b.o.Jk(null));e.bindTexture(e.TEXTURE_2D,null);e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,0)}catch(e){}b.o.update(2)}}Vp(){let a=this.o,b=a.N;if(this.fb)for(;0<this.fb.length;)b.deleteTexture(this.fb.pop());this.fb=[];for(let h=0;6>h;h++){var e=b.createTexture();
this.de++;e.Tf=null;e.Of=null;e.Op=!1;b.bindTexture(b.TEXTURE_2D,e);b.texImage2D(b.TEXTURE_2D,0,b.RGBA,1,1,0,b.RGBA,b.UNSIGNED_BYTE,null);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_MIN_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);if(a.Bh[h]){var f=new Image;f.crossOrigin=a.crossOrigin;f.src=a.Ha(a.Bh[h]);e.Tf=f;f.addEventListener&&f.addEventListener("load",this.hm(e),!1);a.md.push(f)}this.fb.push(e)}for(e=
0;6>e;e++)a.Ah[e]&&(f=new Image,f.crossOrigin=a.crossOrigin,f.src=a.Ha(a.Ah[e]),f.addEventListener?f.addEventListener("load",this.hm(this.fb[e]),!1):f.onload=this.hm(this.fb[e]),this.fb[e].Of=f,a.md.push(f));for(f=0;f<a.V.length;f++)a.V[f].vd||(a.V[f].texture=b.createTexture(),a.de++,b.bindTexture(b.TEXTURE_2D,a.V[f].texture),b.texImage2D(b.TEXTURE_2D,0,b.RGB,1,1,0,b.RGB,b.UNSIGNED_BYTE,null));a.B.texture=b.createTexture();a.de++;b.bindTexture(b.TEXTURE_2D,a.B.texture);b.texImage2D(b.TEXTURE_2D,0,
b.RGB,1,1,0,b.RGB,b.UNSIGNED_BYTE,null);b.bindTexture(b.TEXTURE_2D,null)}rw(){var a=this.o;if(a.N){var b=a.N;b.useProgram(this.R);this.Wg(0);b.uniform1i(this.R.Rg,0);b.enableVertexAttribArray(this.R.la);b.enableVertexAttribArray(this.R.Ea);b.bindBuffer(b.ARRAY_BUFFER,this.xe);b.vertexAttribPointer(this.R.Ea,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.nd);b.uniform1f(this.R.hl,1E-4);b.vertexAttribPointer(this.R.la,3,b.FLOAT,!1,0,0);kb.identity(this.Za);kb.perspective(a.Eb(),
a.Xa.width/a.Xa.height,.1,100,this.Za);b.uniformMatrix4fv(this.R.Se,!1,this.Za);for(a=0;6>a;a++)this.Wg(a),b.bindBuffer(b.ARRAY_BUFFER,this.rj),b.vertexAttribPointer(this.R.la,3,b.FLOAT,!1,0,0),b.bindBuffer(b.ARRAY_BUFFER,this.xe),b.vertexAttribPointer(this.R.Ea,2,b.FLOAT,!1,0,0),6<=this.fb.length&&this.fb[a].loaded&&(b.activeTexture(b.TEXTURE0),b.bindTexture(b.TEXTURE_2D,this.fb[a]),b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.nd),b.uniform1i(this.R.Rg,0),b.uniformMatrix4fv(this.R.ni,!1,this.Aa),b.uniformMatrix4fv(this.R.Se,
!1,this.Za),b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0))}}Or(){let a=this.o;var b=this.D;b.M.nk&&6<b.M.nk.length&&(b=parseInt(b.M.nk),a.N.clearColor((b>>16&255)/255,(b>>8&255)/255,(b>>0&255)/255,1))}Wg(a,b=1){const e=this.D;kb.identity(this.Aa);kb.rotate(this.Aa,b*-e.Y.m*Math.PI/180,[0,0,1]);kb.rotate(this.Aa,b*-e.u.m*Math.PI/180,[1,0,0]);-1==b?kb.rotate(this.Aa,-e.pan.m*Math.PI/180,[0,1,0]):kb.rotate(this.Aa,(180-e.pan.m)*Math.PI/180,[0,1,0]);e.qb&&(kb.rotate(this.Aa,-e.qb.pitch*Math.PI/180,
[1,0,0]),kb.rotate(this.Aa,e.qb.Y*Math.PI/180,[0,0,1]));4>a?kb.rotate(this.Aa,-Math.PI/2*a,[0,1,0]):kb.rotate(this.Aa,Math.PI/2*(5==a?1:-1),[1,0,0])}Rv(a){let b=this;return function(){b.Il.push(a)}}Is(a){function b(){a.Fb=f.createTexture();e.o.de++;f.pixelStorei(f.UNPACK_FLIP_Y_WEBGL,1);f.bindTexture(f.TEXTURE_2D,a.Fb);try{f.texImage2D(f.TEXTURE_2D,0,f.RGBA,f.RGBA,f.UNSIGNED_BYTE,a.M)}catch(h){f.texImage2D(f.TEXTURE_2D,0,f.RGBA,1,1,0,f.RGBA,f.UNSIGNED_BYTE,new Uint8Array([128,128,128,250]))}}let e=
this;e.o.Ma=!0;e.o.Eh=!0;a.loaded=!0;a.Vm=0;a.Ve=0;let f=e.o.N;this.Eo();null!=a.M&&a.M.complete&&("decode"in a.M?a.M.decode().then(()=>{a.M&&(a.ub=document.createElement("canvas"),a.ub.width=a.M.width,a.ub.height=a.M.height,a.Yd=a.ub.getContext("2d"),a.Yd&&a.Yd.drawImage(a.M,0,0),b(),a.Yd&&(delete a.Yd,a.Yd=null))}):b());e.o.update(2)}Eo(){this.D.Xi&&this.D.Xi--}Js(){let a=2;for(;0<this.Il.length&&0<a;){let b=this.Il.shift();this.Is(b);a--}}qu(a){const b=this,e=b.o;return function(){e.Ma=!0;e.Eh=
!0;const f=b.D.M;try{if(null!=a&&a.complete){let h=f.levels[f.levels.length-1],n=f.ib;h.height=h.width=a.width-2*n;h.$=h.sa=1;for(let r=0;6>r;r++){let w=new Hb(r);w.ub=document.createElement("canvas");b.o.pa?(w.ub.width=h.width+2*n,w.ub.height=h.height+2*n):(w.ub.width=f.W+2*n,w.ub.height=f.W+2*n);w.Yd=w.ub.getContext("2d");w.ub.style[b.o.Be+"Origin"]="0% 0%";w.ub.style.overflow="hidden";w.ub.style.position="absolute";w.M=a;let x=h.width+2*n,y=h.height+2*n;w.Yd&&w.Yd.drawImage(a,0,r*y,x,y,0,0,x,y);
if(b.o.pa&&b.o.N){let z=b.o.N;z.pixelStorei(z.UNPACK_FLIP_Y_WEBGL,1);w.Fb=z.createTexture();b.o.de++;z.bindTexture(z.TEXTURE_2D,w.Fb);try{z.texImage2D(z.TEXTURE_2D,0,z.RGBA,z.RGBA,z.UNSIGNED_BYTE,w.ub)}catch(cb){}z.bindTexture(z.TEXTURE_2D,null);z.pixelStorei(z.UNPACK_FLIP_Y_WEBGL,0)}b.o.jh&&(w.ub.Jw=-1,b.o.ia.insertBefore(w.ub,b.o.ia.firstChild));h.Oa[r]=w}h.loaded=!0;b.o.F.ed&&("previewtrack"==b.o.F.delay||"videopano"==b.o.F.delay&&!b.o.B.j)&&b.o.Jk(null)}}catch(h){}b.o.update(2)}}Hr(a){let b=this;
return function(){b.o.Ma=!0;b.o.Eh=!0;b.Eo();a.M=null}}pw(){let a=this.o,b=this.D;var e=b.M;let f=e.levels;if(a.N){var h=a.N;h.useProgram(this.R);h.enable(h.DEPTH_TEST);kb.identity(this.Za);kb.perspective(b.Eb(),a.Xa.width/a.Xa.height,.1,100,this.Za);h.uniformMatrix4fv(this.R.Se,!1,this.Za);b.dw();b.kq();var n=b.Po();var r=f.length-1;for(b.pe=0;r>=n;){var w=f[r],x=1;r==f.length-1&&0==e.ib&&(x=e.W/(e.W-.5));for(var y=0;6>y;y++){var z=void 0;z=b.cube.yf[y];var cb=z.si;if(z.Xb&&0<cb.fj&&0<cb.$k&&0<cb.scale||
w.cache){z.Ma=!1;var ab=void 0;z.bounds[r]||(z.bounds[r]={fx:0,fy:0,Kd:0,Ld:0});ab=z.bounds[r];w.cache?(ab.fx=0,ab.fy=0,ab.Kd=w.$-1,ab.Ld=w.sa-1):b.Ct(w,cb,ab);cb=!0;for(var db=ab.fy;db<=ab.Ld;db++)for(let bb=ab.fx;bb<=ab.Kd;bb++){var gb=bb+db*w.$+y*w.$*w.sa;let fb=w.Oa[gb];fb||(fb=w.Oa[gb]=new Hb(gb));this.xj()?fb.M||(fb.Ve?fb.Ve--:(this.ck(fb,w,b.bh(y,r,bb,db)),a.Ma=!0)):b.pe++;if(fb.Fb){if(!fb.Ef){gb=.5*r+1;fb.Ef=h.createBuffer();h.bindBuffer(h.ARRAY_BUFFER,fb.Ef);let ib=[-1,-1,1,1,-1,1,1,1,1,
-1,1,1];ib[3]=bb*e.W-e.ib;ib[0]=Math.min((bb+1)*e.W,w.width)+e.ib;ib[7]=db*e.W-e.ib;ib[1]=Math.min((db+1)*e.W,w.height)+e.ib;ib[4]=ib[1];ib[6]=ib[3];ib[9]=ib[0];ib[10]=ib[7];for(let hb=0;12>hb;hb++)ib[hb]=0==hb%3?x*gb*(-2*ib[hb]/w.width+1):1==hb%3?x*gb*(-2*ib[hb]/w.height+1):gb;h.bufferData(h.ARRAY_BUFFER,new Float32Array(ib),h.STATIC_DRAW)}}else cb=!1;fb.visible=z.Xb}ab.gq=cb}}r--}for(e=0;6>e;e++)if(w=b.cube.yf[e],w.Xb)for(x=w.si,this.Wg(e),h.uniform1i(this.R.Rg,0),h.uniformMatrix4fv(this.R.Se,!1,
this.Za),h.uniformMatrix4fv(this.R.ni,!1,this.Aa),h.enableVertexAttribArray(this.R.la),h.enableVertexAttribArray(this.R.Ea),h.bindBuffer(h.ARRAY_BUFFER,this.xe),h.vertexAttribPointer(this.R.Ea,2,h.FLOAT,!1,0,0),h.activeTexture(h.TEXTURE0),h.bindBuffer(h.ELEMENT_ARRAY_BUFFER,this.nd),h.useProgram(this.R),r=n;r<=f.length-1;){y=f[r];if(w.Xb&&0<x.fj&&w.bounds[r]&&0<=w.bounds[r].fx){z=w.bounds[r];for(ab=z.fy;ab<=z.Ld;ab++)for(cb=z.fx;cb<=z.Kd;cb++)(db=y.Oa[cb+ab*y.$+e*y.$*y.sa])&&db.Fb&&(h.uniform1f(this.R.hl,
1E-4*(cb%2+ab%2*2)),h.bindBuffer(h.ARRAY_BUFFER,db.Ef),h.vertexAttribPointer(this.R.la,3,h.FLOAT,!1,0,0),h.bindTexture(h.TEXTURE_2D,db.Fb),this.Ni(h),h.drawElements(h.TRIANGLES,6,h.UNSIGNED_SHORT,0)),db.visible=w.Xb;z.gq&&(r=f.length)}r++}this.Zm();a.Eh=!1}}Ni(a){a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MAG_FILTER,a.LINEAR);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MIN_FILTER,a.LINEAR);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_WRAP_S,a.CLAMP_TO_EDGE);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_WRAP_T,a.CLAMP_TO_EDGE)}xj(){return this.D.Xi<
this.D.Cn}ck(a,b,e){let f=this.D,h=this.o;f.Wt++;a.M=new Image;a.Vm++;a.Ve=1<<a.Vm;a.M.onload=this.Rv(a);a.M.onerror=this.Hr(a);a.M.onabort=this.Hr(a);a.M.crossOrigin=h.crossOrigin;a.M.setAttribute("src",e);b.cache&&h.md.push(a.M);f.Xi++}fs(){let a=this.o,b=this.D;if(a.N){var e=a.N;this.Or();e.disable(e.CULL_FACE);e.bindBuffer(e.ARRAY_BUFFER,this.Ai);var f=[0,0];f[2]=a.H.width;f[3]=0;f[4]=a.H.width;f[5]=a.H.height;f[6]=0;f[7]=a.H.height;e.bufferData(e.ARRAY_BUFFER,new Float32Array(f),e.STATIC_DRAW);
b.pe=0;this.Dl();a.B.Mc?this.Iu():(e.enable(e.DEPTH_TEST),e.depthRange(0,1),e.depthFunc(e.LESS),this.Gk=!1,0<this.D.M.levels.length&&this.qw(),this.hb&&!this.Gk&&this.Hu())}}Dl(){const a=this.D.M;if(!this.hb||this.$h)0<a.levels.length?this.Gs():this.Hs()}Gv(a,b,e,f,h,n,r){const w=this.o;var x=this.D;h=x.M;let y=w.H;var z=e*h.W/b.width,cb=(e+1)*h.W/b.width;e=f*h.W/b.height;b=(f+1)*h.W/b.height;h=.1;r&&(h=.2);this.Gh&&(h=0);f=(cb-z)*h;1<cb&&(cb=1);1<b&&(b=1);z-=f;e-=f;h=this.Qn;r=(cb+f-z)/h;cb=(b+f-
e)/h;b={x:0,y:0};f={x:0,y:0};a.fm=0;let ab=w.Fj(),db=new lb,gb=this.ku;gb.Zq();4>n?gb.Sc(-90*n):gb.bc(5==n?90:-90);x.qb&&(gb.ke(x.qb.Y),gb.bc(-x.qb.pitch));gb.Sc(-x.pan.m);gb.bc(x.u.m);gb.ke(x.Y.m);for(n=0;n<=h;n++)for(x=0;x<=h;x++){var bb=2*(z+x*r)-1;var fb=2*(e+n*cb)-1;db.x=1*bb;db.y=1*fb;db.z=-1;db.normalize();gb.pu(db);bb=this.Bp(db,b,w.Z());0!=w.Ic()&&1>ab&&(fb=this.Bp(db,f,w.Ic()),bb&&fb?(b.x=b.x*ab+f.x*(1-ab),b.y=b.y*ab+f.y*(1-ab)):!bb&&fb&&(bb=!0,b.x=f.x,b.y=f.y));bb||(b.x=NaN);a.yg[a.fm++]=
y.width/2+b.x*y.width/2;a.yg[a.fm++]=y.height/2-b.y*y.width/2}this.Hv(a,h)}Bp(a,b,e){let f=!0;switch(e){case 0:case 4:e=1/(a.z*this.Um);b.x=-a.x*e;b.y=a.y*e;0<a.z&&(f=!1);break;case 9:1==a.z&&(f=!1);e=1/((1-a.z)*this.vn);b.x=a.x*e;b.y=-a.y*e;break;case 12:if(e=Math.sqrt(a.x*a.x+a.y*a.y),0==e)b.x=0,b.y=0;else{let h=2*this.Ql*Math.acos(-a.z)/e;if(2<e)return!1;b.x=h*a.x;b.y=-h*a.y}}return f}Hv(a,b){let e=this.o,f=[],h;a.Pa=0;for(let r=0;r<b;r++)for(let w=0;w<b;w++){f[0]=r+w*(b+1);f[1]=r+1+w*(b+1);f[2]=
r+(w+1)*(b+1);f[3]=r+1+(w+1)*(b+1);h=!0;for(var n=0;4>n;n++)isNaN(a.yg[2*f[0]])&&(h=!1);if(h){let x=n=!1,y=!1,z=!1;for(let cb=0;4>cb;cb++){let ab=a.yg[2*f[cb]];ab<e.H.width&&(x=!0);0<=ab&&(n=!0);ab=a.yg[2*f[cb]+1];ab<e.H.height&&(y=!0);0<=ab&&(z=!0)}if(h=h&&x&&n&&y&&z)this.Gh?(a.mb[a.Pa++]=f[0],a.mb[a.Pa++]=f[1],a.mb[a.Pa++]=f[1],a.mb[a.Pa++]=f[3],a.mb[a.Pa++]=f[3],a.mb[a.Pa++]=f[2],a.mb[a.Pa++]=f[2],a.mb[a.Pa++]=f[0],a.mb[a.Pa++]=f[3],a.mb[a.Pa++]=f[0],a.mb[a.Pa++]=f[1],a.mb[a.Pa++]=f[2]):(a.mb[a.Pa++]=
f[0],a.mb[a.Pa++]=f[3],a.mb[a.Pa++]=f[2],a.mb[a.Pa++]=f[0],a.mb[a.Pa++]=f[1],a.mb[a.Pa++]=f[3])}}}qw(){let a=this.o,b=this.D,e=b.M,f=e.levels;if(a.N){var h=a.N,n=this.Xm;h.useProgram(n);this.Tk(n);h.enable(h.CULL_FACE);h.cullFace(h.FRONT);h.enable(h.STENCIL_TEST);h.stencilOp(h.KEEP,h.KEEP,h.INCR);h.stencilFunc(h.EQUAL,0,255);h.blendFuncSeparate(h.SRC_ALPHA,h.ONE_MINUS_SRC_ALPHA,h.SRC_ALPHA,h.ONE);h.enable(h.BLEND);kb.identity(this.Za);kb.perspective(a.Eb(),a.Xa.width/a.Xa.height,.1,100,this.Za);h.uniformMatrix4fv(h.getUniformLocation(n,
"uPMatrix"),!1,this.Za);b.pe=0;b.kq();var r=b.Po(),w=0;var x=f.length-1;for(var y={},z=f[x];z.Mg&&0<x;)x--,z=f[x];var cb=x,ab=cb;for(var db=0;6>db;db++)for(var gb=0;gb<z.sa;gb++)for(var bb=0;bb<z.$;bb++)y[bb+gb*z.$+db*z.$*z.sa]=1;for(;x>=r;){db={};z=f[x];gb=null;0<x&&(gb=f[x-1]);bb=!0;for(var fb in y)if(y.hasOwnProperty(fb)){var ib=Number(fb),hb=z.Oa[ib],mb=Number(Math.floor(ib/(z.$*z.sa))),ob=Math.floor((ib-mb*z.$*z.sa)/z.$),jb=Math.floor(ib-(ob*z.$+mb*z.$*z.sa));if(6<=mb)console.log("Grrr...");
else if(b.cube.yf[mb].Ma=!1,hb||(hb=z.Oa[ib]=new Hb(ib)),this.Gv(hb,z,jb,ob,Math.max(1,this.Qn>>cb-x),mb,0!=a.Ic()),hb.visible=0<hb.Pa||z.cache,hb.Xj=3,hb.ak=Date.now(),hb.visible&&!hb.Fb&&(bb=!1,this.xj()?hb.M||(hb.Ve?hb.Ve--:(this.ck(hb,z,b.bh(mb,x,jb,ob)),a.Ma=!0)):this.D.pe++),gb&&(hb.visible||gb.cache)){ib=(jb*e.W+1)/z.width;jb=Math.min(1,((jb+1)*e.W-1)/z.width);var sb=(ob*e.W+1)/z.height;ob=Math.min(1,((ob+1)*e.W-1)/z.height);hb=e.W/gb.width;let vb=e.W/gb.height,yb=sb;sb=Math.floor(sb*gb.height/
e.W);do{let zb=ib,xb=Math.floor(ib*gb.width/e.W);do{let Fb=xb+sb*gb.$+mb*gb.$*gb.sa;xb<gb.$&&sb<gb.sa&&(db[Fb]=1);xb++;zb+=hb}while(zb<jb);sb++;yb+=vb}while(yb<ob)}}bb&&(ab=x,20>b.fov.m&&x<this.ng&&(this.Gk=!0));y=db;x--}this.Zm();h.uniform1i(h.getUniformLocation(n,"tileTexture"),0);h.activeTexture(h.TEXTURE0);x=r;for(r=-1;x<=Math.min(ab,this.ng-1);){z=f[x];for(let vb in z.Oa)if(z.Oa.hasOwnProperty(vb)){db=Number(vb);y=z.Oa[db];cb=Math.floor(db/(z.$*z.sa));fb=Math.floor((db-cb*z.$*z.sa)/z.$);db=Math.floor(db-
(fb*z.$+cb*z.$*z.sa));r!=cb&&(r=cb,this.hh(cb,n));if(w>b.ji){this.Gk=!1;break}y.Fb&&(bb=gb=e.W,db==z.$-1&&(gb=z.width-e.W*db),fb==z.sa-1&&(bb=z.height-e.W*fb),gb=(gb+2*e.ib)/e.W,bb=(bb+2*e.ib)/e.W,h.bindTexture(h.TEXTURE_2D,y.Fb),h.uniform2f(h.getUniformLocation(n,"uCanvasDimensions"),a.H.width,a.H.height),mb=h.getUniformLocation(n,"srcScale"),h.uniform2f(mb,.5*z.width/e.W/gb,.5*z.height/e.W/bb),mb=h.getUniformLocation(n,"srcOffset"),h.uniform2f(mb,(.5*z.width+e.ib-e.W*db)/e.W/gb,-(.5*z.height+e.ib-
e.W*fb)/e.W/bb+1),mb=h.getUniformLocation(n,"zOffset"),h.uniform1f(mb,(x+1)/(f.length+5)),fb=h.getAttribLocation(n,"aVertexPosition"),h.disableVertexAttribArray(0),h.disableVertexAttribArray(1),h.disableVertexAttribArray(2),h.enableVertexAttribArray(fb),h.activeTexture(h.TEXTURE0),this.Ni(h),h.bindBuffer(h.ARRAY_BUFFER,this.Qt),h.vertexAttribPointer(fb,2,h.FLOAT,!1,0,0),h.bufferData(h.ARRAY_BUFFER,new Float32Array(y.yg),h.DYNAMIC_DRAW),h.bindBuffer(h.ELEMENT_ARRAY_BUFFER,this.Pt),h.bufferData(h.ELEMENT_ARRAY_BUFFER,
new Uint16Array(y.mb),h.DYNAMIC_DRAW),this.Gh?0==x&&0==cb&&h.drawElements(h.LINES,y.Pa,h.UNSIGNED_SHORT,0):h.drawElements(h.TRIANGLES,y.Pa,h.UNSIGNED_SHORT,0),w++)}x++}h.disable(h.CULL_FACE);h.cullFace(h.FRONT_AND_BACK);a.Eh=!1;h.disable(h.STENCIL_TEST)}}hh(a,b){let e=this.o.N;kb.identity(this.Aa);this.Wg(a,-1);e.uniformMatrix4fv(e.getUniformLocation(b,"matRotate"),!1,this.Aa)}Iu(){let a=this.o;if(a.N){var b=a.N,e=this.Bi;b.useProgram(e);this.hh(0,e);b.uniform2f(b.getUniformLocation(e,"uCanvasDimensions"),
a.H.width,a.H.height);var f=b.getUniformLocation(e,"srcOffset");b.uniform2f(f,.5,.5);1==a.B.format&&(f=b.getUniformLocation(e,"srcScale"),a.pc?(b.uniform2f(f,-.5/Math.PI,(a.B.flipY?-.5:.5)/Math.PI),f=b.getUniformLocation(e,"srcOffset"),a.Zf?b.uniform2f(f,.5,.75):b.uniform2f(f,.5,.25)):b.uniform2f(f,-.5/Math.PI,(a.B.flipY?-1:1)/Math.PI));if(15==a.B.format){f=b.getUniformLocation(e,"srcScale");let h=1;0<a.B.width&&0<a.B.height&&(h=.5*a.B.width/a.B.height);a.pc?(b.uniform2f(f,-.5/Math.PI,(a.B.flipY?
-h:h)/Math.PI),f=b.getUniformLocation(e,"srcOffset"),a.Zf?b.uniform2f(f,.25,.5):b.uniform2f(f,.75,.5)):b.uniform2f(f,-1/Math.PI,2*(a.B.flipY?-h:h)/Math.PI)}14==a.B.format&&(f=b.getUniformLocation(e,"srcScale"),b.uniform2f(f,1-2*a.B.rf/(a.B.width/3),1-2*a.B.rf/(a.B.height/2)));this.Tk(e);f=b.getUniformLocation(e,"cubeTexture");b.uniform1i(f,0);f=b.getAttribLocation(e,"aVertexPosition");b.disableVertexAttribArray(0);b.disableVertexAttribArray(1);b.disableVertexAttribArray(2);b.enableVertexAttribArray(f);
b.bindBuffer(b.ARRAY_BUFFER,this.Ai);b.vertexAttribPointer(f,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_2D,a.B.texture);this.Ni(b);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.nd);b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0)}}Tk(a){const b=this.D;let e=this.o.N;var f=this.o.H;let h=1;switch(b.fov.mode){case 0:h=f.width/f.height;break;case 1:h=1;break;case 2:h=f.width/Math.sqrt(f.width*f.width+f.height*f.height);break;case 3:4*f.height/3<f.width&&(h=4/3)}f=e.getUniformLocation(a,
"rectDstDistance");this.Um=Math.tan(Math.min(b.fov.m,179)/2*Math.PI/180)*h;e.uniform1f(f,this.Um);f=e.getUniformLocation(a,"fisheyeDistance");this.Ql=180/(b.fov.m*Math.PI*h);e.uniform1f(f,this.Ql);f=e.getUniformLocation(a,"stereoDistance");this.vn=Math.tan(Math.min(b.fov.m,359)/4*Math.PI/180)*h;e.uniform1f(f,this.vn);f=e.getUniformLocation(a,"directionBlend");e.uniform1f(f,b.Fj());f=e.getUniformLocation(a,"transitionBlend");e.uniform1f(f,this.En)}Hu(){var a=this.o;let b=a.N;var e=this.Wm;b.useProgram(e);
b.enable(b.STENCIL_TEST);b.stencilOp(b.KEEP,b.KEEP,b.INCR);b.stencilFunc(b.EQUAL,0,255);this.hh(0,e);b.uniform2f(b.getUniformLocation(e,"uCanvasDimensions"),a.H.width,a.H.height);a=b.getUniformLocation(e,"srcScale");b.uniform2f(a,1,1);a=b.getUniformLocation(e,"srcOffset");b.uniform2f(a,0,0);a=b.getUniformLocation(e,"zOffset");b.uniform1f(a,.9999);this.Tk(e);this.hh(0,e);a=b.getUniformLocation(e,"cubeTexture");b.uniform1i(a,0);e=b.getAttribLocation(e,"aVertexPosition");b.disableVertexAttribArray(0);
b.disableVertexAttribArray(1);b.disableVertexAttribArray(2);b.enableVertexAttribArray(e);b.bindBuffer(b.ARRAY_BUFFER,this.Ai);b.vertexAttribPointer(e,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_CUBE_MAP,this.hb);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_MIN_FILTER,b.LINEAR);b.texParameteri(b.TEXTURE_CUBE_MAP,b.TEXTURE_MAG_FILTER,b.LINEAR);
b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.nd);b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0);b.disable(b.STENCIL_TEST)}Hs(){var a,b;let e=this.o,f=e.N;if(f){f.getParameter(f.MAX_CUBE_MAP_TEXTURE_SIZE);var h=[1,3,5,4,0,2],n=!0,r=!0,w=!1;for(var x=0;6>x;x++)(null===(a=this.fb[x].Of)||void 0===a?0:a.complete)?this.qj[x]||(w=!0):n=!1,(null===(b=this.fb[x].Tf)||void 0===b?0:b.complete)||(r=!1);if(r||n)if(!r||n||!this.hb||w){r=Math.round(e.bf/e.dh);a=(e.bf-r)/2;this.ng=0;this.hb||(this.hb=f.createTexture(),
e.de++);f.bindTexture(f.TEXTURE_CUBE_MAP,this.hb);f.texParameteri(f.TEXTURE_CUBE_MAP,f.TEXTURE_MIN_FILTER,f.LINEAR);f.texParameteri(f.TEXTURE_CUBE_MAP,f.TEXTURE_MAG_FILTER,f.LINEAR);f.pixelStorei(f.UNPACK_FLIP_Y_WEBGL,1);b=document.createElement("canvas");b.width=r;b.height=r;r=b.getContext("2d");for(w=0;6>w;w++)x=h[w],this.fb[x].Of.complete?this.qj[x]||(r.drawImage(this.fb[x].Of,-a,-a),f.texImage2D(f.TEXTURE_CUBE_MAP_POSITIVE_X+w,0,f.RGBA,f.RGBA,f.UNSIGNED_BYTE,b),this.qj[x]=!0):(r.drawImage(this.fb[x].Tf,
-a,-a,e.bf,e.bf),f.texImage2D(f.TEXTURE_CUBE_MAP_POSITIVE_X+w,0,f.RGBA,f.RGBA,f.UNSIGNED_BYTE,b));this.$h=!n}}}Gs(){var a=this.o,b=this.D.M;let e=b.levels,f=a.N;if(f){var h=e.length-1;if(!(0>h)){e[h].Mg&&h--;var n=512;a.Zh?n=2<=a.devicePixelRatio?512:256:!a.Pg&&2<=a.devicePixelRatio&&(n=1024);var r=f.getParameter(f.MAX_CUBE_MAP_TEXTURE_SIZE);for(r&&r<n&&(n=r);0<h&&e[h-1].width<=n;)h--;r=e[h];if(0!=r.$){n=h;r=this.zs(h);this.$h&&r&&(this.$h=!1);r||(h=e.length-1,r||(r=this.loadLevel(h)),this.$h=!0);
for(var w=h;w>=n;w--)this.loadLevel(w)&&(h=w);if(r){this.hb||(this.hb=f.createTexture(),a.de++);r=e[h];this.ng>h&&(this.ng=h);a=b.ib;w=0<a||1<r.$||1<r.sa||h==n;let y,z=this.Sv;w&&(z||(this.Sv=z=document.createElement("canvas")),2048>r.width?1500<r.width?(z.width=2048,z.height=2048):700<r.width?(z.width=1024,z.height=1024):(z.width=512,z.height=512):(z.width=r.width,z.height=r.height),y=z.getContext("2d"));f.bindTexture(f.TEXTURE_CUBE_MAP,this.hb);f.texParameteri(f.TEXTURE_CUBE_MAP,f.TEXTURE_WRAP_S,
f.CLAMP_TO_EDGE);f.texParameteri(f.TEXTURE_CUBE_MAP,f.TEXTURE_WRAP_T,f.CLAMP_TO_EDGE);f.pixelStorei(f.UNPACK_FLIP_Y_WEBGL,1);let cb=[1,3,5,4,0,2];b=b.W;for(let ab=0;6>ab;ab++){y&&y.clearRect(0,0,z.width,z.height);for(let db=h;db>=n;db--){r=e[db];for(let gb=0;gb<r.sa;gb++)for(let bb=0;bb<r.$;bb++){var x=r.Oa[bb+gb*r.$+cb[ab]*r.$*r.sa];if(x){let fb=x.M;x.ub&&(fb=x.ub);fb&&0<fb.width&&(w?(x=z.width/r.width,y.drawImage(fb,x*(bb*b-a),x*(gb*b-a),x*fb.width,x*fb.height)):f.texImage2D(f.TEXTURE_CUBE_MAP_POSITIVE_X+
ab,0,f.RGBA,f.RGBA,f.UNSIGNED_BYTE,fb))}}}w&&f.texImage2D(f.TEXTURE_CUBE_MAP_POSITIVE_X+ab,0,f.RGBA,f.RGBA,f.UNSIGNED_BYTE,z)}}}}}}loadLevel(a){let b=this.o,e=this.D,f=e.M.levels[a];if(0==f.$)return!1;let h=!0;f.cache=!0;for(let n=0;6>n;n++)for(let r=0;r<f.sa;r++)for(let w=0;w<f.$;w++){let x=w+r*f.$+n*f.$*f.sa,y=f.Oa[x];y||(y=f.Oa[x]=new Hb(x));this.xj()?y.M||(y.Ve?y.Ve--:(this.ck(y,f,e.bh(n,a,w,r)),b.Ma=!0)):e.pe++;y.Fb||(h=!1,b.Ma=!0)}h&&(f.loaded=!0);return h}zs(a){a=this.D.M.levels[a];if(0==a.$)return!1;
for(let b=0;6>b;b++)for(let e=0;e<a.sa;e++)for(let f=0;f<a.$;f++){let h=a.Oa[f+e*a.$+b*a.$*a.sa];if(!h||!h.Fb)return!1}return a.loaded=!0}ready(){return null!=this.hb}Gq(){if(this.D.cu()){var a=this.D.M;return a.levels[a.levels.length-1].loaded}for(a=0;6>a;a++)if(!this.fb[a].Tf.complete)return!1;return!0}Zm(){let a=this.o,b=this.D;var e=b.M.levels;let f=a.N,h=Date.now();for(let r=e.length-1;0<=r;r--){let w=e[r];if(!w.cache)for(let x in w.Oa)if(w.Oa.hasOwnProperty(x)){var n=w.Oa[x];0<n.Xj&&n.Xj--;
n.visible||0<n.Xj?(n.visible&&(n.ak=h),n=this.ze.indexOf(n),-1!==n&&this.ze.splice(n,1)):-1===this.ze.indexOf(n)&&(n.level=w,this.ze.push(n))}}if(this.ze.length>1.1*b.Gr)for(this.ze.sort(function(r,w){return w.ak-r.ak});this.ze.length>b.Gr;)e=this.ze.pop(),e.Fb&&(f.deleteTexture(e.Fb),a.de--,e.Fb=0),e.M=null,e.Ef&&(f.deleteBuffer(e.Ef),e.Ef=0),delete e.level.Oa[e.id]}hw(){let a=this.o;if(a.N){var b=this.o.N,e=this.D,f;for(f=0;f<a.V.length;f++){let h=a.V[f];if(h.vd)continue;kb.identity(this.Aa);kb.rotate(this.Aa,
-e.Y.m*Math.PI/180,[0,0,1]);kb.rotate(this.Aa,-e.u.m*Math.PI/180,[1,0,0]);kb.rotate(this.Aa,(180-e.pan.m)*Math.PI/180,[0,1,0]);kb.rotate(this.Aa,h.pan*Math.PI/180,[0,1,0]);kb.rotate(this.Aa,-h.u*Math.PI/180,[1,0,0]);kb.translate(this.Aa,[0,0,1]);kb.rotate(this.Aa,h.Sa*Math.PI/180,[0,0,1]);kb.rotate(this.Aa,-h.Ca*Math.PI/180,[0,1,0]);kb.rotate(this.Aa,h.ya*Math.PI/180,[1,0,0]);let n=Math.tan(h.fov/2*Math.PI/180),r=h.aspect;r||(r=16/9);kb.scale(this.Aa,[n,n/r,1]);kb.translate(this.Aa,[0,0,-1]);b.bindBuffer(b.ARRAY_BUFFER,
this.rj);b.vertexAttribPointer(this.R.la,3,b.FLOAT,!1,0,0);b.bindBuffer(b.ARRAY_BUFFER,this.xe);b.vertexAttribPointer(this.R.Ea,2,b.FLOAT,!1,0,0);b.activeTexture(b.TEXTURE0);b.bindTexture(b.TEXTURE_2D,h.texture);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_S,b.CLAMP_TO_EDGE);b.texParameteri(b.TEXTURE_2D,b.TEXTURE_WRAP_T,b.CLAMP_TO_EDGE);this.Ni(b);b.bindBuffer(b.ELEMENT_ARRAY_BUFFER,this.nd);b.uniform1i(this.R.Rg,0);b.uniformMatrix4fv(this.R.ni,!1,this.Aa);b.uniformMatrix4fv(this.R.Se,!1,this.Za);
b.drawElements(b.TRIANGLES,6,b.UNSIGNED_SHORT,0)}}}gw(){let a=this.o,b;if(a.N){var e=a.N;e.useProgram(this.R);kb.identity(this.Za);kb.perspective(a.Eb(),a.Xa.width/a.Xa.height,.1,100,this.Za);e.uniformMatrix4fv(this.R.Se,!1,this.Za);this.Wg(0);e.uniform1i(this.R.Rg,0);e.uniformMatrix4fv(this.R.Se,!1,this.Za);e.uniformMatrix4fv(this.R.ni,!1,this.Aa);e.enableVertexAttribArray(this.R.la);e.enableVertexAttribArray(this.R.Ea);e.bindBuffer(e.ARRAY_BUFFER,this.xe);e.vertexAttribPointer(this.R.Ea,2,e.FLOAT,
!1,0,0);e.activeTexture(e.TEXTURE0);e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,this.nd);e.uniform1f(this.R.hl,1E-4);e.vertexAttribPointer(this.R.la,3,e.FLOAT,!1,0,0);e.bindTexture(e.TEXTURE_2D,a.B.texture);for(b=0;1>b;b++)this.Wg(0),e.bindBuffer(e.ARRAY_BUFFER,a.B.Vk),e.vertexAttribPointer(this.R.la,3,e.FLOAT,!1,0,0),e.bindBuffer(e.ARRAY_BUFFER,a.B.Wi),e.vertexAttribPointer(this.R.Ea,2,e.FLOAT,!1,0,0),e.activeTexture(e.TEXTURE0),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,a.B.Oj),e.uniform1i(this.R.Rg,0),e.uniformMatrix4fv(this.R.ni,
!1,this.Aa),e.uniformMatrix4fv(this.R.Se,!1,this.Za),e.drawElements(e.TRIANGLES,36,e.UNSIGNED_SHORT,0)}}fw(){let a=this.o,b=a.N,e=a.B;if(0<a.V.length)for(var f=0;f<a.V.length;f++){let h=a.V[f];if(!h.vd&&h.isRunning&&h.Th!=h.j.currentTime&&(h.Th=h.j.currentTime,!h.aspect&&0<h.j.videoHeight&&(h.aspect=h.j.videoWidth/h.j.videoHeight),a.ej))try{h.texture&&(b.bindTexture(b.TEXTURE_2D,h.texture),b.texImage2D(b.TEXTURE_2D,0,b.RGB,b.RGB,b.UNSIGNED_BYTE,h.j),a.update())}catch(n){}}if(e.j&&(f=Number(e.j.currentTime),
e.Th!=f)){e.Th=f;try{let h=0<e.j.readyState;a.Xh&&e.Mc&&(h=h&&0<e.j.currentTime);e.texture&&e.Eg&&h&&(e.Mc||this.Ao(),e.Mc=!0,e.width=e.j.videoWidth,e.height=e.j.videoHeight,b.pixelStorei(b.UNPACK_FLIP_Y_WEBGL,a.B.flipY),b.bindTexture(b.TEXTURE_2D,e.texture),b.texImage2D(b.TEXTURE_2D,0,b.RGB,b.RGB,b.UNSIGNED_BYTE,e.j),e.Bn=!0,a.update())}catch(h){}}}Bq(){let a;var b;let e=this.o,f=this.o.N;e.P.um!=e.P.mode&&(e.P.um=e.P.mode);if((0<=e.P.mode||0<e.P.dc.length)&&!e.F.Zi){let r=1;0>=e.P.mode&&(r=0);3==
e.P.mode&&(r=e.P.Fa);for(a=0;a<e.L.length;a++){let w=e.L[a];if("poly"==w.type){var h=w.Lg;let x=r;2==e.P.mode&&(x=w.Fa);var n=e.P.dc.indexOf(w.id);-1!=n&&(x=e.P.Tc[n]);if(0<h.length){n=[];for(b=0;b<h.length;b++)n.push(h[b].Ac),n.push(h[b].ac),n.push(0);f.useProgram(this.ie);f.enable(f.BLEND);f.blendFuncSeparate(f.SRC_ALPHA,f.ONE_MINUS_SRC_ALPHA,f.SRC_ALPHA,f.ONE);f.disable(f.DEPTH_TEST);this.On||(this.On=f.createBuffer());f.bindBuffer(f.ARRAY_BUFFER,this.On);f.bufferData(f.ARRAY_BUFFER,new Float32Array(n),
f.STATIC_DRAW);f.uniform2f(f.getUniformLocation(this.ie,"uCanvasDimensions"),e.H.width,e.H.height);h=f.getUniformLocation(this.ie,"uColor");b=w.ic;f.uniform3f(h,(b>>16&255)/255,(b>>8&255)/255,(b&255)/255);let y=f.getUniformLocation(this.ie,"uAlpha");f.uniform1f(y,w.hc*x);f.vertexAttribPointer(this.ie.la,3,f.FLOAT,!1,0,0);f.drawArrays(f.LINE_LOOP,0,n.length/3);b=w.fc;f.uniform3f(h,(b>>16&255)/255,(b>>8&255)/255,(b&255)/255);f.uniform1f(y,w.ec*x);f.enable(f.STENCIL_TEST);f.clearStencil(0);f.clear(f.STENCIL_BUFFER_BIT);
f.colorMask(!1,!1,!1,!1);f.stencilFunc(f.ALWAYS,1,1);f.stencilOp(f.INCR,f.INCR,f.INCR);f.drawArrays(f.TRIANGLE_FAN,0,n.length/3);f.colorMask(!0,!0,!0,!0);f.stencilFunc(f.EQUAL,1,1);f.stencilOp(f.ZERO,f.ZERO,f.ZERO);f.drawArrays(f.TRIANGLE_FAN,0,n.length/3);f.disable(f.BLEND);f.enable(f.DEPTH_TEST);f.disable(f.STENCIL_TEST);f.useProgram(this.R)}}}}}ew(){var a=this.o,b=this.D;let e=b.M;if(a.N){var f=a.N;f.disable(f.DEPTH_TEST);f.disable(f.CULL_FACE);f.useProgram(this.wg);var h=f.getUniformLocation(this.wg,
"uRect");f.uniform2f(f.getUniformLocation(this.wg,"uCanvasDimensions"),a.H.width,a.H.height);f.activeTexture(f.TEXTURE0);f.bindBuffer(f.ELEMENT_ARRAY_BUFFER,this.nd);var n=f.getAttribLocation(this.wg,"aVertexPosition");f.disableVertexAttribArray(0);f.disableVertexAttribArray(1);f.disableVertexAttribArray(2);f.enableVertexAttribArray(n);f.bindBuffer(f.ARRAY_BUFFER,this.xe);f.vertexAttribPointer(n,2,f.FLOAT,!1,0,0);b.pe=0;var r=100/b.fov.m;var w=e.width/e.height;n=a.H.height*r*w;r*=a.H.height;w=(b.pan.m/
100/w-.5)*n+a.H.width/2;for(var x=(b.u.m/100-.5)*r+a.H.height/2,y,z,cb,ab=0;e.levels.length>=ab+2&&e.levels[ab+1].width>n;)ab++;var db;var gb=[];for(db=e.levels.length-1;db>=ab;){var bb=e.levels[db];let fb;bb.cache?(fb={fx:0,fy:0},fb.Kd=bb.$-1,fb.Ld=bb.sa-1):(fb={},y=-x/r*(bb.height/e.W),z=(-w+a.H.width)/n*(bb.width/e.W),cb=(-x+a.H.height)/r*(bb.height/e.W),fb.fx=Math.min(Math.max(0,Math.floor(-w/n*(bb.width/e.W))),bb.$-1),fb.fy=Math.min(Math.max(0,Math.floor(y)),bb.sa-1),fb.Kd=Math.min(Math.max(0,
Math.floor(z)),bb.$-1),fb.Ld=Math.min(Math.max(0,Math.floor(cb)),bb.sa-1));gb[db]=fb;let ib=!0;for(z=fb.fy;z<=fb.Ld;z++)for(y=fb.fx;y<=fb.Kd;y++){cb=y+z*bb.$;let hb=bb.Oa[cb];hb||(hb=new Hb(cb),bb.Oa[cb]=hb);this.xj()?hb.M||(this.ck(hb,bb,b.bh(0,db,y,z)),a.Ma=!0):b.pe++;hb.M&&hb.M.complete||(ib=!1);hb.visible=!0}fb.gq=ib;db--}for(db=e.levels.length-1;db>=ab;){a=e.levels[db];if(gb[db]&&0<=gb[db].fx)for(b=gb[db],z=b.fy;z<=b.Ld;z++)for(y=b.fx;y<=b.Kd;y++)cb=y+z*a.$,(bb=a.Oa[cb])&&bb.M&&bb.M.complete&&
(f.uniform4f(h,w+(-e.ib+e.W*y)*n/a.width,x+(-e.ib+e.W*z)*r/a.height,bb.M.width*n/a.width,bb.M.height*r/a.height),bb&&bb.Fb&&(f.bindBuffer(f.ELEMENT_ARRAY_BUFFER,this.nd),f.bindTexture(f.TEXTURE_2D,bb.Fb),this.Ni(f),f.drawElements(f.TRIANGLES,6,f.UNSIGNED_SHORT,0)));db--}this.Zm()}}Uf(){let a=this.o.N;if(a&&this.fb)for(;0<this.fb.length;){let b=this.fb.pop();b.vu=!0;a.deleteTexture(b)}this.hb&&(a.deleteTexture(this.hb),this.hb=null);this.ng=1E6;this.qj=[!1,!1,!1,!1,!1,!1]}}class vd{constructor(a){this.$i=
[];this.o=a;this.enabled=!1;this.Kb=2;this.Bo=!1}No(a){if(2==a.mode||3==a.mode||5==a.mode){var b=this.o.ga.currentTime;if(a.lb&&a.lb.gain&&a.Db&&a.Bb&&a.Cb){let e=a.Db.gain.value,f=a.Bb.gain.value,h=a.Cb.gain.value;a.lb.gain.linearRampToValueAtTime(a.lb.gain.value,b);a.lb.gain.linearRampToValueAtTime(0,b+this.Kb);a.Db.gain.linearRampToValueAtTime(e,b);a.Db.gain.linearRampToValueAtTime(0,b+this.Kb);a.Bb.gain.linearRampToValueAtTime(f,b);a.Bb.gain.linearRampToValueAtTime(0,b+this.Kb);a.Cb.gain.linearRampToValueAtTime(h,
b);a.Cb.gain.linearRampToValueAtTime(0,b+this.Kb)}}else b=this.o.ga.currentTime,a.Ab&&a.Ab.gain&&(a.Ab.gain.linearRampToValueAtTime(a.Ab.gain.value,b),a.Ab.gain.linearRampToValueAtTime(0,b+this.Kb));a.Pl=!0;setTimeout(function(){a.We()},1E3*this.Kb+5)}Ov(){for(var a=0;a<this.o.X.length;a++){let b=this.o.X[a];!b.stopped&&!this.o.isPlaying(b.id)&&-1<b.loop&&4!=b.mode&&6!=b.mode&&(b.ta?b.Vd():(b.zd&&b.se(),b.j.play(),b.j.currentTime=0))}}Os(){let a=(this.o.ga.currentTime-this.Mv)/this.Kb;a=Math.min(1,
a);for(let b=0;b<this.o.X.length;b++){let e=this.o.X[b];this.o.isPlaying(e.id)&&1>e.va&&(e.va=a)}1==a&&clearInterval(this.Lv)}}class wd{constructor(a){this.ei=[];this.zc=[];this.jc=[];this.Bc=[];this.ln=!0;this.o=a;this.Es()}Vh(){let a=this.o.N;var b=a.createShader(a.VERTEX_SHADER);a.shaderSource(b,"attribute vec3 aVertexPosition;\nvoid main(void) {\n gl_Position = vec4(aVertexPosition, 1.0);\n}\n");a.compileShader(b);a.getShaderParameter(b,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(b)),b=null);
var e=a.createShader(a.FRAGMENT_SHADER);a.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec4 vColor;\nuniform vec2 uCanvasDimensions;\nuniform vec2 uFlareCenterPosition;\nuniform float uBlindingValue;\nuniform float uAspectRatio;\nvoid main(void) {\n float canvasDiag = sqrt( (uCanvasDimensions.x * uCanvasDimensions.x) + (uCanvasDimensions.y * uCanvasDimensions.y) );\n vec2 diff = uFlareCenterPosition - gl_FragCoord.xy;\n diff.y = diff.y * uAspectRatio;\n float distFromFlarePoint = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n float factor = (distFromFlarePoint / canvasDiag) / 10.0;\n gl_FragColor = vec4(1.0, 1.0, 1.0, pow(((1.0 - factor) * 0.8) * uBlindingValue, 2.0));\n}\n");
a.compileShader(e);a.getShaderParameter(e,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(e)),e=null);this.Fc=a.createProgram();a.attachShader(this.Fc,b);a.attachShader(this.Fc,e);a.linkProgram(this.Fc);a.getProgramParameter(this.Fc,a.LINK_STATUS)||alert("Could not initialise shaders");this.Fc.la=a.getAttribLocation(this.Fc,"aVertexPosition");a.enableVertexAttribArray(this.Fc.la);e=a.createShader(a.VERTEX_SHADER);b=a.createShader(a.VERTEX_SHADER);a.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\nvarying vec4 vColor;\nuniform vec2 uCirclePosition;\nuniform float uCircleRadius;\nuniform vec2 uCanvasDimensions2;\nuniform float uAspectRatio;\nvoid main(void) {\n vec2 circleOnScreen = aVertexPosition.xy * uCircleRadius + uCirclePosition;\n circleOnScreen.y = circleOnScreen.y / uAspectRatio;\n vec2 circleNorm = (circleOnScreen / uCanvasDimensions2) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(circleNorm.x, circleNorm.y, 0.0, 1.0);\n}\n");
a.compileShader(e);a.getShaderParameter(e,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(e)),e=null);a.shaderSource(b,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nattribute vec3 aVertexPosition;\nvarying vec4 vColor;\nuniform vec2 uCirclePosition;\nuniform float uCircleRadius;\nuniform vec2 uCanvasDimensions2;\nuniform float uAspectRatio;\nvoid main(void) {\n vec2 circleOnScreen = aVertexPosition.xy * uCircleRadius + uCirclePosition;\n circleOnScreen.y = circleOnScreen.y / uAspectRatio;\n vec2 circleNorm = (circleOnScreen / uCanvasDimensions2) * 2.0 - vec2(1.0, 1.0);\n gl_Position = vec4(circleNorm.x, circleNorm.y, 0.0, 1.0);\n}\n");
a.compileShader(b);a.getShaderParameter(b,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(b)),e=null);let f=a.createShader(a.FRAGMENT_SHADER);a.shaderSource(f,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec4 vColor;\nuniform vec2 uCircleTexturePosition;\nuniform vec3 uCircleColor;\nuniform float uCircleRadius;\nuniform float uCircleAlpha;\nuniform float uCircleSoftness;\nuniform float uAspectRatio;\nvoid main(void) {\n vec2 diff = uCircleTexturePosition - gl_FragCoord.xy;\n diff.y = diff.y * uAspectRatio;\n float distFromCircleCenter = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n float softnessDistance = uCircleRadius * (1.0 - uCircleSoftness);\n if (distFromCircleCenter > uCircleRadius)\n {\n\t  gl_FragColor = vec4(uCircleColor, 0.0);\n }\n else if (distFromCircleCenter <= (softnessDistance))\n {\n\t  float factor = distFromCircleCenter / softnessDistance;\n\t  gl_FragColor = vec4(uCircleColor, pow((1.0 - (0.2 * factor)) * uCircleAlpha, 1.8));\n }\n else\n {\n\t  float factor = (distFromCircleCenter - softnessDistance) / (uCircleRadius - softnessDistance);\n\t  gl_FragColor = vec4(uCircleColor, pow((0.8 - (0.8 * factor)) * uCircleAlpha, 1.8));\n }\n}\n");
a.compileShader(f);a.getShaderParameter(f,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(f)),f=null);this.Ba=a.createProgram();a.attachShader(this.Ba,e);a.attachShader(this.Ba,f);a.linkProgram(this.Ba);a.getProgramParameter(this.Ba,a.LINK_STATUS)||alert("Could not initialise shaders");this.Ba.la=a.getAttribLocation(this.Ba,"aVertexPosition");a.enableVertexAttribArray(this.Ba.la);e=a.createShader(a.FRAGMENT_SHADER);a.shaderSource(e,"#ifdef GL_FRAGMENT_PRECISION_HIGH\nprecision highp float;\n#else\nprecision mediump float;\n#endif\nvarying vec4 vColor;\nuniform vec2 uRingTexturePosition;\nuniform float uRingRadius;\nuniform float uRingAlpha;\nuniform float uAspectRatio;\nuniform sampler2D uSampler;\nvoid main(void) {\n vec2 diff = uRingTexturePosition - gl_FragCoord.xy;\n diff.y = diff.y * uAspectRatio;\n float distFromRingCenter = sqrt( (diff.x * diff.x) + (diff.y * diff.y) );\n float factor = distFromRingCenter / uRingRadius;\n if (distFromRingCenter > uRingRadius)\n {\n\t gl_FragColor = vec4(1.0, 1.0, 1.0, 0.0);\n }\n else\n {\n vec4 textureColor = texture2D(uSampler, vec2(factor / uAspectRatio, 0.5));\n gl_FragColor = vec4(textureColor.x, textureColor.y, textureColor.z, uRingAlpha);\n }\n}\n");
a.compileShader(e);a.getShaderParameter(e,a.COMPILE_STATUS)||(alert(a.getShaderInfoLog(e)),e=null);this.Ib=a.createProgram();a.attachShader(this.Ib,b);a.attachShader(this.Ib,e);a.linkProgram(this.Ib);a.getProgramParameter(this.Ib,a.LINK_STATUS)||alert("Could not initialise shaders");this.Ib.la=a.getAttribLocation(this.Ib,"aVertexPosition")}If(){let a=this.o.N;this.$d=a.createBuffer();a.bindBuffer(a.ARRAY_BUFFER,this.$d);a.bufferData(a.ARRAY_BUFFER,new Float32Array([-1,-1,0,1,-1,0,1,1,0,-1,1,0]),a.STATIC_DRAW);
this.$d.itemSize=3;this.$d.numberOfItems=4;this.vg=a.createBuffer();a.bindBuffer(a.ARRAY_BUFFER,this.vg);var b=[0,0,0],e=2*Math.PI/6,f=Math.PI/180*35;let h=1;for(var n=f;n<=f+2*Math.PI;n+=e)b.push(Math.sin(n)),b.push(-Math.cos(n)),b.push(0),h++;a.bufferData(a.ARRAY_BUFFER,new Float32Array(b),a.STATIC_DRAW);this.vg.itemSize=3;this.vg.numberOfItems=h;this.Rq=a.createTexture();a.bindTexture(a.TEXTURE_2D,this.Rq);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MIN_FILTER,a.LINEAR);a.texParameteri(a.TEXTURE_2D,
a.TEXTURE_MAG_FILTER,a.LINEAR);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_WRAP_S,a.CLAMP_TO_EDGE);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_WRAP_T,a.CLAMP_TO_EDGE);b=document.createElement("canvas");b.width=100;b.height=1;e=b.getContext("2d");e.width=100;e.height=1;f=e.createLinearGradient(0,0,100,0);f.addColorStop(0,this.o.Gb(0,0));f.addColorStop(.88,this.o.Gb(0,0));f.addColorStop(.9,this.o.Gb(16654848,1));f.addColorStop(.92,this.o.Gb(16776448,1));f.addColorStop(.94,this.o.Gb(4849466,1));f.addColorStop(.96,
this.o.Gb(131071,1));f.addColorStop(.98,this.o.Gb(8190,1));f.addColorStop(1,this.o.Gb(0,0));e.fillStyle=f;e.fillRect(0,0,100,1);a.texImage2D(a.TEXTURE_2D,0,a.RGBA,a.RGBA,a.UNSIGNED_BYTE,b)}Vu(){for(;0<this.ei.length;)this.ei.pop()}Es(){let a=[],b=[],e=[];var f={radius:14,alpha:.2,color:11390415,C:.27};a.push(f);f={radius:20,alpha:.25,color:11390415,C:.4};a.push(f);f={radius:10,alpha:.2,color:12442332,C:.6};a.push(f);f={radius:15,alpha:.2,color:11390415,C:.8};a.push(f);f={radius:10,alpha:.2,color:12442332,
C:1.5};a.push(f);f={radius:15,alpha:.2,color:11390415,C:1.8};a.push(f);f={radius:8,alpha:.2,color:12575203,J:.8,C:.7};b.push(f);f={radius:7,alpha:.4,color:12575203,J:.5,C:1.6};b.push(f);f={radius:5,alpha:.4,color:12575203,J:.6,C:.9};b.push(f);f={radius:8,alpha:.3,color:12575203,J:.4,C:1.1};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:30,alpha:.3,color:11390415,C:.5};a.push(f);f={radius:10,alpha:.3,color:11390415,C:1};a.push(f);f={radius:20,alpha:.3,color:11390415,
C:1.3};a.push(f);f={radius:10,alpha:.3,color:11390415,C:1.5};a.push(f);f={radius:15,alpha:.3,color:11390415,C:1.8};a.push(f);f={radius:10,alpha:.3,color:15506856,J:.8,C:.7};b.push(f);f={radius:20,alpha:.5,color:15506856,J:.5,C:1.6};b.push(f);f={radius:5,alpha:.5,color:15506856,J:.6,C:.9};b.push(f);f={radius:60,alpha:.4,color:15506856,J:.2,C:1.1};b.push(f);e.push({radius:220,alpha:.035,C:2});this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:30,alpha:.5,color:15465727,C:.5};a.push(f);
f={radius:40,alpha:.28,color:15726842,C:.8};a.push(f);f={radius:25,alpha:.32,color:15726842,C:1.1};a.push(f);f={radius:15,alpha:.25,color:15726842,C:1.35};a.push(f);f={radius:10,alpha:.28,color:15465727,C:1.65};a.push(f);f={radius:10,alpha:.45,color:15465727,J:.8,C:.7};b.push(f);f={radius:7,alpha:.5,color:15465727,J:.4,C:.9};b.push(f);f={radius:40,alpha:.4,color:15465727,J:.3,C:.38};b.push(f);f={radius:50,alpha:.4,color:15465727,J:.5,C:1.25};b.push(f);f={radius:18,alpha:.2,color:15465727,J:.5,C:1.25};
b.push(f);f={radius:10,alpha:.34,color:15726842,J:.8,C:1.5};b.push(f);f={radius:38,alpha:.37,color:15465727,J:.3,C:-.5};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:16,alpha:.5,color:16363159,C:.1};a.push(f);f={radius:26,alpha:.3,color:16091819,C:.32};a.push(f);f={radius:29,alpha:.2,color:16091819,C:1.32};a.push(f);f={radius:20,alpha:.18,color:16363159,C:1.53};a.push(f);f={radius:27,alpha:.13,color:16425092,C:1.6};a.push(f);f={radius:20,alpha:.1,color:16091819,
C:1.75};a.push(f);f={radius:12,alpha:.45,color:16312238,J:.45,C:.2};b.push(f);f={radius:8,alpha:.25,color:16434209,J:.7,C:.33};b.push(f);f={radius:9,alpha:.25,color:16091819,J:.4,C:.7};b.push(f);f={radius:7,alpha:.2,color:16091819,J:.4,C:.85};b.push(f);f={radius:60,alpha:.23,color:16091819,J:.55,C:1.05};b.push(f);f={radius:37,alpha:.1,color:16091819,J:.55,C:1.22};b.push(f);f={radius:10,alpha:.25,color:16363159,J:.65,C:1.38};b.push(f);f={radius:7,alpha:.2,color:16434209,J:.5,C:1.45};b.push(f);f={radius:3,
alpha:.2,color:16416033,J:.5,C:1.78};b.push(f);f={radius:6,alpha:.18,color:16434209,J:.45,C:1.9};b.push(f);f={radius:4,alpha:.14,color:16766514,J:.45,C:2.04};b.push(f);f={radius:30,alpha:.14,color:16766514,J:.8,C:.04};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:9,alpha:.3,color:14346999,J:.3,C:.3};b.push(f);f={radius:5,alpha:.5,color:14148072,J:.8,C:.6};b.push(f);f={radius:3,alpha:.37,color:14346999,J:.66,C:.8};b.push(f);f={radius:45,alpha:.2,color:14346999,
J:.36,C:1.2};b.push(f);f={radius:13,alpha:.2,color:14346999,J:.36,C:1.23};b.push(f);f={radius:11,alpha:.2,color:14148072,J:.36,C:1.28};b.push(f);f={radius:27,alpha:.16,color:14346999,J:.36,C:1.55};b.push(f);f={radius:6,alpha:.36,color:14148072,J:.8,C:1.7};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:24,alpha:.2,color:15186464,C:.2};a.push(f);f={radius:7,alpha:.26,color:15186464,C:.35};a.push(f);f={radius:23,alpha:.18,color:15186464,C:.65};a.push(f);f={radius:13,
alpha:.2,color:15186464,C:.8};a.push(f);f={radius:11,alpha:.15,color:15186464,C:1.4};a.push(f);f={radius:15,alpha:.11,color:15451904,C:1.6};a.push(f);f={radius:6,alpha:.45,color:15579138,J:.45,C:.22};b.push(f);f={radius:3,alpha:.3,color:15451904,J:.25,C:.4};b.push(f);f={radius:4,alpha:.2,color:15451904,J:.25,C:.45};b.push(f);f={radius:65,alpha:.17,color:15186464,J:.25,C:.5};b.push(f);f={radius:5,alpha:.45,color:15579138,J:.45,C:.88};b.push(f);f={radius:140,alpha:.18,color:15579138,J:.32,C:.95};b.push(f);
f={radius:12,alpha:.22,color:15579138,J:.32,C:1.1};b.push(f);f={radius:8,alpha:.32,color:15451904,J:.72,C:1.2};b.push(f);f={radius:55,alpha:.2,color:15451904,J:.45,C:1.33};b.push(f);f={radius:4,alpha:.3,color:15451904,J:.25,C:1.42};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:16,alpha:.4,color:10933495,C:.32};a.push(f);f={radius:14,alpha:.3,color:11007484,C:.36};a.push(f);f={radius:10,alpha:.3,color:4037331,C:.58};a.push(f);f={radius:14,alpha:.22,color:8835068,
C:.68};a.push(f);f={radius:10,alpha:.27,color:11007484,C:.82};a.push(f);f={radius:11,alpha:.27,color:10867450,C:1};a.push(f);f={radius:9,alpha:.2,color:6158332,C:1.05};a.push(f);f={radius:10,alpha:.17,color:10867450,C:1.78};a.push(f);f={radius:10,alpha:.3,color:4037331,C:-.23};a.push(f);f={radius:8,alpha:.45,color:8835068,J:.45,C:.175};b.push(f);f={radius:7,alpha:.4,color:12574715,J:.55,C:.46};b.push(f);f={radius:3,alpha:.3,color:10867450,J:.35,C:.5};b.push(f);f={radius:60,alpha:.37,color:4031699,
J:.75,C:.75};b.push(f);f={radius:3,alpha:.25,color:4031699,J:.25,C:.75};b.push(f);f={radius:3,alpha:.2,color:6158332,J:.25,C:.9};b.push(f);f={radius:7,alpha:.45,color:8835068,J:.45,C:1.3};b.push(f);f={radius:32,alpha:.22,color:8835068,J:.75,C:1.62};b.push(f);f={radius:9,alpha:.45,color:4031699,J:.65,C:1.6};b.push(f);f={radius:8,alpha:.25,color:4031699,J:.65,C:1.83};b.push(f);f={radius:7,alpha:.4,color:12574715,J:.55,C:-.18};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];
f={radius:16,alpha:.4,color:16389120,C:.32};a.push(f);f={radius:26,alpha:.22,color:16389120,C:.4};a.push(f);f={radius:26,alpha:.25,color:16389120,C:.65};a.push(f);f={radius:18,alpha:.3,color:16389120,C:1.23};a.push(f);f={radius:14,alpha:.26,color:16389120,C:1.33};a.push(f);f={radius:17,alpha:.18,color:16389120,C:1.7};a.push(f);f={radius:30,alpha:.16,color:16389120,C:2.15};a.push(f);f={radius:100,alpha:.25,color:16389120,J:.22,C:1.45};b.push(f);f={radius:7,alpha:.5,color:15628151,J:.3,C:1.5};b.push(f);
f={radius:3,alpha:.5,color:15628151,J:.3,C:1.52};b.push(f);f={radius:4,alpha:.5,color:16389120,J:.3,C:1.745};b.push(f);f={radius:9,alpha:.22,color:16389120,J:.3,C:1.8};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:16,alpha:.4,color:10933495,C:.32};a.push(f);f={radius:14,alpha:.3,color:11007484,C:.36};a.push(f);f={radius:10,alpha:.3,color:4037331,C:.58};a.push(f);f={radius:14,alpha:.22,color:8835068,C:.68};a.push(f);f={radius:10,alpha:.27,color:11007484,C:.82};
a.push(f);f={radius:11,alpha:.27,color:10867450,C:1};a.push(f);f={radius:9,alpha:.2,color:6158332,C:1.05};a.push(f);f={radius:10,alpha:.17,color:10867450,C:1.78};a.push(f);f={radius:10,alpha:.3,color:4037331,C:-.23};a.push(f);f={radius:8,alpha:.45,color:8835068,J:.45,C:.175};b.push(f);f={radius:7,alpha:.4,color:12574715,J:.55,C:.46};b.push(f);f={radius:3,alpha:.3,color:10867450,J:.35,C:.5};b.push(f);f={radius:60,alpha:.37,color:4031699,J:.75,C:.75};b.push(f);f={radius:3,alpha:.25,color:4031699,J:.25,
C:.75};b.push(f);f={radius:3,alpha:.2,color:6158332,J:.25,C:.9};b.push(f);f={radius:7,alpha:.45,color:8835068,J:.45,C:1.3};b.push(f);f={radius:32,alpha:.22,color:8835068,J:.75,C:1.62};b.push(f);f={radius:9,alpha:.45,color:4031699,J:.65,C:1.6};b.push(f);f={radius:8,alpha:.25,color:4031699,J:.65,C:1.83};b.push(f);f={radius:7,alpha:.4,color:12574715,J:.55,C:-.18};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:16,alpha:.4,color:16389120,C:.32};a.push(f);f={radius:26,
alpha:.22,color:16389120,C:.4};a.push(f);f={radius:26,alpha:.25,color:16389120,C:.65};a.push(f);f={radius:18,alpha:.3,color:16389120,C:1.23};a.push(f);f={radius:14,alpha:.26,color:16389120,C:1.33};a.push(f);f={radius:17,alpha:.18,color:16389120,C:1.7};a.push(f);f={radius:30,alpha:.16,color:16389120,C:2.15};a.push(f);f={radius:100,alpha:.25,color:16389120,J:.22,C:1.45};b.push(f);f={radius:7,alpha:.5,color:15628151,J:.3,C:1.5};b.push(f);f={radius:3,alpha:.5,color:15628151,J:.3,C:1.52};b.push(f);f={radius:4,
alpha:.5,color:16389120,J:.3,C:1.745};b.push(f);f={radius:9,alpha:.22,color:16389120,J:.3,C:1.8};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e);a=[];b=[];e=[];f={radius:24,alpha:.2,color:15186464,C:.2};a.push(f);f={radius:7,alpha:.26,color:15186464,C:.35};a.push(f);f={radius:23,alpha:.18,color:15186464,C:.65};a.push(f);f={radius:13,alpha:.2,color:15186464,C:.8};a.push(f);f={radius:11,alpha:.15,color:15186464,C:1.4};a.push(f);f={radius:15,alpha:.11,color:15451904,C:1.6};a.push(f);f={radius:6,
alpha:.45,color:15579138,J:.45,C:.22};b.push(f);f={radius:3,alpha:.3,color:15451904,J:.25,C:.4};b.push(f);f={radius:4,alpha:.2,color:15451904,J:.25,C:.45};b.push(f);f={radius:65,alpha:.17,color:15186464,J:.25,C:.5};b.push(f);f={radius:5,alpha:.45,color:15579138,J:.45,C:.88};b.push(f);f={radius:140,alpha:.18,color:15579138,J:.32,C:.95};b.push(f);f={radius:12,alpha:.22,color:15579138,J:.32,C:1.1};b.push(f);f={radius:8,alpha:.32,color:15451904,J:.72,C:1.2};b.push(f);f={radius:55,alpha:.2,color:15451904,
J:.45,C:1.33};b.push(f);f={radius:4,alpha:.3,color:15451904,J:.25,C:1.42};b.push(f);this.zc.push(a);this.jc.push(b);this.Bc.push(e)}Gu(){if(this.ln){var a=this.o.N,b,e=new lb(0,0,-100),f=this.o.ae(),h=0,n=0,r=!1,w=this.o.D;if(this.o.pa){var x=this.o.Xa.width;var y=this.o.Xa.height;this.o.F.Zi&&(x=this.o.F.Pb.width,y=this.o.F.Pb.height)}var z=Math.sqrt(x*x+y*y),cb=z/800;for(b=0;b<this.ei.length;b++){var ab=this.ei[b];e.init(0,0,-100);e.ya(-ab.u*Math.PI/180);e.Ca(ab.pan*Math.PI/180);e.Ca(-w.pan.m*Math.PI/
180);e.ya(w.u.m*Math.PI/180);e.Sa(w.Y.m*Math.PI/180);if(-.01>e.z){n=-f/e.z;h=e.x*n;n*=e.y;var db=Math.max(x,y);Math.abs(h)<db/2+100&&Math.abs(n)<db/2+100&&(r=!0,h+=x/2,n+=y/2)}if(r){this.o.pa&&(a.blendFunc(a.SRC_ALPHA,a.ONE),a.enable(a.BLEND),a.disable(a.DEPTH_TEST));db=x/2;var gb=y/2;var bb=Math.sqrt((db-h)*(db-h)+(gb-n)*(gb-n));var fb=z/2;db=ab.jo/100*((fb-bb)/fb);0>db&&(db=0);this.o.pa&&(a.useProgram(this.Fc),a.bindBuffer(a.ARRAY_BUFFER,this.o.F.vb),a.vertexAttribPointer(this.Fc.la,this.o.F.vb.itemSize,
a.FLOAT,!1,0,0),a.uniform2f(a.getUniformLocation(this.Fc,"uCanvasDimensions"),a.drawingBufferWidth,a.drawingBufferHeight),a.uniform2f(a.getUniformLocation(this.Fc,"uFlareCenterPosition"),a.drawingBufferWidth/x*h,y-a.drawingBufferHeight/y*n),a.uniform1f(a.getUniformLocation(this.Fc,"uBlindingValue"),db),a.uniform1f(a.getUniformLocation(this.Fc,"uAspectRatio"),this.o.F.Zi?a.drawingBufferWidth/a.drawingBufferHeight:a.drawingBufferWidth/a.drawingBufferHeight/(x/y)),a.drawArrays(a.TRIANGLE_STRIP,0,this.o.F.vb.numberOfItems));
if(0!=Number(ab.type)&&!this.o.F.Zi){db=x/2-h;gb=y/2-n;let ob=1,jb=Number(ab.type)-1;bb<.35*fb&&(ob=bb/(.35*fb),ob*=ob);bb>.7*fb&&(ob=(fb-bb)/(.3*fb));ob*=ab.alpha/100;if(0<this.zc[jb].length)for(bb=0;bb<this.zc[jb].length;bb++){fb=this.zc[jb][bb];var ib=fb.radius*cb,hb=fb.alpha*ob;0>hb&&(hb=0);var mb=fb.color;if(8==jb||9==jb||10==jb)mb=ab.color;this.o.pa&&(a.useProgram(this.Ba),a.bindBuffer(a.ARRAY_BUFFER,this.vg),a.vertexAttribPointer(this.Ba.la,this.vg.itemSize,a.FLOAT,!1,0,0),a.uniform2f(a.getUniformLocation(this.Ba,
"uCanvasDimensions2"),a.drawingBufferWidth,a.drawingBufferHeight),a.uniform2f(a.getUniformLocation(this.Ba,"uCirclePosition"),a.drawingBufferWidth/x*(h+db*fb.C),a.drawingBufferWidth/x*(y-(n+gb*fb.C))),a.uniform2f(a.getUniformLocation(this.Ba,"uCircleTexturePosition"),a.drawingBufferWidth/x*(h+db*fb.C),y-(n+gb*fb.C)),a.uniform1f(a.getUniformLocation(this.Ba,"uCircleRadius"),ib),a.uniform3f(a.getUniformLocation(this.Ba,"uCircleColor"),(mb>>16&255)/255,(mb>>8&255)/255,(mb&255)/255),a.uniform1f(a.getUniformLocation(this.Ba,
"uCircleAlpha"),hb),a.uniform1f(a.getUniformLocation(this.Ba,"uCircleSoftness"),.1),a.uniform1f(a.getUniformLocation(this.Ba,"uAspectRatio"),a.drawingBufferWidth/a.drawingBufferHeight/(x/y)),a.drawArrays(a.TRIANGLE_FAN,0,this.vg.numberOfItems))}if(0<this.jc[jb].length)for(bb=0;bb<this.jc[jb].length;bb++){fb=this.jc[jb][bb];ib=fb.radius*cb;hb=fb.alpha*ob;0>hb&&(hb=0);mb=fb.color;if(8==jb||9==jb||10==jb)mb=ab.color;this.o.pa&&(a.useProgram(this.Ba),a.bindBuffer(a.ARRAY_BUFFER,this.$d),a.vertexAttribPointer(this.Ba.la,
this.$d.itemSize,a.FLOAT,!1,0,0),a.uniform2f(a.getUniformLocation(this.Ba,"uCanvasDimensions2"),a.drawingBufferWidth,a.drawingBufferHeight),a.uniform2f(a.getUniformLocation(this.Ba,"uCirclePosition"),a.drawingBufferWidth/x*(h+db*fb.C),a.drawingBufferWidth/x*(y-(n+gb*fb.C))),a.uniform2f(a.getUniformLocation(this.Ba,"uCircleTexturePosition"),a.drawingBufferWidth/x*(h+db*fb.C),y-(n+gb*fb.C)),a.uniform1f(a.getUniformLocation(this.Ba,"uCircleRadius"),ib),a.uniform3f(a.getUniformLocation(this.Ba,"uCircleColor"),
(mb>>16&255)/255,(mb>>8&255)/255,(mb&255)/255),a.uniform1f(a.getUniformLocation(this.Ba,"uCircleAlpha"),hb),a.uniform1f(a.getUniformLocation(this.Ba,"uCircleSoftness"),fb.J),a.uniform1f(a.getUniformLocation(this.Ba,"uAspectRatio"),a.drawingBufferWidth/a.drawingBufferHeight/(x/y)),a.drawArrays(a.TRIANGLE_FAN,0,this.$d.numberOfItems))}if(0<this.Bc[jb].length)for(bb=0;bb<this.Bc[jb].length;bb++)ab=this.Bc[jb][bb],fb=ab.radius*cb,ib=ab.alpha*ob,0>ib&&(ib=0),this.o.pa&&(a.useProgram(this.Ib),a.activeTexture(a.TEXTURE0),
a.bindTexture(a.TEXTURE_2D,this.Rq),a.bindBuffer(a.ARRAY_BUFFER,this.$d),a.vertexAttribPointer(this.Ib.la,this.$d.itemSize,a.FLOAT,!1,0,0),a.uniform2f(a.getUniformLocation(this.Ib,"uCanvasDimensions2"),x,y),a.uniform2f(a.getUniformLocation(this.Ib,"uCirclePosition"),h+db*ab.C,y-(n+gb*ab.C)),a.uniform2f(a.getUniformLocation(this.Ib,"uRingTexturePosition"),a.drawingBufferWidth/x*(h+db*ab.C),y-(n+gb*ab.C)),a.uniform1f(a.getUniformLocation(this.Ib,"uCircleRadius"),fb),a.uniform2f(a.getUniformLocation(this.Ib,
"uRingPosition"),h+db*ab.C,y-(n+gb*ab.C)),a.uniform1f(a.getUniformLocation(this.Ib,"uRingRadius"),fb),a.uniform1f(a.getUniformLocation(this.Ib,"uRingAlpha"),ib),a.uniform1f(a.getUniformLocation(this.Ib,"uAspectRatio"),a.drawingBufferWidth/a.drawingBufferHeight/(x/y)),a.uniform1i(a.getUniformLocation(this.Ib,"uSampler"),0),a.drawArrays(a.TRIANGLE_FAN,0,this.$d.numberOfItems))}this.o.pa&&(a.useProgram(this.o.D.Ig.R),a.disable(a.BLEND),a.enable(a.DEPTH_TEST))}}}}}class xd{constructor(a,b){this.o=a;this.O=
b;let e,f,h=this.__div=document.createElement("div");h.ggVisible=!0;e=document.createElement("img");let n;b.od?(n=b.od,e.setAttribute("style","position: absolute; width: "+b.uf+"px; height: "+b.Dd+"px; top: -"+b.Dd/2+"px; left: -"+b.uf/2+"px; cursor: pointer; "+a.Tb+"user-select: none; max-width: none;")):(n="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA5xJREFUeNqclmlIVFEUx997TjrplFQW2WKBBSYtRFlpWUILSSsRZRQIBdGHCFqIoKIvQRsUFRJC9LEgaSFbMMpcWi1pLzOLsjItKms0U5t5/c/wH7nc5o2jF374xrv87z33nHOPaRsRtbFgDpgJxoD+wATfwDNQDK6CyrCr5OcbhgiGIRsUAZt4QTWoIFXgp9JfAhY7rgdBl8NeBoLDYBloA+dBOagFTcDHcVEgDgwBGWA+OAcugvXgvb5wKMGJoAAMp9BpUA96EBf/Btsf8BI8AWfAErAcpHHDZeriliY2AVwDg8AucAQ0Ag+I4XhTm2Oxz8PT46KMbTx5EZjuJDgAnAVusJUm9DhYwalFcc59sIXXIaceFkowDySBPTRPL20xm+b7zYXa+N3CPrWJ6GuwGySA40HLBHc/GywFhbS5R1lEBrZy7FQwiSaX9pmnqeAYt+KUcew7BVZw/QKTq0ocpYPVvDOXItZCk2xgDIZqL8BR8Ab0VDbr4yZOgLeIwzQx6WiQxcCt1+6sld66L4yYtFSwF4yg2dU7/cEwGW9YVkAwmycp1dzdpvgm0DcCh4kHmxWzBls0uBX4qqmZJ4KzePm1IeJLgjmlC16aDKZpp5Q168B3o6wsSwTHgU+MIUs74RSj6y1d+212HKimJlUE+tFRfJpYtOKNXWmJTASqWf2Bu/R6+4TKHOrOzG4IhptjWgHbGkZvepQ6SQK7oRuCXzjX1DJavBEX1ygfT8FgBqpfm1zRDcEKbR2bsZlkJCdXieB1ZhZ5YtqVgXIPN+m9kbY6hpdb+d9fPncJRmZmqQheZkemJmgxyxykl3XWJEkcAl7N21s7PDcl5ZJ0PAa3wVwmWtVbZafPwQ7wLozYB7ATPNJO56d/LAikP9u+66KNJS1d4IOZp7wU0hfLukUyzgwm70T2N/DOxIy/eFdqawa5DL2NEGwP5k15Ja4woz9glvcomd9NzyvkFcQo5gomaLfm5c0svnKZ2k7q7+FauvR2MJKZR3+sY5WgtvkdG6JyELGhNHMTXyGfLviRJ5Tcd4Dlhle7086Sgp8CqVxDkn4OqHaqacr5ekjy3Q/W0FRNNGmoMtamdzdxsytZC0lqXKhEgWPVVgImg2NgFT1MHOoOk3yLEtgWN5TEOYvoIFI1rGM19//2wpAD7imF7lfwENwAxaASNCj90pcLLKdC2Iyw1M9gnEplMEp5kOU1f8WwKGJm8oUr9f8JMAAVMDM6HSDa9QAAAABJRU5ErkJggg%3D%3D",
e.setAttribute("style","position: absolute; width: 28px; height: 28px; top: -14px; left: -14px; cursor: pointer; "+a.Tb+"user-select: none; max-width: none;"));e.setAttribute("src",n);e.ondragstart=function(){return!1};h.appendChild(e);n="position:absolute;"+(a.Tb+"user-select: none;");n+=a.Tb+"touch-callout: none;";n+=a.Tb+"tap-highlight-color: rgba(0,0,0,0);";a.Mb&&!a.pa&&(n+=a.Tb+"transform: translateZ(9999999px);");h.setAttribute("style",n);h.onclick=function(){a.Ye(b);a.kk(a.Ka(b.url),b.target);
a.ka&&a.ka.hotspotProxyClick&&a.ka.hotspotProxyClick(b.id,b.url);a.G("hsproxyclick",{id:b.id,url:b.url})};h.ondblclick=function(){a.ka&&a.ka.hotspotProxyDoubleClick&&a.ka.hotspotProxyDoubleClick(b.id,b.url);a.G("hsproxydblclick",{id:b.id,url:b.url})};let r=a.P.Mk;r.enabled&&""!=b.title&&(f=document.createElement("div"),n="position:absolute;",n=b.od?a.Yl()?n+("top: -"+(b.Dd/2+20)+"px;"):n+("top: "+b.Dd/2+"px;"):a.Yl()?n+"top:\t -50px;":n+"top:\t 20px;",n=r.gj?n+"white-space: pre-wrap;":n+"white-space: nowrap;",
n+=a.Tb+"transform-origin: 50% 50%;",f.setAttribute("style",n+"visibility: hidden;overflow: hidden;padding: 0px 1px 0px 1px;"),f.style.color=this.o.Gb(r.Nk,r.Lk),f.style.backgroundColor=r.background?this.o.Gb(r.fc,r.ec):"transparent",f.style.border="solid "+this.o.Gb(r.ic,r.hc)+" "+r.lj+"px",f.style.borderRadius=r.nl+"px",f.style.textAlign="center",0<r.width?(f.style.left=-r.width/2+"px",f.style.width=r.width+"px"):f.style.width="auto",f.style.height=0<r.height?r.height+"px":"auto",f.style.overflow=
"hidden",f.innerHTML=a.Ka(b.title),f.classList.add("ggskin"),f.classList.add("ggdefaulthotspot"),h.addEventListener("mouseover",function(){0==r.width&&(f.style.left=-f.offsetWidth/2+"px");f.style.visibility="inherit";a.ka&&a.ka.hotspotProxyOver&&a.ka.hotspotProxyOver(b.id,b.url);a.G("hsproxyover",{id:b.id,url:b.url})}),h.addEventListener("mouseout",function(){f.style.visibility="hidden";a.ka&&a.ka.hotspotProxyOut&&a.ka.hotspotProxyOut(b.id,b.url);a.G("hsproxyout",{id:b.id,url:b.url})}),h.appendChild(f),
this.Kr=f);h.addEventListener("mouseover",function(){a.Ye(b)});h.addEventListener("mouseout",function(){a.Ye(null)})}Nt(){this.Kr&&(this.Kr.innerHTML=this.o.Ka(this.O.title))}}class tb{constructor(a,b){this.x=a;this.y=b}init(a,b){this.x=a;this.y=b}ge(a,b,e){let f=b.y-a.y;this.x=a.x+(b.x-a.x)*e;this.y=a.y+f*e}rs(a,b,e,f,h){var n=new tb;n.ge(a,e,h);a=new tb;a.ge(e,f,h);e=new tb;e.ge(f,b,h);b=new tb;b.ge(n,a,h);n=new tb;n.ge(a,e,h);a=new tb;a.ge(b,n,h);this.x=a.x;this.y=a.y}ml(a,b,e,f,h){let n=new tb,
r=.5,w=.25,x;do n.rs(a,b,e,f,r),x=n.x-h,r=0<x?r-w:r+w,w/=2;while(.01<Math.abs(x));this.x=n.x;this.y=n.y}}class yd{constructor(){this.si={Lh:1,Mh:1,gi:0,hi:0,fj:0,$k:0,scale:1};this.Xb=!0;this.bounds=[]}}class zd{constructor(){let a;this.yf=Array(6);for(a=0;6>a;a++)this.yf[a]=new yd}checkVisibility(a,b,e,f){for(let n=0;6>n;n++){let r;if(r=this.yf[n]){let w;w=[];w.push(new lb(-1,-1,-1,0,0));w.push(new lb(1,-1,-1,1,0));w.push(new lb(1,1,-1,1,1));w.push(new lb(-1,1,-1,0,1));for(var h=0;h<w.length;h++)4>
n?w[h].Ca(-Math.PI/2*n):w[h].ya(Math.PI/2*(4===n?-1:1)),f&&(w[h].Sa(f.Y*Math.PI/180),w[h].ya(-f.pitch*Math.PI/180)),w[h].Ca(-a*Math.PI/180),w[h].ya(b*Math.PI/180),w[h].Sa(e*Math.PI/180);r.Xb=0<w.length}}}}class Xb{constructor(a){this.pan={m:0,Ua:0,min:0,max:360,d:0,Cm:0,Ub:0,open:0};this.u={m:0,Ua:0,min:-90,max:90,d:0,Ub:0,open:0};this.Y={m:0,Ua:0,min:-180,max:180,d:0,open:0};this.Zc={pan:0,u:-90,Y:0,fov:170,rb:9};this.fov={m:70,Ua:70,min:1,ki:0,max:170,ym:360,zm:270,prev:0,d:0,Yc:{enabled:!1,x:0,
y:0},mode:0,Br:0,Co:0,open:0};this.cube=new zd;this.Ig=null;this.qb={Y:0,pitch:0};this.Zf=this.pc=!1;this.lk=this.Aq=4;this.cd=this.zq=this.Em=0;this.M=new qd;this.ji=200;this.Xi=0;this.Cn=5;this.pe=0;this.Gr=50;this.Wt=0;this.B={src:[],rf:4,width:640,height:480,Mc:!1,Eg:!1,Ik:!0,Hk:!1,Ce:"loop",Kk:"",j:null,Bn:!1,texture:null,Vk:null,Wi:null,Oj:null,format:1,Th:0,flipY:1,gb:function(){return 0},cn:function(){},loop:function(){return!1}};this.Mf={pan:-1,u:-1,fov:-1};this.vm=0;this.Ah=[];this.El="";
this.Bh=[];this.dh=this.bf=1;this.Mm=new Gb;this.o=a}ra(){this.o.ra(!1)}update(){this.o.update()}be(){return this.pan.m}Dj(){let a=this.pan.m;if(!this.Ia()){for(;-180>a;)a+=360;for(;180<a;)a-=360}return a}ce(){let a=this.pan.m-this.pan.Cm;if(!this.Ia()){for(;-180>a;)a+=360;for(;180<a;)a-=360}return a}Nd(a){this.ra();isNaN(a)||(this.pan.m=Number(a));this.update()}Fi(a){this.ra();isNaN(a)||(this.pan.m=Number(a)+this.pan.Cm);this.update()}wh(a,b){isNaN(a)||(this.Nd(this.be()+a),b&&(this.pan.d=a))}sl(a,
b){this.wh(a*this.Kh(),b)}Kh(){return Math.min(1,2*Math.tan(Math.PI*Math.min(this.fov.m,90)/360))}Bf(){return this.u.m}Od(a){this.ra();isNaN(a)||(this.u.m=Number(a));this.update()}xh(a,b){this.Od(this.Bf()+a);b&&(this.u.d=a)}ul(a,b){this.xh(a*this.Kh(),b)}Tg(a){this.ra();isNaN(a)||(this.Y.m=Number(a));this.update()}Gj(){return this.Y.m}rd(){return this.fov.m}Jb(a){this.ra();switch(this.Z()){case 4:var b=170;break;case 12:b=360;break;case 9:b=355;break;default:b=170}this.Ia()&&(b=9999999999);return!isNaN(a)&&
0<a&&a<b&&(b=this.fov.m,this.fov.m=1*a,b!=this.fov.m)?(this.update(),!0):!1}en(a){0<=a&&4>a&&(this.fov.mode=Number(a),this.Xd(),this.update())}Ia(){return 2==this.cd}cu(){return 0<this.M.levels.length}vh(a,b){return this.Jb(this.rd()+a)?(b&&(this.fov.d=a),!0):!1}mj(a,b){if(!isNaN(a)){let e;e=a/90*Math.cos(Math.min(this.fov.m,90)*Math.PI/360);e=this.fov.m*Math.exp(e);this.Jb(e);b&&(this.fov.d=a)}}Dk(a,b){this.ra();isNaN(a)||(this.pan.m=Number(a));isNaN(b)||(this.u.m=Number(b));this.update()}kn(a,b,
e){this.ra();!isNaN(a)&&1E7>a&&-1E7<a&&(this.pan.m=Number(a));!isNaN(b)&&1E7>b&&-1E7<b&&(this.u.m=Number(b));!isNaN(e)&&1E7>e&&-1E7<e&&(this.Y.m=Number(e));this.update()}Sg(a,b,e){this.ra();isNaN(a)||(this.pan.m=Number(a));isNaN(b)||(this.u.m=Number(b));isNaN(e)||this.Jb(e);this.update()}xk(){this.Sg(this.pan.Ua,this.u.Ua,this.fov.Ua)}ae(){return Number(this.o.H.height)/(2*Math.tan(Math.PI/180*(this.Eb()/2)))}Hd(a,b,e){var f=this.o;a||(a=f.ca.x,b=f.ca.y);if(this.Ia())e=this.fov.m/f.H.height,a=-(a-
f.H.width/2)*e+this.pan.m,b=-(b-f.H.height/2)*e+this.u.m;else{f=new lb(0,0,1);b=this.Ej(a,b);f.bc(-b.tilt);f.Sc(b.pan);f.bc(-this.u.m);f.Sc(-this.pan.m);e||(f.bc(-this.qb.pitch),f.ke(this.qb.Y));for(a=f.kf()-180;-180>a;)a+=360;b=f.lf()}e={};e.pan=a;e.tilt=b;return e}Ej(a,b){var e=this.o;a||(a=e.ca.x,b=e.ca.y);var f=e.H;e=f.height/(2*Math.tan(this.Eb()*Math.PI/360));a-=f.width/2;b-=f.height/2;f={};f.pan=180*Math.atan(a/e)/Math.PI;f.tilt=180*Math.atan(-b/Math.sqrt(a*a+e*e))/Math.PI;return f}$a(a,b,
e){let f=b?Number(b):0;if(0!=a&&4!=a&&12!=a&&9!=a)this.o.Yb("Unsupported projection type: "+a);else if(b&&0!==f&&4!==f&&12!==f&&9!==f)this.o.Yb("Unsupported projection2 type: "+f);else if(a==f&&(f=0),this.zq=e?Number(e):1,this.lk!=a||this.Em!=f)this.lk=a,this.Em=f,this.Ig.Pj()}Z(){return 0==this.lk?4:this.lk}Ic(){return this.Em}Qh(){return this.Aq}Fj(){return this.zq}aspect(){return this.o.H.width/this.o.H.height}$p(){return 4==this.Z()&&0==this.Ic()}Eb(a,b){a||(a=this.fov.m);b||(b=this.Z());return this.mg(b,
1/this.Ro(this.o.H),a)}mg(a,b,e){return this.Ia()?b*e:4==a?360*Math.atan(b*Math.tan(e/2*Math.PI/180))/Math.PI:9==a?720*Math.atan(b*Math.tan(e/4*Math.PI/180))/Math.PI:b*e}Wl(a,b){a||(a=this.Eb());b||(b=this.Z());return this.mg(b,this.aspect(),a)}Vl(a,b){a||(a=this.Eb());b||(b=this.Z());let e=this.aspect();e=Math.sqrt(1+e*e);return this.mg(b,e,a)}lo(a){var b;b||(b=this.Z());return this.mg(b,this.Ro(this.o.H),a)}Ro(a){let b=1;switch(this.fov.mode){case 0:b=1;break;case 1:b=a.width/a.height;break;case 2:b=
Math.sqrt(a.width*a.width+a.height*a.height)/a.height;break;case 3:b=4*a.height/3>a.width?1:3*a.width/(4*a.height)}return b}Ji(a){this.Jb(this.lo(a))}gn(a){let b=this.Z(),e=1/this.aspect();this.Ji(this.mg(b,e,a))}dn(a){let b=this.Z(),e=this.aspect();e=1/Math.sqrt(1+e*e);this.Ji(this.mg(b,e,a))}Xd(){if(!this.o.ef){var a=this.Mm;a.pan=this.pan.m;a.u=this.u.m;a.fov=this.fov.m;this.lg(a);this.lg(a);this.lg(a);this.pan.m=a.pan;this.u.m=a.u;this.fov.m!=a.fov&&(this.fov.m=a.fov);this.o.G("checklimits",{pan:a.pan,
tilt:a.u,fov:a.fov})}}lg(a){var b=this.aspect();var e=this.o.H;var f=this.o;let h=f.A;if(this.Ia()){if(0<this.fov.ki){var n=this.bf;this.M.levels&&0<this.M.levels.length&&(n=this.M.levels[0].height);10<n&&(this.fov.min=100*e.height/(n*this.fov.ki))}n=a.fov/2;e=n*b;var r=this.M.width/this.M.height*50;b=f.K.Uq?2*Math.min(50,r/b):2*Math.max(50,r/b);a.fov<this.fov.min&&(a.fov=this.fov.min);a.fov>b&&(a.fov=b);50<n?a.u=0:(50<a.u+n&&(a.u=50-n),-50>a.u-n&&(a.u=-50+n));e>r?a.pan=0:(a.pan+e>r&&(a.pan=r-e,f.A.active&&
(f.A.speed=-f.A.speed,this.pan.d=0)),a.pan-e<-r&&(a.pan=-r+e,h.active&&(h.speed=-h.speed,this.pan.d=0)))}else{0<this.fov.ki&&(n=this.bf,this.M.levels&&0<this.M.levels.length&&(n=this.M.levels[0].height),10<n&&(this.fov.min=360*Math.atan2(e.height/2,n/2*this.fov.ki)/Math.PI));a.fov<this.fov.min&&(a.fov=this.fov.min);r=this.fov.max;var w=179;n=this.Eb()/2;e=b*n;4==this.Z()?e=180*Math.atan(b*Math.tan(n*Math.PI/180))/Math.PI:9==this.Z()?(r=this.fov.zm,w=355):12==this.Z()&&(r=this.fov.ym,w=360);f.pa||
(r=Math.max(160,r));a.fov>r&&(a.fov=r);12==this.Z()&&(2*e>w&&(a.fov=w/b),n=this.Eb()/2,2*n>w&&(a.fov=w),n=this.Eb()/2,e=b*n);2*n>this.u.max-this.u.min&&180>this.u.max-this.u.min&&(n=(this.u.max-this.u.min)/2,a.fov=this.lo(2*n));90>this.u.max?a.u+n>this.u.max&&(a.u=this.u.max-n):a.u>this.u.max&&(a.u=this.u.max);-90<this.u.min?a.u-n<this.u.min&&(a.u=this.u.min+n):a.u<this.u.min&&(a.u=this.u.min);f=this.pan.max-this.pan.min;359.99>f&&(a.pan<this.pan.min&&a.pan+360<this.pan.max&&(a.pan+=360),this.pan.m>
this.pan.max&&a.pan-360>this.pan.min&&(a.pan-=360),b=90,r=Math.tan(n*Math.PI/180),w=Math.tan((Math.abs(a.u)+n)*Math.PI/180),w=Math.sqrt(w*w+1)/Math.sqrt(r*r+1),n=180*Math.atan(w*Math.tan(e*Math.PI/180))/Math.PI,2*n>f&&(w=Math.tan(f*Math.PI/360)/Math.tan(e*Math.PI/180),f=w*Math.sqrt(r*r+1),w=Math.sqrt(f*f-1),b=180/Math.PI*Math.atan(w)),a.pan+n>this.pan.max&&(a.pan=this.pan.max-n,h.active&&(h.speed=-h.speed,this.pan.d=0)),a.pan-n<this.pan.min&&(a.pan=this.pan.min+n,h.active&&(h.speed=-h.speed,this.pan.d=
0)),a.u+e>b&&(a.u=b-e),a.u-e<-b&&(a.u=-b+e))}}bh(a,b,e,f,h){let n=this.M.wm-1-b,r=this.M.xm,w="x";switch(a){case 0:w="f";break;case 1:w="r";break;case 2:w="b";break;case 3:w="l";break;case 4:w="u";break;case 5:w="d";break;case "vr":w="vr"}h&&(a+="l",w+="l");h=this.o.Nq;for(let x=0;3>x;x++)r=h(r,"c",a),r=h(r,"s",w),r=h(r,"r",b),r=h(r,"l",n),r=h(r,"x",e),r=h(r,"y",f),r=h(r,"v",f),r=h(r,"h",e);return this.o.Ha(r)}Ct(a,b,e){e.fx=a.width/this.M.W*b.Lh;e.fy=a.height/this.M.W*b.Mh;e.Kd=a.width/this.M.W*
b.gi;e.Ld=a.height/this.M.W*b.hi;e.fx=Math.min(Math.max(0,Math.floor(e.fx)),a.$-1);e.fy=Math.min(Math.max(0,Math.floor(e.fy)),a.sa-1);e.Kd=Math.min(Math.max(0,Math.floor(e.Kd)),a.$-1);e.Ld=Math.min(Math.max(0,Math.floor(e.Ld)),a.sa-1)}dw(){if(!(6>this.cube.yf.length))for(let e=0;6>e;e++){var a=void 0;a=this.cube.yf[e];let f;f=[];f.push(new lb(-1,-1,-1,0,0));f.push(new lb(1,-1,-1,1,0));f.push(new lb(1,1,-1,1,1));f.push(new lb(-1,1,-1,0,1));for(var b=0;4>b;b++)4>e?f[b].Ca(-Math.PI/2*e):f[b].ya(Math.PI/
2*(4==e?-1:1)),this.qb&&(f[b].Sa(this.qb.Y*Math.PI/180),f[b].ya(-this.qb.pitch*Math.PI/180)),f[b].Sc(-this.pan.m),f[b].bc(this.u.m),f[b].ke(this.Y.m);f=this.o.wl(f);a.Xb=0<f.length;if(a.Xb){a=a.si;a.Lh=f[0].yd;a.gi=f[0].yd;a.Mh=f[0].v;a.hi=f[0].v;for(b=1;b<f.length;b++)a.Lh=Math.min(a.Lh,f[b].yd),a.gi=Math.max(a.gi,f[b].yd),a.Mh=Math.min(a.Mh,f[b].v),a.hi=Math.max(a.hi,f[b].v);a.fj=a.gi-a.Lh;a.$k=a.hi-a.Mh;a.scale=Math.max(a.fj,a.$k)}else a.si.fj=-1,a.si.$k=-1}}kq(){for(var a=0;a<this.M.levels.length;a++){let e=
this.M.levels[a];for(var b in e.Oa)e.Oa.hasOwnProperty(b)&&(e.Oa[b].visible=!1)}}Po(){let a=0,b=this.o.H,e=Math.tan(Math.min(this.Eb(),175)*Math.PI/360),f=b.height/(2*e);f*=1+b.width/b.height*e/2;for(f*=Math.pow(2,1<this.o.devicePixelRatio?this.M.iq:this.M.hq);this.M.levels.length>=a+2&&!this.M.levels[a+1].Mg&&this.M.levels[a+1].width>f;)a++;return a}Mu(){if(this.o.K.Tq&&this.o.K.mc){var a=this.Mm;a.pan=this.pan.m;a.u=this.u.m;a.fov=this.fov.m;this.lg(a);this.lg(a);this.lg(a);let b=a.pan-this.pan.m,
e=a.u-this.u.m;a=a.fov-this.fov.m;if(0!=b||0!=e||0!=a){let f;f=.2+.9*Math.min((Math.abs(b)+Math.abs(e)+Math.abs(a))/Math.abs(Math.min(this.fov.m,90))*.3,1);this.pan.m+=b*f;this.u.m+=e*f;this.fov.m+=a*f;this.o.qa.bn=.3;this.update()}else this.o.qa.bn=0}else this.Xd();if(isNaN(this.pan.m)||1E7<this.pan.m||-1E7>this.pan.m)this.pan.m=0;if(isNaN(this.u.m)||1E7<this.u.m||-1E7>this.u.m)this.u.m=0;if(isNaN(this.Y.m)||1E7<this.Y.m||-1E7>this.Y.m)this.Y.m=0;if(!this.Ia()){for(;360<this.pan.m;)this.pan.m-=360;
for(;-360>this.pan.m;)this.pan.m+=360}}}class Eb extends wb{Mt(){return this.N}constructor(a,b){super(a,b);this.D=new Xb(this);this.cd=0;this.H={width:10,height:10};this.Fl=new lb;this.crossOrigin="anonymous";this.de=this.fg=0;this.es={};this.Zf=this.pc=!1;this.oa={start:{x:0,y:0},na:{x:0,y:0},he:{x:0,y:0},m:{x:0,y:0},ma:{x:0,y:0}};this.U={bd:!1,pd:!1,Sj:function(){return this.pd||this.bd},Qo:0,startTime:0,start:{x:0,y:0},na:{x:0,y:0},he:{x:0,y:0},m:{x:0,y:0},ma:{x:0,y:0},ui:!1,Zg:null};this.De=this.Zd=
!0;this.qa={enabled:!0,na:{x:0,y:0},ma:{x:0,y:0},bn:0,fov:{active:!1,kc:0,center:{enabled:!1,x:0,y:0}}};this.ob={alpha:0,beta:0,gamma:0,orientation:0,Rf:0,Ci:!0,fadeIn:0};this.Uh={alpha:0,beta:0,gamma:0,orientation:0};this.B={src:[],rf:4,width:640,height:480,Mc:!1,Eg:!1,Ik:!0,Hk:!1,level:1,Ce:"loop",Kk:"",j:null,Bn:!1,texture:null,Vk:null,Wi:null,Oj:null,format:1,Th:0,flipY:1,gb:function(){return 0},cn:function(){},loop:function(){return!1}};this.control=this.T=this.Gc=this.La=this.ia=null;this.md=
[];this.Ma=!1;this.Ae=1;this.aa=null;this.te=this.hf=this.ee=!1;this.th=0;this.Sd=.02;this.kl=0;this.ll=!1;this.jl=this.jj=this.uh=this.gf=this.eo=0;this.bo=-1;this.wc="";this.$m=this.Xe=!1;this.Ah=[];this.El="";this.Bh=[];this.dh=this.bf=1;this.Pg=!1;this.A={enabled:!1,timeout:5,active:!1,Yg:!1,speed:.4,Ok:0,Rp:!1,ik:0,uq:!0,Hg:"",tq:!1,cg:!1,gd:!1,kj:"",uo:!1,Td:"Animation01",gg:!1,gk:!1,yn:!1,startTime:0,td:0,Jh:!1,sj:!1,bk:0,Jd:0,ci:0,di:0,bi:0,eq:0};this.I={active:!1,aborted:!1,speed:.1,pan:0,
u:0,Y:0,fov:70,Ui:70,ci:0,di:0,fq:0,bi:0,rb:0,xi:0,lastTime:0,Ln:!1,Gg:!1,ao:0,$n:0,Nj:!1};this.Wk=null;this.vm=0;this.ye=[];this.co={};this.rc={};this.Oc={};this.Fg={};this.L=[];this.qr=!1;this.X=[];this.V=[];this.Ya=[];this.Qb=[];this.df=[];this.bb=[];this.za=[];this.Yf=[];this.Ue=this.Sb=null;this.da=[];this.Fu={target:0,current:0,Bd:.01,delay:2,Ll:0,tj:!1,qs:!1};this.K={fi:!1,iu:!1,qc:!1,Qe:!1,mc:!0,Wp:!1,qn:1,Tq:!1,re:!0,Hl:!0,yl:!0,zl:!0,vo:!1,Kj:!1,Uq:!0,sensitivity:8};this.Eh=!0;this.ej=this.kh=
this.jh=this.ef=this.Mn=this.pa=!1;this.jw=!0;this.jm=this.kw=!1;this.Rl=!0;this.mh=this.gm=this.im=!1;this.Yr=!0;this.Tb="";this.Hc="transition";this.Be="transform";this.wj="perspective";this.Xa={width:0,height:0};this.Uo=new lb;this.To=new lb;this.Vo=new lb;this.Wo=new lb;this.So=new lb;this.sd=!1;this.Uc=this.cq=this.ua="";this.Xk=[];this.Bm=[];this.em=!1;this.dj=!0;this.ha=this.F=null;this.yh=!1;this.ab=null;this.Da={enabled:!1,speed:1,running:!1,finished:!0,Fd:{pan:0,u:0,fov:0,rb:0,Xr:!1}};this.Nc=
null;this.zh=function(f,h){if(0==f.length)return f;let n,r,w,x,y,z,cb,ab;ab=[];n=h.qk(f[0])-0;for(x=0;x<f.length;x++){z=x;cb=x+1;cb==f.length&&(cb=0);r=h.qk(f[cb])-0;if(0<=n&&0<=r)ab.push(f[z]);else if(0<=n||0<=r)w=r/(r-n),0>w&&(w=0),1<w&&(w=1),y=new lb,y.ge(f[z],f[cb],w),0>n||ab.push(f[z]),ab.push(y);n=r}return ab};this.Mm=new Gb;this.Qk=1;this.$j=-1;this.hk=1;this.Sl=this.Ml=this.Tm=this.sk=0;this.bq=!1;this.Gd=!0;this.Ev();this.Sn="aHR0cHM6Ly9wYW5vMnZyLmNvbQ==";this.Tn="Q3JlYXRlZCB3aXRoIFBhbm8yVlI=";
this.jm=pb();this.pa=qb();this.jh=this.jm;this.pa&&(this.jh=!1);this.Xe=!0;this.$m=!1;if(this.Lc||this.Wh)this.dr(80),this.D.Cn=2;var e="Pano2VR player "+this.cm();this.Ff()&&(e+=", THREEjs "+eb.REVISION);this.Np()&&(e+=", HSL.js");e+=", "+(this.jm?"CSS 3D available":"CSS 3D not available");e+=", "+(this.pa?(this.Mn?"WebGL2":"WebGL")+" available":"WebGL not available");this.Yb(e);this.addListener("languagechanged",()=>{this.vs();this.ws()});document.addEventListener("visibilitychange",()=>{});this.Va=
new Rb(this);this.Va.Ke=!1;7<this.dk.length&&(e=Eb.Dh("bG9n"),window[Eb.Dh("Y29uc29sZQ==")][e](Eb.Do(this.dk)));this.Ye(this.Va);this.Bs();this.dj=this.Yp?this.Mb&&this.uk&&10<=Number(this.Ob)?!0:!1:!0;b&&b.hasOwnProperty("webGLFlags")&&b.webGLFlags&&(this.es=b.webGLFlags);this.Nc=new wd(this);this.userdata=this.dg=this.tg();this.emptyHotspot=this.Va;this.mouse=this.ca;this.F=new gd(this);this.ab=new vd(this);this.Cl(a);this.da.push(new Wb(this,this.D));this.da.push(new Wb(this,new Xb(this)));this.ja=
new md(this);this.ps();this.ra(!1);this.fd()}cm(a){return-99991===a?this.hu:"7.1.9/20995"}Bl(){this.F.enabled=this.ha.enabled;this.F.type=this.ha.type;this.F.ea=this.ha.zoomin;this.F.Vc=this.ha.zoomout;this.F.tf=this.ha.blendtime;this.F.ph=this.ha.zoomoutpause;this.F.fl=this.ha.zoomfov;this.F.cl=this.ha.zoomafterinfov;this.F.el=this.ha.zoomafteroutfov;this.F.qh=this.ha.zoomspeed;this.F.hg=this.ha.blendcolor;this.F.dd=this.ha.softedge;this.ha=null}vv(a){this.ha={};this.ha.enabled=!0;this.ha.type=
this.F.type;this.ha.zoomin=this.F.ea;this.ha.zoomout=this.F.Vc;this.ha.blendtime=this.F.tf;this.ha.zoomoutpause=this.F.ph;this.ha.zoomfov=this.F.fl;this.ha.zoomafterinfov=this.F.cl;this.ha.zoomafteroutfov=this.F.el;this.ha.zoomspeed=this.F.qh;this.ha.blendcolor=this.F.hg;this.ha.softedge=this.F.dd;if(a.hasOwnProperty("type")){var b=a.type;"cut"==b||"crossdissolve"==b||"diptocolor"==b||"irisround"==b||"irisroundcenter"==b||"irisrectangular"==b||"irisrectangularcenter"==b||"wipeleftright"==b||"wiperightleft"==
b||"wipetopbottom"==b||"wipebottomtop"==b||"wiperandom"==b||"mesh"==b?this.ha.type=b:this.Yb("unknown transition type")}a.hasOwnProperty("before")&&(b=Number(a.before),0==b||2==b)&&(this.ha.zoomin=b);a.hasOwnProperty("after")&&(b=Number(a.after),0==b||2==b||3==b||4==b)&&(this.ha.zoomout=b);a.hasOwnProperty("transitiontime")&&(b=Number(a.transitiontime),0<=b&&50>=b&&(this.ha.blendtime=b));a.hasOwnProperty("waitfortransition")&&(this.ha.zoomoutpause=1==a.waitfortransition);a.hasOwnProperty("zoomedfov")&&
(b=Number(a.zoomedfov),.01<=b&&50>=b&&(this.ha.zoomfov=b));a.hasOwnProperty("zoomafterinfov")&&(b=Number(a.zoomafterinfov),70<=b&&120>=b&&(this.ha.zoomafterinfov=b));a.hasOwnProperty("zoomafteroutoutfov")&&(b=Number(a.zoomafteroutfov),.01<=b&&70>=b&&(this.ha.zoomafteroutfov=b));a.hasOwnProperty("zoomspeed")&&(b=Number(a.zoomspeed),.01<=b&&99>=b&&(this.ha.zoomspeed=b));a.hasOwnProperty("dipcolor")&&(this.ha.blendcolor=a.dipcolor);a.hasOwnProperty("softedge")&&(a=Number(a.softedge),0<=a&&1E3>=a&&(this.ha.softedge=
a));this.yh||this.Bl()}$a(a,b,e){this.D.$a(a,b,e)}Z(){return this.D.Z()}Qh(){return this.D.Qh()}Ic(){return this.D.Ic()}Fj(){return this.D.Fj()}tl(a,b){if(0!=a&&4!=a&&12!=a&&9!=a)this.Yb("Unsupported projection type: "+a);else if(this.pa||0==a||4==a||this.Yb("Projection changes require WebGL!"),this.Z()!=a){let e={};const f=this.D;e.pan=f.pan.m;e.tilt=f.u.m;e.fov=f.fov.m;e.projection=a;e.timingFunction=3;e.speed=b;a=this.Nh(a);e.fov=Math.min(a,e.fov);this.mi(e)}}Bs(){if(!this.ga){try{window.AudioContext=
window.AudioContext||window.webkitAudioContext,this.ga=new AudioContext}catch(a){this.ga=null}this.Mb&&(!this.uk||9>Number(this.Ob))&&(this.ga=null);this.Mb&&!this.Lc&&12<=Number(this.Ob)&&13>Number(this.Ob)&&(this.ga=null);this.Mb&&this.Lc&&13==Number(this.Me[0])&&(1==Number(this.Me[1])||2<=Number(this.Me[1]))&&(this.ga=null)}}dr(a){this.D.ji=a}et(){return this.sd}tt(){return this.sd?this.sp().length:1}ct(){return this.A.active}Oh(){return this.isLoaded}kp(){return!this.isLoaded}ae(){return this.D.ae()}Li(a,
b){try{this.oc&&(a=window.innerWidth,b=window.innerHeight);let n=a-this.margin.left.ba-this.margin.right.ba,r=b-this.margin.top.ba-this.margin.bottom.ba;if(!(10>n||10>r)){var e=window.devicePixelRatio||1;this.Pg&&(e=1);this.ia.style.width=n+"px";this.ia.style.height=r+"px";this.ia.style.left=this.margin.left.ba+"px";this.ia.style.top=this.margin.top.ba+"px";if(this.pa)try{this.La&&(this.La.style.position="absolute",this.La.style.display="inline",this.La.style.width=n+"px",this.La.style.height=r+"px",
this.La.width=n*e,this.La.height=r*e),this.N&&(this.Xa.width=n*e,this.Xa.height=r*e,this.N.viewport(0,0,this.N.drawingBufferWidth,this.N.drawingBufferHeight))}catch(w){alert(w)}this.Gc&&(this.Gc.style.width=a+"px",this.Gc.style.height=b+"px",this.Gc.width=a,this.Gc.height=b);this.xa&&(this.xa.style.width=a+"px",this.xa.style.height=b+"px",this.aa&&this.aa!=this.xa&&(this.aa.style.width=a+"px",this.aa.style.height=b+"px"));this.ee&&(this.Ma=!0);var f=this.ia.offsetWidth,h=this.ia.offsetHeight;if(this.H.width!=
f||this.H.height!=h)this.H.width=f,this.H.height=h;this.iw();this.aa&&this.aa.ggUpdateSize&&this.aa.ggUpdateSize(a,b);this.G("sizechanged",{w:a,h:b});this.G("playerstatechanged",{})}}catch(n){}}fd(){this.Tr();this.Li(this.container.offsetWidth,this.container.offsetHeight)}po(a){0==a&&(this.K.mc=!1);1==a&&(this.K.mc=!0);2==a&&(this.K.mc=!this.K.mc);this.G("viewmodechanged",{});this.G("playerstatechanged",{})}Gp(){return 1==this.K.mc?1:0}xs(a){this.Nc&&(this.Nc.ln=0==a?!0:1==a?!1:!this.Nc.ln,this.update())}xp(){return this.P.mode}qo(){this.G("viewstatechanged",
{})}Hp(){return 0}lt(a){return"_firstmap"==a?"web":"_firstfloorplan"==a?"file":(a=this.rc[a])?a.type:"web"}kt(a){"_firstmap"==a?a=this.gp():"_firstfloorplan"==a&&(a=this.Bj());return(a=this.rc[a])?a:{}}gp(){let a="";for(let b in this.rc){let e=this.rc[b];"web"==e.type&&1E4>e.index&&(a=b)}return a}Bj(){let a="";for(var b in this.rc){let e=this.rc[b];"file"==e.type&&1E4>e.index&&(a=b)}return a}pt(a,b){"_firstfloorplan"==b&&(b=this.Bj());let e=[];a||(a=this.ua);let f=this.ye[a];f&&(a=f);""===a&&0<Object.keys(this.Oc).length&&
(a=Object.keys(this.Oc)[0]);this.Oc[a]&&this.Oc[a][b]&&(e.push(this.Oc[a][b].y),e.push(this.Oc[a][b].x));return e}qt(a,b){"_firstfloorplan"==b&&(b=this.Bj());let e=[];a||(a=this.ua);let f=this.ye[a];f&&(a=f);""===a&&0<Object.keys(this.Oc).length&&(a=Object.keys(this.Oc)[0]);this.Fg[a]&&this.Fg[a][b]&&(e.push(this.Fg[a][b].x),e.push(this.Fg[a][b].y));return e}mp(a){var b=this.ye[a];b&&(a=b);b=this.lp();let e=[];for(let f=0;f<b.length;f++)this.Oc[a]&&this.Oc[a][b[f]]&&e.push(b[f]);return e}jt(a){a=
this.mp(a);return 0<a.length?a[0]:""}lp(){let a=[];for(let b in this.rc)a.push(b);return a}Xs(){let a=[];for(let b in this.rc)"file"==this.rc[b].type&&a.push(b);return a}Ut(){for(let a in this.rc)if("web"==this.rc[a].type)return!0;return!1}Tt(){for(let a in this.rc)if("file"==this.rc[a].type)return!0;return!1}Yt(a,b,e){a=Math.atan2(a+1,e);var f=Math.atan2(b+1,e);b=Math.sin(a);e=Math.sin(f);a=Math.cos(a);f=Math.cos(f);this.Uo.init(0,0,-1);this.To.init(a,0,-b);this.Vo.init(-a,0,-b);this.Wo.init(0,f,
-e);this.So.init(0,-f,-e)}wl(a){a=this.zh(a,this.Uo);a=this.zh(a,this.To);a=this.zh(a,this.Vo);a=this.zh(a,this.Wo);return a=this.zh(a,this.So)}Sr(a){if(!this.Xe&&this.fu!=a){this.fu=a;let b;b=this.margin.left.ba+this.H.width/2+"px ";b+=this.margin.top.ba+this.H.height/2+"px ";this.xa.style[this.wj]=a+"px";this.xa.style[this.wj+"Origin"]=b;this.ia.style[this.wj]=a+"px";this.ia.style[this.wj+"Origin"]=b}}Xl(){return this.F.xd||this.F.Qd||!this.Ia()&&this.pa&&(4!=this.Z()||0!=this.Ic())?!1:!0}Sk(){const a=
this.D;let b=new lb(0,0,-100),e=this.ae();const f=100/a.fov.m*this.H.height;let h=this.Xl(),n=1E5,r=-1;for(let cb=0;cb<this.L.length;cb++){let ab=this.L[cb];if("point"==ab.type){var w=void 0,x=void 0;var y=!1;a.Ia()?(w=(a.pan.m-ab.pan)/100*f,x=(a.u.m-ab.u)/100*f,Math.abs(w)<this.H.width/2+500&&Math.abs(x)<this.H.height/2+500&&(y=!0)):(b.init(0,0,-100),b.ya(-ab.u*Math.PI/180),b.Ca(ab.pan*Math.PI/180),b.Ca(-a.pan.m*Math.PI/180),b.ya(a.u.m*Math.PI/180),b.Sa(a.Y.m*Math.PI/180),.01>b.z?(x=-e/b.z,w=b.x*
x,x*=b.y,Math.abs(w)<this.H.width/2+5E3&&Math.abs(x)<this.H.height/2+5E3&&(y=!0)):x=w=-1E4);ab.Ac=w+this.H.width/2;ab.ac=x+this.H.height/2;ab.Om=ab.Ac/this.H.width*100;ab.Pm=ab.ac/this.H.height*100;ab.visible=y;ab.closestToCenter=!1;if(y&&h){var z=Math.sqrt(w*w+x*x);n>z&&(n=z,r=cb)}ab.px=ab.Ac;ab.py=ab.ac;ab.pxp=ab.Om;ab.pyp=ab.Pm;ab.visible=ab.visible;if(ab.j&&ab.j.onUpdatePosition)ab.j.onUpdatePosition(this,ab);else ab.j&&ab.j.__div&&("none"!=ab.j.__div.style[this.Hc]&&(ab.j.__div.style[this.Hc]=
"none"),y&&h?(ab.j.ggUse3d?(this.Xe||this.Sr(e),this.Ia()?(ab.j.__div.style[this.Be]="scale("+(100/a.fov.m*this.H.height/ab.j.gg3dDistance).toFixed(10)+")",ab.j.__div.style.left=this.margin.left.ba+w+this.H.width/2+"px",ab.j.__div.style.top=this.margin.top.ba+x+this.H.height/2+"px"):(ab.j.__div.style.width="1px",ab.j.__div.style.height="1px",y="",this.Xe&&(y+="perspective("+e+"px) "),y+="translate3d(0px,0px,"+e+"px) ",y+="rotateZ("+a.Y.m.toFixed(10)+"deg) ",y+="rotateX("+a.u.m.toFixed(10)+"deg) ",
y+="rotateY("+(-a.pan.m).toFixed(10)+"deg) ",y+="rotateY("+ab.pan.toFixed(10)+"deg) ",y+="rotateX("+(-ab.u).toFixed(10)+"deg) ",y+="translate3d(0px,0px,"+(-1*ab.j.gg3dDistance).toFixed(10)+"px) ",ab.j.__div.style[this.Be+"Origin"]="0% 0%",ab.j.__div.style[this.Be]=y,ab.j.__div.style.left=this.margin.left.ba+this.H.width/2+"px",ab.j.__div.style.top=this.margin.top.ba+this.H.height/2+"px")):this.qr?(ab.j.__div.style.left=this.margin.left.ba+this.H.width/2+"px",ab.j.__div.style.top=this.margin.top.ba+
this.H.height/2+"px",ab.j.__div.style[this.Be]="translate3d(0px,0px,-1000px) rotateY(0.1deg) translate3d("+w+"px,"+x+"px, 0.5px)",ab.j.__div.style["transform-style"]="preserve-3d"):(ab.j.__div.style.left=this.margin.left.ba+w+this.H.width/2+"px",ab.j.__div.style.top=this.margin.top.ba+x+this.H.height/2+"px"),ab.j.__div.ggVisible&&(ab.j.__div.style.visibility="visible")):(ab.j.ggUse3d&&(ab.j.__div.style[this.Be]=""),ab.j.__div.style.left="-1000px",ab.j.__div.style.top="-1000px",ab.j.__div.style.visibility=
"hidden"))}if("poly"==ab.type)if(w=[],this.Ia())for(ab.Lg=[],y=0;y<ab.vertices.length;y++)w=ab.vertices[y],x=(a.pan.m-w.pan)/100*f,z=(a.u.m-w.u)/100*f,x+=this.margin.left.ba+this.H.width/2,z+=this.margin.top.ba+this.H.height/2,ab.Lg.push({Ac:x,ac:z});else{for(y=0;y<ab.vertices.length;y++)x=ab.vertices[y],b.init(0,0,-100),b.ya(-x.u*Math.PI/180),b.Ca(x.pan*Math.PI/180),b.Ca(-a.pan.m*Math.PI/180),b.ya(a.u.m*Math.PI/180),b.Sa(a.Y.m*Math.PI/180),w.push(b.clone());w=this.wl(w);if(0<w.length)for(y=0;y<w.length;y++)b=
w[y],.1>b.z?(z=-e/b.z,x=this.H.width/2+b.x*z,z=this.H.height/2+b.y*z):z=x=0,b.Ac=x,b.ac=z;ab.Lg=w}}-1!=r&&(this.L[r].closestToCenter=!0);this.G("hotspotsupdated",{})}ep(){let a=[];for(let b=0;b<this.L.length;b++){let e=this.L[b];"point"==e.type&&e.j&&e.j.__div&&a.push(e.j.__div)}return a}nj(a,b){let e=-1;if((0<=this.P.mode||0<this.P.dc.length)&&this.$p())for(let f=0;f<this.L.length;f++){let h=this.L[f];"poly"==h.type&&h.Lg&&0<h.Lg.length&&(-1!=this.P.mode||-1!=this.P.dc.indexOf(h.id))&&Eb.Qp(h.Lg,
a,b)&&(e=f,h.Ac=a,h.ac=b)}return 0<=e?this.L[e]:!1}$p(){return 4==this.Z()&&0==this.Ic()}Xd(){this.D.Xd()}update(a=0){this.Ma=!0;a&&(this.Ae=Math.max(1*a,this.Ae))}Zl(){return 0<this.D.Xi||0<this.D.pe}bj(){var a=Date.now();var b=this.N;this.da[0].En=1;3<this.Qk&&(this.Qk=-3);this.Qk+=.01;if(1<this.da.length){var e=this.da[0].D,f=this.da[1].D;f.$a(e.Z());f.pan.m=e.pan.m;f.u.m=e.u.m;f.fov.m=e.fov.m}e=this.da[0];f=e.D;b&&(e.Or(),b.clear(b.COLOR_BUFFER_BIT|b.DEPTH_BUFFER_BIT|b.STENCIL_BUFFER_BIT));this.Ia()?
(this.Sk(),this.pa&&(e.ew(),e.Bq())):0===this.cd&&(!this.pa||4==this.Z()&&0==this.Ic()?(b=this.ae(),this.Yt(this.H.width/2,this.H.height/2,b),this.Sk(),this.ej?e.hw():this.jw&&this.Wr(),this.In(),this.pa?(this.B.Mc?14==this.B.format?e.gw():e.fs():0<f.M.levels.length?e.pw():e.rw(),e.Bq()):this.kh&&this.cw(),this.Nc&&this.Nc.Gu()):(this.da[0].fs(),this.Sk(),this.Vt()));50<Date.now()-a?this.Pg||(2<this.pn?(this.Pg=!0,this.fd()):this.pn++):this.pn=0;this.ee&&e.D.M.Mq++;this.pa&&this.N&&(a=this.N,a.disable(a.BLEND),
a.disable(a.STENCIL_TEST),a.disable(a.DEPTH_TEST),a.disable(a.CULL_FACE))}cw(){this.Xd();let a;this.Gc&&(a=this.Gc.getContext("2d"));if(this.H.width!==this.ia.offsetWidth||this.H.height!==this.ia.offsetHeight)this.H.width=this.ia.offsetWidth,this.H.height=this.ia.offsetHeight;if(a){const b=this.D;let e=a.canvas.width/2,f=a.canvas.height/2,h=a.createRadialGradient(e,f,5,e,f,Math.max(e,f));h.addColorStop(0,"#333");h.addColorStop(1,"#fff");a.rect(0,0,a.canvas.width,a.canvas.height);a.fillStyle=h;a.fill();
a.fillStyle="#f00";a.font="20px Helvetica";a.textAlign="center";a.fillText("Pan: "+b.pan.m.toFixed(1),e,f-60);a.fillText("Tilt: "+b.u.m.toFixed(1),e,f-30);a.fillText("Fov: "+b.fov.m.toFixed(1),e,f+0);a.fillText("Node: "+this.qd(),e,f+30);a.fillText("Title: "+this.dg.title,e,f+60)}}nv(a){a=Math.round(a);this.Xe=0<(a&1);this.$m=0<(a&2);this.lh=0<(a&4);this.Pg=0<(a&8);4096<=a&&(this.jh=0<(a&4096),this.pa=0<(a&8192),this.kh=0<(a&32768))}zt(){let a=0;this.Xe&&(a|=1);this.$m&&(a|=2);this.lh&&(a|=4);this.jh&&
(a|=4096);this.pa&&(a|=8192);this.kh&&(a|=32768);return a}In(){let a=Math.round(this.ae());this.Xe||this.Sr(a);for(var b=0;b<this.Ya.length;b++){var e=void 0;e=this.Ya[b];e.Jn(a);e.j.hidden=!1}for(b=0;b<this.Qb.length;b++)e=this.Qb[b],e.Jn(a),e.j.hidden=!1}Wr(){let a=Math.round(this.ae());for(let b=0;b<this.V.length;b++){let e;e=this.V[b];e.vd||(e.Jn(a),e.j.hidden=this.F.xd?!0:!1)}}Vt(){for(var a=0;a<this.Ya.length;a++)this.Ya[a].Vf(!1);for(a=0;a<this.V.length;a++){let b;b=this.V[a];b.vd||b.Vf(!1)}for(a=
0;a<this.Qb.length;a++)this.Qb[a].Vf(!1)}iw(){try{for(let a=0;a<this.Jj();a++){let b=this.dm(a);b.vd||b.qe()}}catch(a){}}If(a){const b=this;b.Rl=!1;b.La=a?a:document.createElement("canvas");b.La.addEventListener("webglcontextlost",e=>{b.N=null;e.preventDefault();b.Yb("Pano2VR: Context lost")});b.La.addEventListener("webglcontextrestored",e=>{e.preventDefault();b.Up();b.Yb("Pano2VR: Context restored")});return b.Up()}Up(){const a=this;try{let b=a.container.offsetWidth-a.margin.left.ba-a.margin.right.ba,
e=a.container.offsetHeight-a.margin.top.ba-a.margin.bottom.ba;if(100>b||100>e)e=b=100;let f=window.devicePixelRatio||1;a.Pg&&(f=1);a.ia.style.width=b+"px";a.ia.style.height=e+"px";a.La.style.width=b+"px";a.La.style.height=e+"px";a.La.width=b*f;a.La.height=e*f;a.La.style.display="none";a.La.style.touchAction="none";a.ia.insertBefore(a.La,a.ia.firstChild);let h=a.es;h.stencil=!0;h.depth=!0;h.powerPreference="high-performance";a.Lc&&10<=a.Me[0]&&(h.antialias=!1,h.alpha=!1);let n={};n.flags=h;n.canvas=
a.La;a.G("beforewebglcontext",n);a.N=a.La.getContext("webgl2",h);a.N?a.Mn=!0:(a.Mn=!1,a.N=a.La.getContext("webgl",h));a.N||(a.N=a.La.getContext("experimental-webgl",h));if(a.N){let r=a.N;a.Xa.width=b*f;a.Xa.height=e*f;r.clearColor(0,0,0,0);r.enable(a.N.DEPTH_TEST);r.viewport(0,0,500,500);r.clear(r.COLOR_BUFFER_BIT|r.DEPTH_BUFFER_BIT);4096<=r.getParameter(r.MAX_TEXTURE_SIZE)&&!a.Zh&&(a.D.ji=1<f?4*a.D.ji:2*a.D.ji);a.da.forEach(w=>{w.Vh();w.Pj();w.Tp(a.dh);w.Vp()});a.F&&(a.F.Vh(),a.F.If());a.Nc&&(a.Nc.Vh(),
a.Nc.If())}}catch(b){}a.N?(a.pa=!0,a.G("webglready",{gl:this.N})):alert("Could not initialise WebGL!")}Jc(a,b){const e=this.N;e.shaderSource(a,b);e.compileShader(a);e.getShaderParameter(a,e.COMPILE_STATUS)||(console&&console.log(e.getShaderInfoLog(a)),alert(e.getShaderInfoLog(a)))}Nf(a,b,e){const f=this.N;f.attachShader(a,b);f.attachShader(a,e);f.linkProgram(a);f.getProgramParameter(a,f.LINK_STATUS)||(alert("Could not initialise shader program"),console&&console.log(f.getError()));f.useProgram(a)}be(){return this.D.be()}vp(){return this.I.pan}Dj(){return this.D.Dj()}ce(){return this.D.ce()}Ia(){return this.D.Ia()}Nd(a){this.D.Nd(a)}Fi(a){this.D.Fi(a)}wh(a,
b){this.D.wh(a,b)}sl(a,b){this.D.sl(a,b)}Bf(){return this.D.Bf()}Fp(){return this.I.u}Od(a){this.D.Od(a)}xh(a,b){this.D.xh(a,b)}ul(a,b){this.D.ul(a,b)}Tg(a){this.D.Tg(a)}Gj(){return this.D.Gj()}rd(){return this.D.rd()}Eb(){return this.D.Eb()}Wl(a,b){return this.D.Wl(a,b)}Vl(a,b){return this.D.Vl(a,b)}Ys(){return this.I.Ui}Jb(a){return this.D.Jb(a)}en(a){this.D.en(a)}Ji(a){this.D.Ji(a)}gn(a){this.D.gn(a)}dn(a){this.D.dn(a)}yk(a,b){const e=this.D;if(120<e.rd())e.Jb(a);else{var f=this.Hd(b.x,b.y,!0);
if(this.Jb(a)){b=this.Hd(b.x,b.y,!0);a=f.pan-b.pan;b=f.tilt-b.tilt;if(15<Math.abs(a)||10<Math.abs(b))b=a=0;a=this.Ia()?-a:a*Math.cos(f.tilt*Math.PI/180);e.pan.Ub-=a;e.u.Ub+=b;e.Dk(e.pan.m-a,e.u.m+b)}}}vh(a,b){return this.D.vh(a,b)}mo(a,b){this.yk(this.rd()+a,b)}mj(a,b){const e=this.D;if(!isNaN(a)){let f;f=a/90*Math.cos(Math.min(e.fov.m,90)*Math.PI/360);f=e.fov.m*Math.exp(f);this.Jb(f);b&&(e.fov.d=a)}}no(a,b,e){const f=this.D;if(!isNaN(a)){let h;h=a/90*Math.cos(Math.min(f.fov.m,90)*Math.PI/360);h=
f.fov.m*Math.exp(h);this.yk(h,b);e&&(f.fov.d=a,f.fov.Yc.enabled=!0,f.fov.Yc.x=b.x,f.fov.Yc.y=b.y)}}Dk(a,b){this.D.Dk(a,b)}kn(a,b,e){this.D.kn(a,b,e)}Sg(a,b,e){let f=a.toString().split("/");1<f.length&&(a=Number(f[0]),b=Number(f[1]),2<f.length&&(e=Number(f[2])));this.D.Sg(a,b,e)}xk(){this.D.xk()}Kt(){let a={};const b=this.D;a.pan={};a.pan.min=b.pan.min;a.pan.max=b.pan.max;a.tilt={};a.tilt.min=b.u.min;a.tilt.max=b.u.max;return a}Dv(a){const b=this.D;a.hasOwnProperty("pan")&&(a.pan.hasOwnProperty("min")&&
(b.pan.min=a.pan.min),a.pan.hasOwnProperty("max")&&(b.pan.max=a.pan.max));a.hasOwnProperty("tilt")&&(a.tilt.hasOwnProperty("min")&&(b.u.min=a.tilt.min),a.tilt.hasOwnProperty("max")&&(b.u.max=a.tilt.max));this.Xd();this.update()}bt(){return this.qa.enabled}fv(a){let b=this.qa;a&&(b.ma.x=0,b.ma.y=0);b.enabled=!!a}Ip(){return this.K.re}kr(a){this.K.re=a}Zs(){return this.km()?!0:this.gm}gr(a,b=!0){this.Yr=b;this.mh==!a&&(a&&this.km()?this.Oq():((this.mh=!!a)?this.ob.Ci=!0:this.D.Y.m=0,this.G("gyrochanged",
{}),this.G("playerstatechanged",{})))}km(){return window.hasOwnProperty("DeviceOrientationEvent")&&DeviceOrientationEvent.hasOwnProperty("requestPermission")&&"function"===typeof DeviceOrientationEvent.requestPermission?!0:!1}Oq(){let a=this;if(this.km())try{DeviceOrientationEvent.requestPermission().then(b=>{"granted"===b&&(a.ob.Ci=!0,a.mh=!0,a.G("gyrochanged",{}),a.G("playerstatechanged",{}))}).catch(console.error)}catch(b){console.log(b)}}Dt(){return this.mh}moveTo(a,b,e,f,h,n){this.ra(!1);if("_blank"!==
a&&""!==a){this.I.active=!0;this.I.aborted=!1;this.I.Ln=!1;var r=this.D,w=a.toString().split("/");1<w.length&&(a=Number(w[0]),f=Number(b),b=Number(w[1]),2<w.length&&(e=Number(w[2])));this.I.pan=isNaN(a)?r.pan.m:Number(a);this.I.u=isNaN(b)?r.u.m:Number(b);this.I.fov=!isNaN(e)&&0<e&&180>e?Number(e):r.fov.m;this.I.speed=!isNaN(f)&&0<f?Number(f):1;this.I.Y=isNaN(h)?r.Y.m:Number(h);void 0!==n?!a||4!=n&&12!=n&&9!=n||(this.I.rb=n):this.I.rb=this.Z()}}mi(a){this.ra(!1);let b=0,e=0,f=70,h=4,n=0,r=1;a.hasOwnProperty("pan")&&
(b=Number(a.pan),this.I.pan=b);a.hasOwnProperty("tilt")&&(e=Number(a.tilt),this.I.u=e);a.hasOwnProperty("fov")&&(f=Number(a.fov),this.I.fov=f);a.hasOwnProperty("projection")&&(h=Number(a.projection),this.I.rb=h);a.hasOwnProperty("timingFunction")&&(n=Number(a.timingFunction));a.hasOwnProperty("speed")&&(r=Number(a.speed));a=this.D;if(0>=r)this.Sg(b,e,f),this.$a(h);else{let w=new Ub;w.eb="__AutoMove";w.af=this.Dj();w.Ri=a.u.m;w.me=a.fov.m;w.Qi=this.Z();w.Pd=b;w.oe=e;w.ah=f;w.ne=h;w.og=!1;w.vf=!1;w.wf=
!1;0==n&&(w.vf=!0);1==n&&(w.og=!0,w.vf=!0);2==n&&(w.wf=!0);w.speed=r;this.I.$n=this.S;this.S=this.Xo(w);this.I.ao=(new Date).getTime();this.I.Ln=!0;this.I.active=!0;this.I.aborted=!1;this.I.pan=b;this.I.u=e;this.I.fov=f;this.te=!1}}nq(a){const b=this.D;this.moveTo(b.pan.Ua,b.u.Ua,b.fov.Ua,a)}oq(a,b){let e={};const f=this.D;e.pan=f.pan.Ua;e.tilt=f.u.Ua;e.fov=f.fov.Ua;e.projection=this.Qh();e.timingFunction=b;e.speed=a;this.mi(e)}Wn(a,b,e,f){let h=new Rb(this);h.type="point";h.pan=b;h.u=e;h.id=a;h.j=
{};h.j.player=this;h.xf();h.j.hotspot=h;h.j.__div=document.createElement("div");h.j.__div.appendChild(f);this.L.push(h);h.j.__div.style.position="absolute";h.j.__div.style.left="-1000px";h.j.__div.style.top="-1000px";h.j.__div.ggVisible=!0;this.xa.insertBefore(h.j.__div,this.xa.firstChild);this.Ma=!0}Rr(a,b,e){for(let f=0;f<this.L.length;f++){let h=this.L[f];h.id==a&&(h.pan=b,h.u=e,h.xf())}this.Ma=!0}Lq(a){let b=-1,e;for(let f=0;f<this.L.length;f++)e=this.L[f],e.id==a&&(b=f);-1<b&&(e=this.L.splice(b,
1).pop(),e.j&&e.j.__div&&this.xa.removeChild(e.j.__div))}ip(a){for(var b=0;b<this.L.length;b++){let e=this.L[b];if(e.id==a)return b={},b.id=a,b.pan=e.pan,b.tilt=e.u,b.pxp=e.Om,b.pyp=e.Pm,b.url=e.url,b.target=e.target,b.distance=e.distance,b.title=e.title,b.description=e.description,b.skinid=e.nn,e.od&&(b.customimage=e.od,b.customimagewidth=e.uf,b.customimageheight=e.Dd,b.use3D=e.ag,b.distance3D=e.uj),e.j&&e.j.__div&&(b.div=e.j.__div),b}}cs(a,b){this.oa.start.x=a;this.oa.start.y=b;this.oa.na.x=a;this.oa.na.y=
b;this.qa.na.x=a;this.qa.na.y=b;this.Tm++;a=this.D;a.pan.Ub=a.pan.m;a.u.Ub=a.u.m}Zr(a,b){let e;e=this.Ia()?this.rd():this.Eb();const f=this.D;f.pan.Ub+=a*e/this.H.height;f.u.Ub+=b*e/this.H.height;f.pan.m=f.pan.Ub;f.u.m=f.u.Ub}$r(a,b){this.oa.m.x=a;this.oa.m.y=b;this.oa.ma.x=this.oa.m.x-this.oa.na.x;this.oa.ma.y=this.oa.m.y-this.oa.na.y;this.K.mc&&(this.oa.na.x=this.oa.m.x,this.oa.na.y=this.oa.m.y,this.update())}ra(a){const b=this.D;this.A.active&&(this.A.active=!1,this.G("autorotatechanged",{}),b.pan.d=
0,b.u.d=0,b.fov.d=0,b.fov.Yc.enabled=!1);this.I.active&&(this.I.active=!1,b.pan.d=0,b.u.d=0,b.fov.d=0,b.fov.Yc.enabled=!1);this.hf=this.I.aborted=!1;this.A.gk=!1;this.Sd=.02;this.th=0;this.A.gd&&(this.A.enabled=this.A.gg,a&&(this.A.gd=this.A.cg));this.Jf=(new Date).getTime()}Ej(a,b){return this.D.Ej(a,b)}Hd(a,b){return this.D.Hd(a,b)}nc(a){return a==this.control||a&&void 0!==a.ggPermeableMap&&1==a.ggPermeableMap?!0:a&&void 0!==a.ggPermeable&&0==a.ggPermeable?!1:a&&a.ggType&&("container"==a.ggType||
"cloner"==a.ggType||"timer"==a.ggType)?!0:!1}vl(a,b){const e=this.D;let f=this.ae(),h;for(h=0;h<this.Jj();h++){var n=this.dm(h);if(n.Na)return n}for(h=0;h<this.Jj();h++){let w=this.dm(h);if(w.vd||w.Gm)continue;let x=[],y=new lb,z,cb,ab;0<w.fov&&(cb=Math.tan(w.fov/2*Math.PI/180),ab=0<w.lc?cb*w.$c/w.lc:cb,w.Kc&&1!=w.Kc&&(ab*=w.Kc));for(z=0;4>z;z++){switch(z){case 0:y.init(-cb,-ab,0);break;case 1:y.init(cb,-ab,0);break;case 2:y.init(cb,ab,0);break;case 3:y.init(-cb,ab,0)}y.ya(w.ya*Math.PI/180);y.Ca(-w.Ca*
Math.PI/180);y.Sa(w.Sa*Math.PI/180);--y.z;y.ya(-w.u*Math.PI/180);y.Ca(w.pan*Math.PI/180);y.Ca(-e.pan.m*Math.PI/180);y.ya(e.u.m*Math.PI/180);y.Sa(e.Y.m*Math.PI/180);x.push(y.clone())}x=this.wl(x);if(0<x.length){for(z=0;z<x.length;z++){y=x[z];if(.1>y.z){var r=-f/y.z;n=this.H.width/2+y.x*r;r=this.H.height/2+y.y*r}else r=n=0;y.Ac=n;y.ac=r}if(Eb.Qp(x,a,b))return w}}return null}Jj(){return this.V.length+this.Ya.length+this.Qb.length}dm(a){return a<this.V.length?this.V[a]:a<this.V.length+this.Ya.length?
this.Ya[a-this.V.length]:this.Qb[a-(this.V.length+this.Ya.length)]}nu(a){this.Ur(a);window.focus();if(this.Ed)this.Ed.onclick();(this.Zd||this.De&&this.Dg)&&7>this.Wc&&this.xg();if(0==a.button&&(this.Sb=null,!this.K.qc&&!this.xc)){a=a?a:window.event;if(a.which||0==a.which||1==a.which){let b=(new Date).getTime();if(this.Ue){this.Sb=this.Ue;this.U.bd=!0;this.U.startTime=b;a.stopPropagation();return}if(this.nc(a.target)){let e;if((e=this.vl(this.ca.x,this.ca.y))&&e.Ie&&(this.Sb=e,void 0!==e.code&&""!==
e.code&&e.Xb))return;this.cs(a.pageX,a.pageY);this.U.bd=!0;this.U.startTime=b;a.preventDefault();this.ra(!0);a=this.Hd(this.ca.x,this.ca.y,!0);this.G("playerdown",{pan:-Math.round(100*a.pan)/100,tilt:Math.round(100*a.tilt)/100})}}this.oa.ma.x=0;this.oa.ma.y=0}}Ye(a){if("string"==typeof a)for(let b=0;b<this.L.length;b++)if(this.L[b].id==a){a=this.L[b];break}this.O=a&&null!==a&&"object"==typeof a?a:this.Va;this.O==this.Va&&(a=this.nj(this.ca.x,this.ca.y))&&(a.kb=0);this.O.xf&&this.O.xf();this.hotspot=
this.O;this.G("activehotspotchanged",{hotspot:this.O})}mu(a){this.Ur(a);if(!this.K.qc&&!this.xc&&!this.Ue){this.A.active&&(this.A.bk=(new Date).getTime());0==a.buttons&&(this.U.bd=!1);this.U.bd&&(a.preventDefault(),(a.which||0==a.which||1==a.which)&&this.$r(a.pageX,a.pageY),this.ra(!0));let e=!1;if(this.O==this.Va||"poly"==this.O.type){var b=this.Va;0<this.L.length&&this.nc(a.target)&&(b=this.nj(this.ca.x,this.ca.y));this.wk(b);this.gh(this.ca.x,this.ca.y,!1);0!=b&&b!=this.Va&&(e=!0)}b=null;!e&&this.nc(a.target)&&
(b=this.vl(this.ca.x,this.ca.y));this.A.sj&&(this.A.sj=!1);this.xa.style.cursor=this.O!=this.Va&&this.O.Ke&&e||b&&b.zg?"pointer":"default"}}wk(a){!1===a&&(a=this.Va);this.O!=a&&(this.O!=this.Va&&(0<this.P.mode&&(this.O.kb=0),this.ka&&this.ka.hotspotProxyOut&&this.ka.hotspotProxyOut(this.O.id,this.O.url),this.G("hsproxyout",{id:this.O.id,url:this.O.url})),a!=this.Va?(this.Ye(a),this.ka&&this.ka.hotspotProxyOver&&this.ka.hotspotProxyOver(this.O.id,this.O.url),this.G("hsproxyover",{id:this.O.id,url:this.O.url}),
0<this.P.mode&&(this.P.kb=1,this.O.kb=1)):(this.Ye(this.Va),0<this.P.mode&&(this.P.kb=0)))}lu(a){this.$j=-1;if(this.Zd||this.De&&this.Dg)7<=this.Wc?this.yr():this.xg();if(0==a.button&&!this.K.qc&&!this.xc){let b=this.Hd(this.ca.x,this.ca.y,!0);this.Sb&&(this.Sb.Ie(),this.Ue=this.Sb.Na?this.Sb:null);if(this.U.bd){this.ra(!0);a.preventDefault();this.U.bd=!1;a=(new Date).getTime();let e;e=Math.abs(this.oa.start.x-this.oa.na.x)+Math.abs(this.oa.start.y-this.oa.na.y);if(400>a-this.U.startTime&&0<=e&&20>
e){let f=this.nj(this.ca.x,this.ca.y);f&&this.Lr(f);e=Math.abs(this.oa.he.x-this.oa.na.x)+Math.abs(this.oa.he.y-this.oa.na.y);700>a-this.ai&&0<=e&&20>e?(f?this.Mr(f):this.K.Hl&&this.Pk(),this.G("playerdblclick",{pan:-Math.round(100*b.pan)/100,tilt:Math.round(100*b.tilt)/100}),this.ai=0):(this.G("playerclick",{pan:-Math.round(100*b.pan)/100,tilt:Math.round(100*b.tilt)/100}),this.ai=a);this.oa.he.x=this.oa.na.x;this.oa.he.y=this.oa.na.y}this.G("playerup",{pan:-Math.round(100*b.pan)/100,tilt:Math.round(100*
b.tilt)/100})}}}mq(a){if(!this.K.Qe&&!this.xc&&(a=a?a:window.event,this.nc(a.target))){var b=a.detail?-1*a.detail:a.wheelDelta/40;this.K.Wp&&(b=-b);a.axis&&(-1==this.$j?this.$j=a.axis:this.$j!=a.axis&&(b=0));let e=0<b?1:-1;a.wheelDeltaX&&a.wheelDeltaY&&Math.abs(a.wheelDeltaX)>Math.abs(a.wheelDeltaY)&&(b=0);0!=b&&(this.K.re?(b=this.Cf(),this.no(e*this.K.qn,{x:a.clientX-b.x,y:a.clientY-b.y},!0)):this.mj(e*this.K.qn,!0),this.update());a.preventDefault();this.ra(!0)}}Yv(a){const b=this.D;var e=a.touches;
this.Vr(a);this.cf=this.Sb=null;window.focus();this.lm||(this.lm=!0,this.G("hastouch",{}),this.G("playerstatechanged",{}));!this.qm&&(this.Zd||this.De&&this.Dg)&&7>this.Wc&&this.xg();if(!this.K.qc&&!this.xc){var f=(new Date).getTime();if(this.Ue)this.Sb=this.Ue,this.U.pd=!0,this.U.startTime=f,a.preventDefault(),this.U.ui=!0,e[0]&&(this.zb=e[0].target);else{if(!this.U.pd&&e[0]){this.U.startTime=f;this.U.start.x=e[0].pageX;this.U.start.y=e[0].pageY;this.U.na.x=e[0].pageX;this.U.na.y=e[0].pageY;f=this.zb=
e[0].target;if(this.nc(f)){var h;(h=this.vl(this.ca.x,this.ca.y))&&h.Ie&&(this.Sb=h);if(h=this.nj(this.ca.x,this.ca.y))this.cf=h,this.wk(h),this.Af(a),this.gh(this.ca.x,this.ca.y,!0);this.cs(e[0].pageX,e[0].pageY);this.U.Qo=e[0].identifier;this.U.pd=!0;a.preventDefault();this.U.ui=!0;this.ra(!0);h=this.Hd(this.ca.x,this.ca.y,!0);this.G("playerdown",{pan:-Math.round(100*h.pan)/100,tilt:Math.round(100*h.tilt)/100})}if(f){h=!1;if(null!=this.U.Zg)for(var n=this.U.Zg;n&&n!=this.control;){if(n.onmouseout)n.onmouseout(a);
if(n.onmouseleave)n.onmouseleave(a);n=n.parentNode}n=f;let r=!0;for(;n&&n!=this.control;)f!=this.U.Zg&&(n.onmouseover&&(n.onmouseover(a),7<=this.Wc&&this.nc(a)&&(h=!0)),n.onmouseenter&&(n.onmouseenter(a),7<=this.Wc&&this.nc(a)&&(h=!0))),n.onmousedown&&(n.onmousedown(a),r=!1,h=!0),n.onclick&&(r=!1),n=n.parentNode;this.U.Zg=r&&7<=this.Wc&&f!=this.U.Zg?f:null;h&&(a.preventDefault(),this.U.ui=!0)}}1<e.length&&(this.U.pd=!1);!this.im&&2==e.length&&e[0]&&e[1]&&this.nc(this.zb)&&(a=e[0].pageX-e[1].pageX,
e=e[0].pageY-e[1].pageY,b.fov.Br=Math.sqrt(a*a+e*e),b.fov.prev=b.fov.m);this.oa.ma.x=0;this.oa.ma.y=0}}}xg(){try{this.dj&&this.B.j&&(!this.B.Mc&&this.B.Ik&&this.B.j.play(),this.B.j.muted=!1);if(this.ga&&(this.ga.resume(),"suspended"==this.ga.state))return;if(this.Lc&&this.ga&&this.ga.createOscillator){let e=this.ga.createOscillator();var a=this.ga.createGain();e.frequency.value=30;e.type="sine";e.connect(a);a.connect(this.ga.destination);a.gain.value=.01;e.start(0);setTimeout(function(){e.stop()},
1E4)}for(a=0;a<this.X.length;a++){var b=this.X[a];(!this.isPlaying(b.id)||b.ta)&&0<=b.loop&&b.autoplay&&!this.yc&&(b.ta&&b.ig(),this.Md(b.id,b.loop))}for(b=0;b<this.V.length;b++){let e=this.V[b];!this.isPlaying(e.id)&&e.autoplay&&this.dj&&!this.yc&&this.Md(e.id,e.loop);this.isPlaying(e.id)&&e.autoplay&&this.dj&&!this.yc&&(e.zd&&e.se(),e.j.muted=!1)}this.De=this.Zd=!1}catch(e){}}Xv(a){const b=this.D;this.Vr(a);let e=a.touches;if(this.K.qc||this.xc)(this.F.Qd||this.F.xd||this.F.ed)&&a.preventDefault();
else{e[0]&&(this.U.na.x=e[0].pageX,this.U.na.y=e[0].pageY);if(this.zb){for(var f=this.zb,h=!1;f&&f!=this.control&&!h;)"scrollarea"==f.ggType&&(h=!0),"map"==f.ggType&&(h=!0),"text"==f.ggType&&(h=!0),f=f.parentNode;h||a.preventDefault()}if(this.U.pd){a.preventDefault();for(f=0;f<e.length;f++)if(e[f].identifier==this.U.Qo){this.$r(e[f].pageX,e[f].pageY);break}this.cf&&(this.Af(a),this.gh(this.ca.x,this.ca.y,!0));this.ra(!0)}2==e.length&&e[0]&&e[1]&&(this.U.pd=!1,!this.im&&this.nc(this.zb)&&(this.K.Qe||
(f=e[0].pageX-e[1].pageX,h=e[0].pageY-e[1].pageY,b.fov.Co=Math.sqrt(f*f+h*h),this.qa.fov.active=!0,this.qa.fov.center.enabled=!0,this.qa.fov.center.x=(e[0].pageX+e[1].pageX)/2,this.qa.fov.center.y=(e[0].pageY+e[1].pageY)/2,this.qa.fov.kc=b.fov.prev*Math.sqrt(b.fov.Br/b.fov.Co),4==this.Z()&&!this.Ia()&&this.qa.fov.kc>b.fov.max&&(this.qa.fov.kc=b.fov.max),this.qa.fov.kc<b.fov.min&&(this.qa.fov.kc=b.fov.min)),this.ra(!0),a.preventDefault()))}}Wv(a){const b=this;b.U.ui&&(b.U.ui=!1,a.preventDefault());
let e=!1;if(b.Zd||b.De&&b.Dg)7<=b.Wc?b.yr():b.xg();if(!b.K.qc&&!b.xc){let h=this.Hd(b.U.na.x,b.U.na.y,!0);b.U.pd&&(a.preventDefault(),b.ra(!0),this.G("playerup",{pan:-Math.round(100*h.pan)/100,tilt:Math.round(100*h.tilt)/100}));let n=(new Date).getTime(),r=!1;this.nc(this.zb)&&(a.preventDefault(),this.Sb&&(this.Sb.Ie(),this.Ue=this.Sb.Na?this.Sb:null));var f=Math.abs(this.U.start.x-this.U.na.x)+Math.abs(this.U.start.y-this.U.na.y);if(0<=f&&20>f){e=!0;if(b.zb)for(f=b.zb;f&&f!=b.control;){if(f.onclick){let w=
f,x=b.hotspot;setTimeout(()=>{let y=b.hotspot;b.hotspot=x;w.onclick(a);b.hotspot=y},0);"hotspot"!=f.Kw&&(r=!0);e=!1}f=f.parentNode}f=Math.abs(b.U.he.x-b.U.na.x)+Math.abs(b.U.he.y-b.U.na.y);if(700>n-b.ai&&0<=f&&20>f){if(b.nc(b.zb)){a.preventDefault();if(b.cf)b.Mr(b.cf);else if(b.K.Hl){let w=this;setTimeout(function(){w.Pk()},1)}this.G("playerdblclick",{pan:-Math.round(100*h.pan)/100,tilt:Math.round(100*h.tilt)/100})}if(b.zb)for(f=b.zb;f&&f!=b.control;)f.ondblclick&&(f.ondblclick(),r=!0,e=!1),f=f.parentNode;
b.ai=0}else b.ai=n;b.U.he.x=b.U.na.x;b.U.he.y=b.U.na.y}if(b.zb)for(f=b.zb;f&&f!=b.control;)b.U.Zg!=b.zb&&(f.onmouseout&&(f.onmouseout(a),7<=b.Wc&&(r=!0)),f.onmouseleave&&(f.onmouseleave(a),r=!0)),f.onmouseup&&(f.onmouseup(a),7<=b.Wc&&(r=!0)),f=f.parentNode;r&&a.preventDefault();b.Af(a);b.cf&&(b.gh(-1,-1,!0),e&&b.Lr(b.cf));b.zb=null;b.U.pd=!1;b.wk(b.Va);b.cf=null}}Vv(a){this.K.qc||this.xc||(this.U.pd=!1);this.cf=null;this.wk(this.Va);this.Af(a);this.gh(-1,-1,!0)}rm(){return null!=this.zb||this.U.pd||
this.U.bd}pq(a){!this.Pf&&window.MSGesture&&(this.Pf=new window.MSGesture,this.Pf.target=this.control);this.Pf&&this.Pf.addPointer(a.pointerId)}Zo(a){const b=this.D;this.im=!0;this.hk=1;this.K.qc||this.K.Qe||this.xc||(a.touches?(this.zb=a.touches.target,this.nc(a.target)&&(a.preventDefault(),b.fov.prev=b.fov.m,this.ra(!0))):(a.preventDefault(),b.fov.prev=b.fov.m,this.ra(!0)))}Rs(a){const b=this.D;if(!this.K.qc&&!this.K.Qe&&!this.xc&&this.nc(a.target)){a.preventDefault();this.qa.fov.active=!0;this.qa.fov.center.enabled=
!0;let e=this.Af(a);this.qa.fov.center.x=e.x;this.qa.fov.center.y=e.y;this.qa.fov.kc=b.fov.prev/Math.sqrt(a.scale);4==this.Z()&&2!=this.cd&&this.qa.fov.kc>b.fov.max&&(this.qa.fov.kc=b.fov.max);this.update();this.ra(!0)}}ou(a){const b=this.D;this.K.qc||this.K.Qe||this.xc||(a.preventDefault(),1!=a.scale&&(this.qa.fov.active=!0,this.hk*=a.scale,this.qa.fov.center.enabled=!0,a=this.Af(a),this.qa.fov.center.x=a.x,this.qa.fov.center.y=a.y,this.qa.fov.kc=b.fov.prev/Math.sqrt(this.hk),4==this.Z()&&2!=this.cd&&
this.qa.fov.kc>b.fov.max&&(this.qa.fov.kc=b.fov.max),this.update(),this.ra(!0)))}Yo(a){this.K.qc||this.K.Qe||this.xc||(this.qa.fov.active=!1,a.preventDefault(),this.ra(!0),this.Pf&&this.Pf.reset&&this.Pf.reset())}du(a){this.K.fi||this.xc||(this.oc&&a.preventDefault(),this.Yj=a.keyCode,this.ra(!0))}eu(a){this.Yj&&(this.Yj=0,a.preventDefault())}wu(){this.Yj=0}Cu(a,b,e,f){f?(this.Uh.alpha=a,this.Uh.beta=b,this.Uh.gamma=e,this.Uh.gamma+=90):(this.ob.alpha=a,this.ob.beta=b,this.ob.gamma=e,this.ob.gamma+=
90);this.ob.orientation=window.orientation?1*parseInt(""+window.orientation,10):0;e=new Cb;a=this.ob;e.Sc(-a.alpha);e.ke(-a.beta);e.bc(-a.gamma);e.ke(90-a.orientation);1>e.Nb?-1<e.Nb?(b=180/Math.PI*Math.asin(-e.Nb),a=180/Math.PI*Math.atan2(e.vc,e.uc),e=180/Math.PI*Math.atan2(e.tc,e.sc)):(b=0,a=90,e=-180/Math.PI*Math.atan2(-e.Zb,e.$b)):(b=0,a=-90,e=180/Math.PI*Math.atan2(-e.Zb,e.$b));if(this.mh)if(this.rm()||this.I.Nj||this.ob.Ci)this.ob.Rf=this.be()+e,this.ob.fadeIn=0,this.ob.Ci=!1;else{f=this.I.active;
let h=1;10>this.ob.fadeIn&&(this.ob.fadeIn+=1,h=.1*this.ob.fadeIn);e=-e+this.ob.Rf;this.Nd(h*e+(1-h)*this.be());this.Od(h*a+(1-h)*this.Bf());this.Yr?this.Tg(h*b+(1-h)*this.Gj()):this.Tg(0);this.Xd();this.I.active=f}}Kh(){return Math.min(1,2*Math.tan(Math.PI*Math.min(this.D.fov.m,90)/360))}Cq(){let a=this;setTimeout(function(){a.Cq()},100);9!=a.sk||a.Qj||window.requestAnimationFrame(function(){a.pi()});10<a.sk&&1<a.Tm&&(a.Yb("recover timer - disabling requestAnimationFrame"),a.Qj=!0,a.pi());a.sk++}Ki(a){var b=
this.D;if("__VideoPano"!=this.S.eb){var e={PAN:{value:0,name:"pan"},tw:{value:1,name:"tilt"},sw:{value:2,name:"fov"}},f=0,h=0,n=0;a=Math.max(a,0);for(var r in e){var w=e[r],x=void 0,y=this.Wb(this.yp(a,w.value,""),w.value,"");x=(x=this.qp(y))?this.$o(y,x,a):y.value;switch(w.value){case 0:w=b.pan.m;if(this.te&&3!=y.type){if(!this.Ia()){for(;360<x;)x-=360;for(;-360>x;)x+=360}f=x-w;this.Ia()||(180<f&&(f-=360),-180>f&&(f+=360));b.pan.m+=f*this.Sd}else b.pan.m=x;this.A.ci=b.pan.m;break;case 1:w=b.u.m;
this.te&&3!=y.type?(h=x-w,b.u.m+=h*this.Sd):b.u.m=x;this.A.di=b.u.m;break;case 2:w=b.fov.m,this.te&&3!=y.type?(n=x-w,b.fov.m+=n*this.Sd):b.fov.m=x,this.A.bi=b.fov.m}}e=this.Z();for(r=Math.floor(a);!this.Wb(r,3)&&0<r;)r--;r=this.Wb(r,3);y=a-r.time;this.te&&-1!=this.uh&&this.jj+this.jl>a?(e=this.Nh(this.uh),b.fov.m>e?this.jj=a:(b=(a-this.jj)/this.jl,b=Math.min(1,b),this.$a(this.Z(),this.uh,1-b))):(0==r.Kb||y>r.Kb-.3?this.$a(r.value):this.$a(e,r.value,1-y/r.Kb),this.A.eq=r.value);this.te&&(f=Math.sqrt(f*
f+h*h+n*n),.3>f&&(this.te=!1,this.Sd=.02,this.th=0),0<this.th&&f>this.th&&(this.Sd+=.01,this.Sd=Math.min(this.Sd,1)),this.th=f)}f=Math.floor(a);h=this.Ss(f);for(n=0;n<h.length;n++)if(e=h[n],3>e.Zn)f!=this.bo&&(b=e.Ad,this.yb.hasOwnProperty(b)&&(r=this.yb[b].type,0==r?this.Ze(b,e.Nn):1==r?this.Ze(b,e.value):2==r&&this.Ze(b,"true"==e.Nn)));else if(b=this.Wb(this.yp(a,e.sb,e.Ad),e.sb,e.Ad),b=(r=this.qp(b))?this.$o(b,r,a):b.value,r=e.Ad.split("|"),2==r.length)for(e=r[0],r=r[1],y=0;y<this.L.length;y++)x=
this.L[y],x.id==e&&("pan"==r?x.pan=b:"tilt"==r&&(x.u=b));this.bo=f;this.update()}ap(){if(""!=this.A.Hg){var a=this.tp(this.A.Hg);if(0==a.length||1==a.length&&a[0]==this.ua)return this.ua}a=this.ua;do if(this.A.uq){let b=1E3;do a=this.bb[Math.floor(Math.random()*this.bb.length)];while(b--&&a==this.ua)}else a=this.bb.indexOf(a),a++,a>=this.bb.length&&(a=0),a=this.bb[a];while(""!=this.A.Hg&&!this.Ph(a).tags.includes(this.A.Hg)&&a!=this.ua);return a}Lu(a){var b=this.D;var e=this.I.speed;this.I.lastTime&&
(e=e*(a.getTime()-this.I.lastTime)/60);this.I.lastTime=a.getTime();var f=!0;0<this.da.length&&(this.da[0].ready()||(f=!1));this.A.Jh&&(f||4==this.Z())&&this.Oh()&&(this.A.Jh=!1,this.A.active=!0,this.Da.running=!0,this.Da.finished=!1);if(this.I.active||0!=this.I.rb&&f){if(this.I.Ln&&"__AutoMove"==this.S.eb)if(e=(a.getTime()-this.I.ao)/100,e>=this.S.length){if(this.Ki(this.S.length),this.za.splice(this.za.indexOf(this.S),1),this.I.active=!1,this.S=this.I.$n,this.I.rb=0,this.Sg(this.I.pan,this.I.u,this.I.fov),
b.pan.Ub=this.I.pan,b.u.Ub=this.I.u,this.I.Gg&&(this.I.Gg=!1,this.A.gk=!0,this.A.gd=!0,this.A.active=!0,this.G("autorotatechanged",{})),this.onMoveComplete)this.onMoveComplete()}else this.Ki(e);else{b.pan.d=this.I.pan-b.pan.m;if(360==b.pan.max-b.pan.min){for(;-180>b.pan.d;)b.pan.d+=360;for(;180<b.pan.d;)b.pan.d-=360}b.u.d=this.I.u-b.u.m;b.Y.d=this.I.Y-b.Y.m;b.fov.d=this.I.fov-b.fov.m;f=e*this.Kh();var h=Math.sqrt(b.pan.d*b.pan.d+b.u.d*b.u.d+b.Y.d*b.Y.d+b.fov.d*b.fov.d),n=b.pan.m-this.I.ci;let r=b.u.m-
this.I.di,w=b.Y.m-this.I.fq,x=b.fov.m-this.I.bi;100*Math.sqrt(n*n+r*r+w*w+x*x)<f&&0==this.I.rb&&(this.I.aborted=!0);this.I.ci=b.pan.m;this.I.di=b.u.m;this.I.fq=b.Y.m;this.I.bi=b.fov.m;if(100*h<f||this.I.aborted){if(b.pan.d=0,b.u.d=0,b.Y.d=0,b.fov.d=0,b.fov.Yc.enabled=!1,this.I.active&&(this.I.active=!1,b.pan.m=this.I.pan,b.u.m=this.I.u,b.Y.m=this.I.Y,b.fov.m=this.I.fov,this.onMoveComplete))this.onMoveComplete()}else h=h>5*f?f/h:.2,b.pan.d*=h,b.u.d*=h,b.fov.d*=h;b.pan.m+=b.pan.d;b.u.m+=b.u.d;b.Y.m+=
b.Y.d;this.K.re&&b.fov.Yc.enabled?this.yk(b.fov.m+b.fov.d,b.fov.Yc):b.fov.m+=b.fov.d;0!=this.I.rb&&(this.I.rb!=this.Z()?(e=this.Nh(this.I.rb),this.rd()>e?(b.fov.m+=-Math.max((2.5-1.7*Math.min(Math.sqrt(b.pan.d*b.pan.d+b.u.d*b.u.d+b.Y.d*b.Y.d)/f,1))*f,b.fov.d)-b.fov.d,this.I.fov=b.fov.m):(this.$a(this.I.rb,this.Z(),0),this.I.xi=0,this.da.forEach(y=>{y.Pj()}))):1>this.I.xi?(this.I.xi=Math.min(1,this.I.xi+.05*e),this.$a(this.Z(),this.Ic(),this.I.xi)):(this.$a(this.Z(),0,0),this.I.rb=0,this.da.forEach(y=>
{y.Pj()})))}this.Jf=a.getTime();this.update()}else if(this.A.active&&!this.F.xd){e=a.getTime()-this.A.startTime;this.A.bk<this.A.startTime&&(this.A.bk=this.A.startTime);if((this.A.gd||this.Da.running)&&0<this.za.length){e/=100;f=!1;if(this.wc!=this.S.eb||""!=this.S.jf&&this.A.Td!=this.S.jf){for(b=0;b<this.za.length;b++)if(""==this.wc&&this.za[b].jf==this.A.Td||""!=this.wc&&this.za[b].eb==this.wc&&this.za[b].jf==this.A.Td){f=!0;this.S=this.za[b];this.wc=this.S.eb;break}!f&&0<this.za.length&&this.za[0].jf==
this.A.Td&&(f=!0,this.S=this.za[0],this.wc=this.S.eb)}else f=!0;if(f)if(b=(f=this.B.j&&this.B.Mc)&&this.A.yn&&!this.Da.running,this.hf){h=e;if(b){if(0==this.gf&&0!=this.B.j.duration)for(;this.gf<this.S.length/10;)this.gf+=this.B.j.duration;this.B.j.currentTime<this.eo&&this.ll&&(this.kl++,this.ll=!1);h=10*(this.kl*this.B.j.duration+this.B.j.currentTime);this.eo=this.B.j.currentTime;.05>this.B.j.duration-this.B.j.currentTime&&(this.ll=!0);for(;h>=10*this.gf;)h-=10*this.gf}if(!f&&e>=this.S.length||
f&&!b&&e>=this.S.length||f&&b&&(this.S.eb!=this.S.rq||this.S.qq!=this.ua)&&e>=this.S.length){this.Ki(this.S.length);this.A.Jd=0;this.hf=!1;if(this.Da.running){this.Yq();return}f="";if(this.A.uo){b=0;do e=this.co[this.S.jf],f=Math.floor(Math.random()*Object.keys(e).length),f=e[f],this.wc=f.cliptitle,e=f.nodeid,f=f.startview,b++;while(e==this.ua&&10>b)}else this.wc=this.S.rq,e=this.S.qq,f=this.S.tu;this.wc==this.S.eb&&this.ua==e?1<this.bb.length&&0<this.A.ik&&(this.A.startTime=a.getTime(),(b=this.ap())&&
b!=this.ua&&(this.Rc("{"+b+"}"),this.hf=!1,this.A.active=!0,this.F.ue=!0,this.F.Ud=!0)):(this.sd&&e!=this.ua&&(this.Rc("{"+e+"}",f),this.F.enabled?(this.A.active=!1,this.F.ue=!0,this.F.Ud=!0):this.A.active=!0),this.A.startTime=a.getTime())}else this.Ki(h),this.A.Jd=h}else if(e=this.Wb(0,0),f=this.Wb(0,1),h=this.Wb(0,2),n=this.Wb(0,3),3!=n.sb&&(n=0),this.A.gk||this.I.aborted||this.Da.running||b){if(this.hf=!0,this.A.startTime=0<this.A.Jd?a.getTime()-100*this.A.Jd:a.getTime(),this.te=b){this.gf=this.kl=
0;b=10*this.B.j.currentTime;for(e=Math.floor(b);!this.Wb(e,3)&&0<e;)e--;e=this.Wb(e,3);e.value==this.Z()?this.uh=-1:(this.uh=e.value,this.jj=b,this.jl=Math.max(5,e.time+e.Kb-b))}}else{b={};if(0<this.A.Jd)b.pan=this.A.ci,b.tilt=this.A.di,b.fov=this.A.bi,b.projection=this.A.eq;else{b.pan=e.value;if(!this.Ia()){for(;360<b.pan;)b.pan-=360;for(;-360>b.pan;)b.pan+=360}b.tilt=f.value;b.fov=h.value;b.projection=n?n.value:4}b.timingFunction=3;b.speed=1;this.I.Gg=!0;this.mi(b);this.A.active=!0}}else 0<this.A.ik&&
this.sd&&e>=1E3*this.A.ik?1<this.bb.length&&(this.A.startTime=a.getTime(),(b=this.ap())&&b!=this.ua&&(this.A.td=a.getTime(),this.A.timeout=0,this.Rc("{"+b+"}"),this.A.active=!0,this.F.ue=!0)):(e=a.getTime(),h=f=1E3/60,0!=this.A.td&&(h=e-this.A.td),b.u.d=this.A.Ok*((this.A.Rp?b.u.Ua:0)-b.u.m)/100,b.fov.d=this.A.Ok*(b.fov.Ua-b.fov.m)/100,b.pan.d=.95*b.pan.d+-this.A.speed*this.Kh()*.05,f=h/f,b.pan.m+=b.pan.d*f,b.u.m+=b.u.d*f,b.fov.m+=b.fov.d*f,this.A.td=e,this.update());3E3<a.getTime()-this.A.bk&&!this.A.sj&&
(this.xa.style.cursor="none",this.A.sj=!0)}else!this.Da.finished&&1E3<a.getTime()-this.Jf&&(this.za.splice(this.za.indexOf(this.S),1),this.S=this.Tl(!1),this.wc=this.S.eb,this.A.active=!1,this.A.Jh=!0),this.A.enabled&&!this.U.Sj()&&a.getTime()-this.Jf>1E3*this.A.timeout&&!this.A.Jh&&(this.A.Yg&&this.Oh()||!this.A.Yg)&&(this.A.active=!0,this.A.startTime=a.getTime(),this.A.td=0,this.G("autorotatechanged",{}),b.pan.d=0,b.u.d=0,b.fov.d=0,b.fov.Yc.enabled=!1),!this.qa.enabled||this.U.Sj()||0==b.pan.d&&
0==b.u.d&&0==b.fov.d||(this.I.Nj=!0,b.pan.d*=.9,b.u.d*=.9,b.fov.d*=.9,b.pan.m+=b.pan.d,b.u.m+=b.u.d,this.K.re&&b.fov.Yc.enabled?this.no(b.fov.d,b.fov.Yc):this.mj(b.fov.d),1E-4>b.pan.d*b.pan.d+b.u.d*b.u.d+b.fov.d*b.fov.d&&(b.pan.d=0,b.u.d=0,b.fov.d=0,b.fov.Yc.enabled=!1),this.update())}Nr(a){let b=this.F;b.ed=!1;if("previewtrack"==b.delay&&!this.Ia()||"videopano"==b.delay&&!this.B.j)if(0<this.D.M.levels.length)this.D.M.levels[this.D.M.levels.length-1].loaded||(b.ed=!0);else{let e=!0;this.da.forEach(f=>
{f.Gq()||(e=!1)});e||(b.ed=!0)}else"videopano"==b.delay&&this.B.j&&!this.B.Eg&&(b.ed=!0);b.ed||this.Jk(a)}Jk(a){a||(a=new Date);let b=this.F;b.ed=!1;b.xd=!0;b.Ud=this.A.gd;b.un=a.getTime()+100;0==b.Vc||b.ph||this.tn();this.G("transitionstarted",{})}tn(){let a=this.F;4==a.Vc?(this.S=this.Tl(!0,a.zn,a.An,a.Ui),this.wc=this.S.eb,this.A.active=!0,this.Da.running=!0):this.moveTo(a.zn,a.An,a.Ui,a.qh,0,a.ne)}Nu(a){var b=this.F;if(!b.Mi){if(b.Qd){var e=(a.getTime()-b.hs)/(1E3*b.gs);1<=e?(b.Fm(1),b.Qd=!1,
this.Oo(),this.rn(),this.Nr(a)):0<=e&&b.Fm(e)}else b.xd&&(e=(a.getTime()-b.un)/(1E3*b.tf),1<=e?(b.xd=!1,this.G("transitionended",{}),this.Jf=a.getTime(),this.update(),0!=b.Vc&&b.ph&&this.tn(),4!=b.Vc&&(this.zk(!1),this.A.active=b.ue,this.A.gd=b.Ud,this.G("autorotatechanged",{}),b.ue=!1,b.Ud=!1),this.A.td=0,this.ha&&this.Bl(),this.yh=!1):0<=e&&("mesh"==b.type?this.da[0].D.Ia()||this.da[1].D.Ia()?b.un=1:b.Ju(e):b.Fm(e)));b=this.Fu;b.qs&&(b.tj?a.getTime()-b.Ll>=1E3*b.delay&&(b.tj=!1):(b.current+=b.Bd,
0>b.current&&(b.current=0,b.Bd=-b.Bd,b.tj=!0,b.Ll=a.getTime()),1<b.current&&(b.current=1,b.Bd=-b.Bd,b.tj=!0,b.Ll=a.getTime())))}}Qu(){let a,b=this.P;if(0<b.dc.length){for(a=0;a<b.dc.length;a++)b.wd[a]!=b.Tc[a]&&(b.wd[a]>b.Tc[a]?(b.Tc[a]+=.05,b.wd[a]<b.Tc[a]&&(b.Tc[a]=b.wd[a])):(b.Tc[a]-=.05,b.wd[a]>b.Tc[a]&&(b.Tc[a]=b.wd[a],-1!=b.Ek.indexOf(b.dc[a])&&(b.Ek.splice(b.Ek.indexOf(b.dc[a]),1),b.dc.splice(a,1),b.wd.splice(a,1),b.Tc.splice(a,1)))));this.update()}if(2==b.mode)for(a=0;a<this.L.length;a++){let e=
this.L[a];"poly"==e.type&&e.kb!=e.Fa&&(e.kb>e.Fa?(e.Fa+=b.Bd,e.kb<e.Fa&&(e.Fa=e.kb)):(e.Fa-=b.Bd,e.kb>e.Fa&&(e.Fa=e.kb)),this.update())}3==b.mode&&b.kb!=b.Fa&&(b.kb>b.Fa?(b.Fa+=b.Bd,b.kb<b.Fa&&(b.Fa=b.kb)):(b.Fa-=b.Bd,b.kb>b.Fa&&(b.Fa=b.kb)),this.update())}Pu(){var a=this.D;let b=this.qa;this.U.Sj()&&(this.K.mc?(b.ma.x=.4*(this.oa.na.x-b.na.x),b.ma.y=.4*(this.oa.na.y-b.na.y),b.na.x+=b.ma.x,b.na.y+=b.ma.y):(b.ma.x=.1*-this.oa.ma.x*this.K.sensitivity/8,b.ma.y=.1*-this.oa.ma.y*this.K.sensitivity/8),
this.Zr(b.ma.x,b.ma.y),this.update());b.fov.active&&(this.K.re&&b.fov.center.enabled?this.mo(.4*(b.fov.kc-a.fov.m),b.fov.center):this.vh(.4*(b.fov.kc-a.fov.m)),.001>Math.abs(b.fov.kc-a.fov.m)/a.fov.m&&(b.fov.active=!1),this.update());!b.enabled||0==b.ma.x&&0==b.ma.y||this.U.Sj()||(a=.9*(1-b.bn),b.ma.x*=a,b.ma.y*=a,this.I.Nj=!0,.01>b.ma.x*b.ma.x+b.ma.y*b.ma.y?(b.ma.x=0,b.ma.y=0):(this.Zr(b.ma.x,b.ma.y),this.update()))}Ou(){if(!this.Oh()&&this.ee&&5<this.D.M.Mq){let a,b=0,e=this.md.length;if(this.kh)e=
50,this.Ml<e&&this.Ml++,b=this.Ml;else for(a=0;a<e;a++)(this.md[a].complete&&this.md[a].src!=this.Ks||""==this.md[a].src)&&b++;b==e?(this.ri=1,this.isLoaded=!0,this.aa&&this.aa.ggLoaded&&this.aa.ggLoaded(),this.G("imagesready",{}),this.A.Yg&&this.A.enabled&&!this.I.active&&!this.F.Qd&&(this.A.active=!0,this.A.Yg=!1,this.A.startTime=(new Date).getTime(),this.A.td=0)):this.ri=b/(1*e);this.G("downloadprogress",{percentLoaded:this.ri})}}pi(){var a=this;a.tk||(a.Qj?setTimeout(function(){a.tk=!1;a.pi()},
1E3/60):window.requestAnimationFrame(function(){a.tk=!1;a.pi()}));a.tk=!0;this.Tm=this.sk=0;a.I.Nj=!1;this.pa&&(this.Rl&&(this.If(0),this.fd()),this.H.width!=this.ia.offsetWidth||this.H.height!=this.ia.offsetHeight)&&(this.H.width=this.ia.offsetWidth,this.H.height=this.ia.offsetHeight);var b=new Date;this.Sl++;120<=this.Sl&&(this.Sl=0);this.pa&&this.da.forEach(e=>{e.Js()});this.Kn&&(this.fd(),this.Kn=!1);this.Pu();this.Ou();this.B.j&&this.B.Mc&&!this.hf&&!this.I.Gg&&this.Wk&&(this.S=this.Wk,this.Ki(10*
this.B.j.currentTime));this.Lu(b);this.D.Mu();this.Nu(b);this.da.forEach(e=>{e.fw()});(0<=this.P.mode||0<this.P.dc.length)&&this.Qu();this.Qr();this.Ma&&(0<this.Ae?this.Ae--:(this.Ma=!1,this.Ae=0),this.F.xd||this.F.Qd||this.F.ed||this.ef||(this.bj(),this.G("renderframe",{})),this.G("repaint",{}));b=this.Zl();b!=this.bq&&(b?(this.aa&&this.aa.ggReLoadedLevels&&this.aa.ggReLoadedLevels(),this.G("tilesrequested",{})):(a.aa&&a.aa.ggLoadedLevels&&a.aa.ggLoadedLevels(),this.G("tilesready",{})),this.bq=b)}Qr(){const a=
this.D;this.G("timer",{});this.Uk();if(this.Mf.pan!=a.pan.m||this.Mf.u!=a.u.m||this.Mf.fov!=a.fov.m)this.Mf.pan=a.pan.m,this.Mf.u=a.u.m,this.Mf.fov=a.fov.m,this.G("positionchanged",{});this.vm!=this.Z()&&(this.vm=this.Z(),this.G("projectionchanged",{}))}Nh(a){const b=this.D;switch(a){case 4:a=Math.min(110,b.fov.max);break;case 12:a=Math.min(270,b.fov.ym);a=Math.min(360*this.aspect(),a);a=Math.min(360/this.aspect(),a);break;case 9:a=Math.min(270,b.fov.zm);break;default:a=90}return a}Uk(){var a=this.D;
this.Fl.Fe(a.pan.m,a.u.m);for(a=0;a<this.X.length+this.V.length;a++){let b;if(a<this.X.length)b=this.X[a];else if(b=this.V[a-this.X.length],b.vd)continue;b.Uk()}}Iv(a,b){let e=this,f=[];let h;if(e.K.vo&&e.pa){let r=[];r.push({text:this.Ka("Rectilinear Projection"),vi:4});r.push({text:this.Ka("Stereographic Projection"),vi:9});r.push({text:this.Ka("Fisheye Projection"),vi:12});for(h=0;h<r.length;h++){let w=r[h],x={};var n="text-align: left;";n+="margin: 0;";n=e.Z()==w.vi?n+"padding: 5px 20px 5px 7px;":
n+"padding: 5px 20px;";n+="vertical-align: left;";n+="cursor: pointer;";x.style=n;x.onclick=function(y){return function(){e.tl(y,1);e.update()}}(w.vi);x.innerHTML=e.Z()==w.vi?"&#10687; "+w.text:w.text;f.push(x)}(e.K.yl||e.K.zl&&e.Bg())&&f.push({hr:!0})}e.K.yl&&(n={style:"text-align: left;margin: 0;padding: 5px 20px;vertical-align: left;cursor: pointer;",onclick:function(){e.Pk()}},n.innerHTML=e.Yh()?this.Ka("Exit Fullscreen"):this.Ka("Enter Fullscreen"),f.push(n));e.K.zl&&e.Bg()&&(n={style:"text-align: left;margin: 0;padding: 5px 20px;vertical-align: left;cursor: pointer;",
onclick:function(){e.rg()}},n.innerHTML=this.Ka("Enter VR"),f.push(n));e.Jv(a,b,f)}ps(){let a=this,b;b=a.xa;a.control=b;a.control=b;setTimeout(function(){a.pi()},10);setTimeout(function(){a.Cq()},200);setTimeout(function(){a.qe();a.bj()},10);b.addEventListener&&(b.addEventListener("touchstart",function(e){a.Yv(e)},!1),b.addEventListener("touchmove",function(e){a.Xv(e)},!1),b.addEventListener("touchend",function(e){a.Wv(e)},!1),b.addEventListener("touchcancel",function(e){a.Vv(e)},!1),b.addEventListener("pointerdown",
function(e){a.pq(e)},!1),b.addEventListener("MSPointerDown",function(e){a.pq(e)},!1),b.addEventListener("MSGestureStart",function(e){a.Zo(e)},!1),b.addEventListener("MSGestureEnd",function(e){a.Yo(e)},!1),b.addEventListener("MSGestureChange",function(e){a.ou(e)},!1),b.addEventListener("gesturestart",function(e){a.Zo(e)},!1),b.addEventListener("gesturechange",function(e){a.Rs(e)},!1),b.addEventListener("gestureend",function(e){a.Yo(e)},!1),b.addEventListener("mousedown",function(e){a.nu(e)},!1),b.addEventListener("mousemove",
function(e){a.mu(e)},!1),document.addEventListener("mouseup",function(e){a.lu(e)},!1),b.addEventListener("mousewheel",function(e){a.mq(e)},!1),b.addEventListener("DOMMouseScroll",function(e){a.mq(e)},!1),document.addEventListener("keydown",function(e){a.du(e)},!1),document.addEventListener("keyup",function(e){a.eu(e)},!1),window.addEventListener("resize",function(){a.qe()},!1),a.container.addEventListener("resize",function(){a.qe()},!1),window.addEventListener("blur",function(){a.wu()},!1),a.T.addEventListener("webkitfullscreenchange",
function(){a.oi()},!1),document.addEventListener("mozfullscreenchange",function(){a.oi()},!1),window.addEventListener("webkitfullscreenchange",function(){a.oi()},!1),document.addEventListener("MSFullscreenChange",function(){a.oi()},!1),document.addEventListener("fullscreenchange",function(){a.oi()},!1));b.oncontextmenu=function(e){void 0===e&&(e=window.event);if(e.target&&!a.nc(e.target))return!0;if(!e.ctrlKey){a.Af(e);const f=a.Cf();a.Iv(e.clientX-f.x,e.clientY-f.y);return!1}return!0};window.addEventListener("deviceorientation",
function(e){a.Cu(e.alpha,e.beta,e.gamma,e.absolute);null!=e.alpha&&null!=e.beta&&null!=e.gamma&&0==a.gm&&(a.gm=!0,a.G("gyroavailable",{available:!0}),a.G("playerstatechanged",{}))})}Xn(){for(let a=0;a<this.L.length;a++)if("point"==this.L[a].type&&(this.ka&&this.ka.addSkinHotspot?(this.L[a].xf(),this.L[a].j=this.ka.addSkinHotspot(this.L[a])):(this.L[a].j=new xd(this,this.L[a]),this.L[a].od&&this.L[a].ag&&(this.L[a].j.ggUse3d=!0,this.L[a].j.gg3dDistance=this.L[a].uj)),this.L[a].j.__div.style.left="-1000px",
this.L[a].j.__div.style.top="-1000px",this.L[a].j&&this.L[a].j.__div)){let b=this.xa.firstChild;b?this.xa.insertBefore(this.L[a].j.__div,b):this.xa.appendChild(this.L[a].j.__div)}}vs(){if(!this.ka||!this.ka.addSkinHotspot)for(var a=0;a<this.L.length;a++)"point"==this.L[a].type&&this.L[a].j&&this.L[a].j.Nt()}ws(){for(var a=0;a<this.Ya.length;a++)this.Ya[a].Wd();for(a=0;a<this.Qb.length;a++)this.Qb[a].Wd();for(a=0;a<this.X.length;a++)this.X[a].Wd();for(a=0;a<this.V.length;a++)this.V[a].Wd()}al(){let a,
b=document.createElement("fakeelement"),e={OTransition:"oTransitionEnd",MSTransition:"msTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd",transition:"transitionEnd"};for(a in e)if(void 0!==b.style[a])return e[a]}Wa(a){let b=[];a="*"==a?"^.*$":"#"==a.substring(0,1)?a.substring(1):"^"+a+"$";a=new RegExp(a,"");for(var e=0;e<this.X.length;e++)a.test(this.X[e].id)&&b.push(this.X[e]);for(e=0;e<this.V.length;e++)a.test(this.V[e].id)&&b.push(this.V[e]);for(e=0;e<this.Ya.length;e++)a.test(this.Ya[e].id)&&
b.push(this.Ya[e]);for(e=0;e<this.Qb.length;e++)a.test(this.Qb[e].id)&&b.push(this.Qb[e]);return b}pp(a){if("_videopanorama"==a)return this.B.j;a=this.Wa(a);return 0<a.length?a[0].j:null}op(a){a=this.Wa(a);return 0<a.length&&a[0].ta?a[0]:null}Jq(a,b){let e=this;b.addEventListener("ended",function(n){e.G("videoended",{video:n.target})});b.addEventListener("pause",function(n){e.G("videopaused",{video:n.target})});b.addEventListener("play",function(n){e.G("videostarted",{video:n.target})});let f=null;
for(var h=0;h<this.V.length;h++)this.V[h].id==a&&(this.V[h].j=b,f=this.V[h]);f||(f=new Qb(this),f.registerElement(a,b));e.yc&&f.jn(1);return f}isPlaying(a){if("_main"===a)return!0;a=this.Wa(a);return 0<a.length?a[0].ta?a[0].Kg:!a[0].j.ended&&!a[0].j.paused:!1}Md(a,b){let e=null;if(!this.yc)try{let h=this.Wa(a);for(var f=0;f<h.length;f++){let n=h[f];n instanceof Bb&&(n.gb=b&&!isNaN(Number(b))?Number(b)-1:n.loop-1,-1>=n.gb&&(n.gb=1E7),this.isPlaying(a)&&this.Ti(a),n.ta?n.Vd():(e=n.j.play(),void 0!==
e&&e.then(()=>{this.Ug(!0)}).catch(()=>{this.Ug(!1)}),n.zd&&n.se()),this.Hi(n.id,!1))}}catch(h){}return e}Jm(a,b){a=this.Wa(a);for(var e=0;e<a.length;e++){let f=a[e];this.isPlaying(f.id)?this.Jg(f.id):this.Md(f.id,b)}}Fq(a,b){a=this.Wa(a);for(var e=0;e<a.length;e++){let f=a[e];f instanceof Bb&&(this.isPlaying(f.id)?this.Ti(f.id):this.Md(f.id,b))}}Jg(a){try{let b;if("_main"==a){this.Hi(a,!0);for(b=0;b<this.X.length;b++)this.X[b].ta?this.X[b].ol():this.X[b].j.pause();for(b=0;b<this.V.length;b++)this.V[b].j.pause()}else{let e=
this.Wa(a);for(b=0;b<e.length;b++){let f=e[b];f instanceof Bb&&(this.Hi(f.id,!0),f.ta?f.ol():f.j.pause())}}}catch(b){}}Un(a,b){a=this.Wa(a);for(var e=0;e<a.length;e++){let f=a[e];0==b||1==b?f.Df&&f.Df(1==b):2==b&&f.Ie&&f.Ie();!f.Na||-1==this.Ya.indexOf(f)&&-1==this.V.indexOf(f)&&-1==this.Qb.indexOf(f)||(this.Sb=this.Ue=f)}}Ti(a){let b;try{if("_main"===a){this.Hi(a,!0);for(b=0;b<this.X.length;b++)this.X[b].ta?this.X[b].ig():(this.X[b].j.pause(),this.X[b].j.currentTime=0);for(b=0;b<this.V.length;b++)this.V[b].j.pause(),
this.V[b].j.currentTime=0}else{let e=this.Wa(a);for(b=0;b<e.length;b++){let f=e[b];f instanceof Bb&&(this.Hi(f.id,!0),f.ta?f.ig():f.j&&f.j.pause&&(f.j.pause(),f.j.currentTime=0))}}}catch(e){}}Hi(a,b){a=this.Wa("_main"===a?".*":a);for(let e=0;e<a.length;e++){let f=a[e];f.stopped=b;f.wn=!1}}sr(a){a=this.Wa(a);return 0<a.length?(a=a[0],a instanceof Bb?a.ta?a.ss():a.j?a.j.currentTime:0:0):0}ur(a,b){a=this.Wa(a);for(let e=0;e<a.length;e++){let f=a[e];f instanceof Bb&&(f.ta?(0>b&&(b=0),b>f.audioBuffer.duration&&
(b=f.audioBuffer.duration-.1),f.ts(b)):f.j&&(0>b&&(b=0),b>f.j.duration&&(b=f.j.duration-.1),f.j.currentTime=b))}}tr(a,b){a=this.Wa(a);0<a.length&&(a=a[0],a.j&&(a.j.playbackRate=b))}setVolume(a,b){try{let e,f=Number(b);1<f&&(f=1);0>f&&(f=0);"_videopanorama"===a&&this.B.j&&(this.B.j.volume=f,this.B.level=f);if("_main"===a){this.fa=f;for(e=0;e<this.X.length;e++)this.X[e].j.volume=this.X[e].level*this.fa;for(e=0;e<this.V.length;e++)this.V[e].j.volume=this.V[e].level*this.fa;this.B.j&&(this.B.j.volume=
this.fa*this.B.level)}else{let h=this.Wa(a);for(e=0;e<h.length;e++){let n=h[e];n.j&&null!=n.j.volume&&(n.j.volume=f*this.fa);n.level=f}}}catch(e){}this.G("elementvolume",{id:a,type:"set",volume:b})}ro(a,b){try{let e,f;"_videopanorama"===a&&this.B.j&&(this.B.j.volume+=Number(b));if("_main"===a){e=this.fa;e+=Number(b);1<e&&(e=1);0>e&&(e=0);this.fa=e;for(f=0;f<this.X.length;f++)this.X[f].j.volume=this.X[f].level*this.fa;for(f=0;f<this.V.length;f++)this.V[f].j.volume=this.V[f].level*this.fa;this.B.j&&
(this.B.j.volume=this.fa*this.B.level)}else{let h=this.Wa(a);for(f=0;f<h.length;f++){let n=h[f];n instanceof Bb&&(e=n.level,e+=Number(b),1<e&&(e=1),0>e&&(e=0),n.level=e,n.j&&null!=n.j.volume&&(n.j.volume=e*this.fa))}}}catch(e){}this.G("elementvolume",{id:a,type:"change",volume:b})}Ck(a,b){if("_main"===a||"_all"===a||"_videopanorama"===a)"_all"===a&&(b=(this.yc=1==b||-1==b&&!this.yc?!0:!1)?1:0),this.B.j&&(this.B.j.muted=-1==b?!this.B.j.muted:1==b);let e=this.Wa("_main"===a||"_all"===a?".*":a);for(let f=
0;f<e.length;f++){let h=e[f];h.jn&&h.jn(b)}this.G("elementmuted",{id:a,state:b})}zr(){this.xg();this.Zd&&setTimeout(()=>{this.xg()},500)}Ak(a,b,e){a.j.style.opacity=0==b?1:0;a.j.style[this.Hc]="opacity "+e+"ms";a.j.style.opacity=0==b?0:1}Bk(a,b,e){let f=this.Wa(a);for(var h=0;h<f.length;h++){let n=f[h];n.j&&(this.Ne()?this.ja.Bk(a,b,e):0==b?(this.Ak(n,b,e),setTimeout(function(){0==n.j.style.opacity&&n.Vf(!1)},e+10),n.Xb=!1):1==b?(this.Ak(n,b,e),n.Vf(!0),n.Xb=!0):2==b&&("visible"==n.j.style.visibility?
(this.Ak(n,0,e),setTimeout(function(){0==n.j.style.opacity&&n.Vf(!1)},e+10),n.Xb=!1):(this.Ak(n,1,e),n.Vf(!0),n.Xb=!0)))}}rn(){try{let a=this,b=!1,e=!1;for(let f=0;f<this.X.length;f++){let h=this.X[f];if(-1==h.loop||this.isPlaying(h.id))continue;if(h.stopped)continue;let n;this.ga&&this.ab.enabled&&4!=h.mode&&6!=h.mode&&!this.yc?this.ab.Bo?(h.ta?h.Vd():(n=h.j.play(),h.zd&&h.se(),h.j.currentTime=0),h.va=0,e=!0):b=!0:4==h.mode||6==h.mode||"_background"==h.id&&this.isPlaying(h.id)||this.yc||!(0==h.loop||
0<h.gb)||(h.ta?h.Vd():(n=h.j.play(),h.j.currentTime&&(h.j.currentTime=0)));void 0!==n&&n.then(()=>{this.Ug(!0)}).catch(()=>{this.Ug(!1)})}b&&setTimeout(function(){a.ab.Ov()},1E3*this.ab.Kb);e&&(this.ab.Mv=this.ga.currentTime,this.ab.Lv=setInterval(function(){a.ab.Os()},10))}catch(a){}}Oo(){for(let a=0;a<this.ab.$i.length;a++)this.ab.No(this.ab.$i[a])}Ym(){if(this.O&&this.O!=this.Va&&this.O.div&&this.O.div.onmouseout)this.O.div.onmouseout();let a;for(;0<this.L.length;)a=this.L.pop(),a.j&&(this.xa.removeChild(a.j.__div),
delete a.j),a.j=null;this.P.Fa=0;this.P.kb=0;this.G("hotspotsremoved",{})}Vg(a){this.fg=a;this.T.style.zIndex=0!=a?a.toString():"auto";this.xa.style.zIndex=(a+4).toString();this.fe.style.zIndex=(a+5).toString();for(let b=0;b<this.Jj();b++){let e;e=b<this.V.length?this.V[b]:this.Ya[b-this.V.length];e.j&&(e.j.style.zIndex=(a+(e.Na?8E4:0)).toString())}}Di(a){let b=this.oc!==a;this.oc!==a&&(this.oc=a,this.update(100));if(this.oc){if(this.lh)try{this.T.webkitRequestFullScreen?this.T.webkitRequestFullScreen():
this.T.mozRequestFullScreen?this.T.mozRequestFullScreen():this.T.msRequestFullscreen?this.T.msRequestFullscreen():this.T.requestFullScreen?this.T.requestFullScreen():this.T.requestFullscreen&&this.T.requestFullscreen()}catch(e){}this.T.style.position="absolute";a=this.Cf();this.T.style.left=window.pageXOffset-a.x+this.margin.left.ba+"px";this.T.style.top=window.pageYOffset-a.y+this.margin.top.ba+"px";this.Vg(10);document.body.style.overflow="hidden";b&&(this.aa&&this.aa.ggEnterFullscreen&&this.aa.ggEnterFullscreen(),
this.G("fullscreenenter",{}),this.G("playerstatechanged",{}))}else{if(this.lh)try{document.webkitIsFullScreen?document.webkitCancelFullScreen():document.mozFullScreen?document.mozCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():document.fullScreen&&(document.cancelFullScreen?document.cancelFullScreen():document.exitFullscreen&&document.exitFullscreen())}catch(e){}this.T.style.position="relative";this.T.style.left="0px";this.T.style.top="0px";this.Vg(0);document.body.style.overflow=
"";b&&(this.aa&&this.aa.ggExitFullscreen&&this.aa.ggExitFullscreen(),this.G("fullscreenexit",{}),this.G("playerstatechanged",{}))}this.qe()}rg(a){this.ja.Fs();this.Wj||this.Vj||this.Oq();this.ja.rg(a);this.ja.Eu();return!0}Ft(){return"N/A"}Et(){if(this.ja)return this.ja.Ne()?this.ja.Yk:this.ja.camera}Gt(){if(this.ja)return this.ja.xb}wv(a,b){this.ja.Wq(a,b)}Ht(){return this.ja.Dc}xv(a){this.ja&&(this.ja.Ee=a)}sg(){this.ja&&(this.ja.sg(),this.ja.Uf(),this.ja.xl());return!0}Jr(){this.ja&&(this.ja.Ne()?
this.sg():this.rg());return!0}Ne(){return this.ja&&this.ja.Ne()?!0:!1}Bg(){return this.ja&&this.ja.Bg()?!0:!1}ir(a){return this.ja?(2==a?this.ja.le():this.ja.le(0!=a),!0):!1}Hj(){return this.ja?this.ja.Hj():!1}zv(a){this.ja&&this.ja.rv(a)}yv(a){this.ja&&this.ja.ev(a)}Ar(a,b,e){this.A.Td=this.A.kj;this.A.gd=this.A.cg;this.A.enabled=!0;this.A.gg=this.A.enabled;this.A.active=!0;this.A.td=0;let f=new Date;this.A.Jd=0;this.A.startTime=f.getTime();void 0!==a&&0!=a&&(this.A.speed=a);void 0!==b&&(this.A.timeout=
b);void 0!==e&&(this.A.Ok=e);this.G("autorotatechanged",{})}Dr(){this.A.active=!1;this.A.enabled=!1;this.A.gg=this.A.enabled;this.hf=this.A.gk=!1;this.I.active&&this.I.Gg&&(this.I.active=!1,this.I.Gg=!1,this.I.rb=0);this.G("autorotatechanged",{})}Ir(){this.A.enabled=!this.A.active;this.A.gg=this.A.enabled;this.A.active=this.A.enabled;this.A.td=0;if(this.A.enabled){let a=new Date;this.A.Jd=0;this.A.startTime=a.getTime();this.A.Td=this.A.kj;this.A.gd=this.A.cg}this.G("autorotatechanged",{})}Dq(){this.ra(!1)}Qq(){this.A.enabled&&
(this.A.active=!0,this.A.td=0)}Vq(a){this.A.Hg=a;this.A.tq=!0}vr(a){this.Da.running&&this.Yq();this.wc="";a&&""!=a&&(this.A.Td=a);this.A.gg=this.A.enabled;this.A.gd=!0;this.A.enabled=!0;this.A.active=!0;this.A.td=0;this.F.xd&&(this.F.Ud=!0,this.F.ue=!0);a=new Date;this.A.Jd=0;this.A.startTime=a.getTime();this.G("autorotatechanged",{})}Yu(a){a&&""!=a&&this.A.Td!=a||0==this.A.Jd?this.vr(a):(this.A.gd=!0,this.A.enabled=!0,this.A.active=!0,this.G("autorotatechanged",{}))}Yq(){this.Da.running=!1;this.Da.finished=
!0;this.A.active=this.F.ue;this.A.gd=this.F.Ud;this.za.splice(this.za.indexOf(this.S),1);0<this.za.length&&(this.S=this.za[0]);this.wc="";this.zk(!1);this.F.ue=!1;this.F.Ud=!1;this.Jf=(new Date).getTime()}so(a){if(this.container=document.getElementById(a))return this.container.innerHTML="",!0;alert("container not found!");return!1}Xq(a){let b=this;b.container.removeEventListener("resize",function(){b.qe()});b.so(a)&&(b.container.appendChild(b.T),b.container.addEventListener("resize",function(){b.qe()},
!1))}Cl(a){this.so(a)&&(this.T=document.createElement("div"),this.T.onselectstart=function(){return!1},this.T.setAttribute("id","viewport"),this.T.setAttribute("role","application"),a="top: 0px;left: 0px;position: relative;perspective: none;-ms-touch-action: none;touch-action: none;-webkit-user-select: none;user-select: none;text-align: left;"+(this.Tb+"user-select: none;"),this.T.setAttribute("style",a),this.container.appendChild(this.T),this.ia=document.createElement("div"),a="top:\t0px;left: 0px;width:  100px;height: 100px;overflow: hidden;position:absolute;-ms-touch-action: none;touch-action: none;"+
(this.Tb+"user-select: none;"),this.ia.setAttribute("id","viewer"),this.ia.setAttribute("style",a),this.T.appendChild(this.ia),this.xa=document.createElement("div"),this.xa.setAttribute("id","hotspots"),a="top:\t0px;left: 0px;width:  100px;height: 100px;overflow: hidden;position:absolute;",this.om&&(a+="background-image: url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7);"),this.Mb&&!this.pa&&(a+=this.Tb+"transform: translateZ(9999999px);"),a+=this.Tb+"user-select: none;",
this.xa.setAttribute("style",a),this.T.appendChild(this.xa),this.fe=document.createElement("div"),this.fe.setAttribute("id","hotspottext"),this.fe.setAttribute("style","top:\t0px;left: 0px;position:absolute;padding: 3px;visibility: hidden;"),this.fe.classList.add("ggskin"),this.fe.classList.add("ggdefaulthotspot"),this.fe.innerHTML=" Hotspot text!",this.T.appendChild(this.fe),this.divSkin=this.aa=this.xa,this.Vg(0))}Uf(){var a;this.qa.ma.x=0;this.qa.ma.y=0;this.da.forEach(f=>{f.Uf()});let b=[];for(a=
0;a<this.V.length;a++){var e=this.V[a];e.vd?b.push(e):e.We()}for(a=0;a<this.Ya.length;a++)this.Ya[a].We();for(a=0;a<this.Qb.length;a++)this.Qb[a].We();this.P.um=-1;this.cd=0;this.Yf=[];this.ab.$i=[];for(a=0;a<this.X.length;a++)e=this.X[a],e.sq||7>this.Wc&&0==e.mode||this.Yf.push(e);this.V=b;this.Ya=[];this.Qb=[];this.B.j&&(this.T.removeChild(this.B.j),this.B.j=null,a=this.Wa("_videopanorama"),0<a.length&&(a[0].j=null));this.B.Mc=!1;this.B.Eg=!1}us(a){a="{"==a.charAt(0)?a.slice(1,a.length-1):a;let b=
this.ye[a];b&&(a=b);this.G("beforechangenodeid",{oldNodeId:this.ua,nodeId:a});this.em?this.em=!1:this.Bm.push(this.ua);""!=this.ua&&-1==this.Xk.indexOf(this.ua)&&(this.Xk.push(this.ua),this.G("changevisitednodes",{}));this.cq=this.ua;this.ua=a;this.ka&&this.ka.changeActiveNode&&this.ka.changeActiveNode("{"+a+"}");this.De=!0}qd(){return this.ua}rp(a){return 0<this.bb.length?(a||(a=this.ua),a=this.bb.indexOf(a),a++,a>=this.bb.length&&(a=0),this.bb[a]):""}zp(a){return 0<this.bb.length?(a||(a=this.ua),
a=this.bb.indexOf(a),a--,0>a&&(a=this.bb.length-1),this.bb[a]):""}ht(){return this.cq}Bt(){return this.Uc}uu(a){return-1!=this.Xk.indexOf(a)}As(){this.Xk=[];this.G("changevisitednodes",{})}Sm(a,b){a=a.firstChild;this.df=[];this.bb=[];this.ye=[];this.Ag=!1;var e;(e=a.getAttributeNode("appversion"))&&e.nodeValue.toString().match(/(\d+)\.(\d+)\.?(\d+)?/);for(var f=a.firstChild;f;){if("map"==f.nodeName){var h={};(e=f.getAttributeNode("index"))&&(h.index=Number(e.nodeValue));(e=f.getAttributeNode("title"))&&
(h.title=e.nodeValue.toString());e=f.getAttributeNode("type");h.type=e.nodeValue.toString();"web"==h.type?(e=f.getAttributeNode("mapprovider"),h.mapprovider=e.nodeValue.toString(),(e=f.getAttributeNode("mapstyle"))&&(h.mapstyle=e.nodeValue.toString()),(e=f.getAttributeNode("googlecustomstylecode"))&&(h.googlecustomstylecode=e.nodeValue.toString()),(e=f.getAttributeNode("mapurltemplate"))&&(h.mapurltemplate=e.nodeValue.toString()),(e=f.getAttributeNode("mapmaxzoom"))&&(h.mapmaxzoom=Number(e.nodeValue)),
(e=f.getAttributeNode("customlayernames"))&&(h.customlayernames=e.nodeValue.toString().split("|")),(e=f.getAttributeNode("customlayerurltemplates"))&&(h.customlayerurltemplates=e.nodeValue.toString().split("|")),(e=f.getAttributeNode("customlayermaxzooms"))&&(h.customlayermaxzooms=e.nodeValue.toString().split("|")),(e=f.getAttributeNode("mapkey"))&&(h.mapkey=e.nodeValue.toString()),(e=f.getAttributeNode("styleurl"))&&(h.styleurl=e.nodeValue.toString()),(e=f.getAttributeNode("mapboxlayernames"))&&
(h.mapboxlayernames=e.nodeValue.toString().split("|")),(e=f.getAttributeNode("mapboxlayerstyleurls"))&&(h.mapboxlayerstyleurls=e.nodeValue.toString().split("|")),(e=f.getAttributeNode("googlelayernames"))&&(h.googlelayernames=e.nodeValue.toString().split("|")),(e=f.getAttributeNode("googlelayerstyles"))&&(h.googlelayerstyles=e.nodeValue.toString().split("|")),(e=f.getAttributeNode("maplimits"))&&(h.maplimits=e.nodeValue.toString().split("|"))):(e=f.getAttributeNode("width"),h.width=Number(e.nodeValue),
e=f.getAttributeNode("height"),h.height=Number(e.nodeValue),e=f.getAttributeNode("zoomlevels"),h.zoomlevels=Number(e.nodeValue),e=f.getAttributeNode("tileformat"),h.tileformat=e.nodeValue.toString(),e=f.getAttributeNode("bgcolor"),h.bgcolor=e.nodeValue.toString(),e=f.getAttributeNode("crispedges"),h.crispedges=1==e.nodeValue,e=f.getAttributeNode("transparent"),h.transparent=1==e.nodeValue,e=f.getAttributeNode("floorplannorth"),h.floorplannorth=Number(e.nodeValue));e=f.getAttributeNode("id");this.rc[e.nodeValue.toString()]=
h}f=f.nextSibling}if("tour"==a.nodeName){this.sd=!0;if(e=a.getAttributeNode("start"))this.Uc=e.nodeValue.toString();if(e=a.getAttributeNode("hassounds"))this.Ag=1==e.nodeValue;this.hasOwnProperty("startNode")&&this.startNode&&(this.Uc=String(this.startNode),this.startNode="");this.hasOwnProperty("startView")&&this.startView&&("object"===typeof this.startView&&null!==this.startView?b=this.startView:""!=this.startView&&(b=String(this.startView)),this.startView="");f=a.firstChild;for(a=h="";f;){if("panorama"==
f.nodeName){if(e=f.getAttributeNode("id"))h=e.nodeValue.toString(),""==this.Uc&&(this.Uc=h),""==a&&(a=h),this.df[h]=f,this.bb.push(h);for(e=f.firstChild;e;){if("userdata"==e.nodeName){var n=this.tg(e);this.ff[h]=n;h==this.Uc&&(this.ff._first=n);n.customnodeid&&(this.ye[n.customnodeid]=h);this.Oc[h]=this.Ko(e);this.Fg[h]=this.Lo(e);this.eg[h]=this.Mo(e)}if("hotspots"==e.nodeName)for(n=e.firstChild;n;)"hotspot"!=n.nodeName&&"polyhotspot"!=n.nodeName||this.Nl(h,e),n=n.nextSibling;e=e.nextSibling}}if("masternode"==
f.nodeName)for(e=f.firstChild;e;){"userdata"==e.nodeName&&(n=this.tg(e),this.ff._master=n);"animationsequences"==e.nodeName&&this.Ms(e);if("translations"==e.nodeName){(n=e.getAttributeNode("json"))&&this.rh(n.nodeValue.toString());let r=e.firstChild;for(;r;){"json"==r.nodeName&&this.rh(r.textContent);if("projecttranslation"==r.nodeName){let w={};if(n=r.getAttributeNode("langcode"))w.langCode=n.nodeValue;if(n=r.getAttributeNode("langname"))w.langName=n.nodeValue;this.Nm.push(w)}r=r.nextSibling}this.tm||
this.hn(window.navigator.language)}e=e.nextSibling}"customproperty"==f.nodeName&&this.Jo(f);f=f.nextSibling}!this.df.hasOwnProperty(this.Uc)&&(f=this.ye[this.Uc])&&(this.Uc=f);this.df.hasOwnProperty(this.Uc)||(this.Yb("Start node "+this.Uc+" not found!"),this.Uc=a);this.Qm(this.df[this.Uc],b,this.da[0].D)}else{this.sd=!1;this.Qm(a,b);if(e=a.getAttributeNode("hassounds"))this.Ag=1==e.nodeValue;this.bb.push("")}this.ef&&this.ja.hj();this.Ag&&(this.Xt(),this.wr());this.G("configloaded",{});this.G("changenode",
{});this.G("playerstatechanged",{})}Qm(a,b,e){e||(e=this.D);this.Ye(this.Va);this.Ym();this.Nc&&this.Nc.Vu();this.Uf();this.Wk=0;var f;let h="";(f=a.getAttributeNode("id"))&&(h=f.nodeValue.toString());let n={oldNodeId:this.ua,nodeId:h};var r,w=0,x=e.M;let y=a.firstChild;for(;y;){if("view"==y.nodeName){if(f=y.getAttributeNode("fovmode"))e.fov.mode=Number(f.nodeValue);f=y.getAttributeNode("pannorth");e.pan.Cm=1*(f?f.nodeValue:0);for(var z=y.firstChild;z;){"start"==z.nodeName&&(f=z.getAttributeNode("pan"),
e.pan.m=Number(f?f.nodeValue:0),e.pan.Ua=e.pan.m,f=z.getAttributeNode("tilt"),e.u.m=Number(f?f.nodeValue:0),e.u.Ua=e.u.m,f=z.getAttributeNode("roll"),e.Y.m=Number(f?f.nodeValue:0),e.Y.Ua=e.Y.m,f=z.getAttributeNode("fov"),e.fov.m=Number(f?f.nodeValue:70),e.fov.Ua=e.fov.m,f=z.getAttributeNode("projection"),e.Aq=Number(f?f.nodeValue:4),this.$a(this.Qh()));"min"==z.nodeName&&(f=z.getAttributeNode("pan"),e.pan.min=1*(f?f.nodeValue:0),f=z.getAttributeNode("tilt"),e.u.min=1*(f?f.nodeValue:-90),f=z.getAttributeNode("fov"),
e.fov.min=1*(f?f.nodeValue:5),1E-20>e.fov.min&&(e.fov.min=1E-20),f=z.getAttributeNode("fovpixel"),e.fov.ki=1*(f?f.nodeValue:0));if("max"==z.nodeName){f=z.getAttributeNode("pan");e.pan.max=1*(f?f.nodeValue:0);f=z.getAttributeNode("tilt");e.u.max=1*(f?f.nodeValue:90);f=z.getAttributeNode("fov");e.fov.max=1*(f?f.nodeValue:120);180<=e.fov.max&&(e.fov.max=179.9);if(f=z.getAttributeNode("fovstereographic"))e.fov.zm=1*f.nodeValue;if(f=z.getAttributeNode("fovfisheye"))e.fov.ym=1*f.nodeValue;if(f=z.getAttributeNode("scaletofit"))this.K.Uq=
1==f.nodeValue}if("flyin"==z.nodeName){if(f=z.getAttributeNode("projection"))e.Zc.rb=Number(f.nodeValue);if(f=z.getAttributeNode("pan"))e.Zc.pan=parseFloat(f.nodeValue);if(f=z.getAttributeNode("tilt"))e.Zc.u=parseFloat(f.nodeValue);if(f=z.getAttributeNode("fov"))e.Zc.fov=parseFloat(f.nodeValue)}z=z.nextSibling}}if("autorotate"==y.nodeName){if(f=y.getAttributeNode("speed"))this.A.speed=1*f.nodeValue;if(f=y.getAttributeNode("delay"))this.A.timeout=1*f.nodeValue;if(f=y.getAttributeNode("returntohorizon"))this.A.Ok=
1*f.nodeValue;if(f=y.getAttributeNode("horizonfromdefview"))this.A.Rp=1==f.nodeValue;if(f=y.getAttributeNode("nodedelay"))this.A.ik=1*f.nodeValue;if(f=y.getAttributeNode("noderandom"))this.A.uq=1==f.nodeValue;(f=y.getAttributeNode("nodefilter"))&&!this.A.tq&&(this.A.Hg=f.nodeValue);this.Gd&&(this.A.enabled=!0,this.A.gg=!0,this.A.active=!1);(f=y.getAttributeNode("startloaded"))&&this.Gd&&(this.A.Yg=1==f.nodeValue,this.A.Yg&&(this.A.active=!1));if(f=y.getAttributeNode("useanimation"))this.A.cg=1==f.nodeValue,
this.A.gd=this.A.cg;if(f=y.getAttributeNode("syncanimationwithvideo"))this.A.yn=1==f.nodeValue}if("animation"==y.nodeName){if(f=y.getAttributeNode("syncanimationwithvideo"))this.A.yn=1==f.nodeValue;if(f=y.getAttributeNode("useinautorotation"))this.A.cg=1==f.nodeValue;if(f=y.getAttributeNode("animsequence"))this.A.kj=f.nodeValue,this.Gd&&(this.A.Td=this.A.kj);if(f=y.getAttributeNode("clipsrandom"))this.A.uo=1==f.nodeValue;this.za=[];for(z=y.firstChild;z;){if("clip"==z.nodeName){this.S=new Tb;if(f=
z.getAttributeNode("animtitle"))this.S.jf=f.nodeValue.toString();if(f=z.getAttributeNode("cliptitle"))this.S.eb=f.nodeValue.toString();if(f=z.getAttributeNode("nodeid"))this.S.Nw=f.nodeValue.toString();if(f=z.getAttributeNode("length"))this.S.length=Number(f.nodeValue);if(f=z.getAttributeNode("animtype"))this.S.os=Number(f.nodeValue);if(f=z.getAttributeNode("nextcliptitle"))this.S.rq=f.nodeValue.toString();if(f=z.getAttributeNode("nextclipnodeid"))this.S.qq=f.nodeValue.toString();if(f=z.getAttributeNode("nextclipstartview"))this.S.tu=
f.nodeValue.toString();if(f=z.getAttributeNode("transitiontype"))this.S.aw=Number(f.nodeValue);var cb=z.firstChild;for(this.S.Ja=[];cb;){if("keyframe"==cb.nodeName){let db=new Ab;if(f=cb.getAttributeNode("time"))db.time=Number(f.nodeValue);if(f=cb.getAttributeNode("value"))db.value=Number(f.nodeValue);if(f=cb.getAttributeNode("valuestring"))db.Nn=f.nodeValue.toString();if(f=cb.getAttributeNode("transitiontime"))db.Kb=Number(f.nodeValue);f=cb.getAttributeNode("type");let gb=0;f&&(db.type=Number(f.nodeValue),
gb=Number(f.nodeValue));if(f=cb.getAttributeNode("property"))db.sb=Number(f.nodeValue);if(f=cb.getAttributeNode("additionaltrackid"))db.Ad=f.nodeValue.toString();if(f=cb.getAttributeNode("additionaltracktype"))db.Zn=Number(f.nodeValue);if(1==gb||2==gb){if(f=cb.getAttributeNode("bezierintime"))db.mf=Number(f.nodeValue);if(f=cb.getAttributeNode("bezierinvalue"))db.nf=Number(f.nodeValue);if(f=cb.getAttributeNode("bezierouttime"))db.pf=Number(f.nodeValue);if(f=cb.getAttributeNode("bezieroutvalue"))db.qf=
Number(f.nodeValue)}this.S.Ja.push(db)}cb=cb.nextSibling}this.za.push(this.S);"__VideoPano"==this.S.eb&&(this.Wk=this.S)}z=z.nextSibling}}"input"==y.nodeName&&(r||(r=y));if(r)for(z=0;6>z;z++)f=r.getAttributeNode("prev"+z+"url"),this.Bh[z]=f?String(f.nodeValue):"";"altinput"==y.nodeName&&(z=0,(f=y.getAttributeNode("screensize"))&&(z=1*f.nodeValue),0<z&&z<=this.np()&&z>w&&(w=z,r=y));if("control"==y.nodeName&&this.Gd){if(f=y.getAttributeNode("simulatemass"))this.qa.enabled=1==f.nodeValue;if(f=y.getAttributeNode("rubberband"))this.K.Tq=
1==f.nodeValue;if(f=y.getAttributeNode("zoomcentercursor"))this.K.re=1==f.nodeValue;if(f=y.getAttributeNode("locked"))this.K.qc=1==f.nodeValue;f&&(this.K.fi=1==f.nodeValue);if(f=y.getAttributeNode("lockedmouse"))this.K.qc=1==f.nodeValue;if(f=y.getAttributeNode("lockedkeyboard"))this.K.fi=1==f.nodeValue;if(f=y.getAttributeNode("lockedkeyboardzoom"))this.K.iu=1==f.nodeValue;if(f=y.getAttributeNode("lockedwheel"))this.K.Qe=1==f.nodeValue;if(f=y.getAttributeNode("invertwheel"))this.K.Wp=1==f.nodeValue;
if(f=y.getAttributeNode("speedwheel"))this.K.qn=1*f.nodeValue;if(f=y.getAttributeNode("invertcontrol"))this.K.mc=1==f.nodeValue;if(f=y.getAttributeNode("sensitivity"))this.K.sensitivity=1*f.nodeValue,1>this.K.sensitivity&&(this.K.sensitivity=1);if(f=y.getAttributeNode("dblclickfullscreen"))this.K.Hl=1==f.nodeValue;if(f=y.getAttributeNode("contextfullscreen"))this.K.yl=1==f.nodeValue;if(f=y.getAttributeNode("contextvr"))this.K.zl=1==f.nodeValue;if(f=y.getAttributeNode("contextprojections"))this.K.vo=
1==f.nodeValue;if(f=y.getAttributeNode("hideabout"))this.K.Kj=1==f.nodeValue;for(z=y.firstChild;z;)"menulink"==z.nodeName&&(cb={text:"",url:""},f=z.getAttributeNode("text"),cb.text=f.nodeValue,f=z.getAttributeNode("url"),cb.url=f.nodeValue,this.fk.push(cb)),z=z.nextSibling}if("vr"==y.nodeName&&this.Gd){if(f=y.getAttributeNode("toggleskinbutton"))this.ja.Io=1==f.nodeValue;if(f=y.getAttributeNode("skinbuttonposition"))this.ja.Fk=f.nodeValue;if(f=y.getAttributeNode("defaulthsscale"))this.ja.Jl=1*f.nodeValue/
100}if("transition"==y.nodeName&&this.Gd){if(f=y.getAttributeNode("enabled"))this.F.enabled=1==f.nodeValue;if(f=y.getAttributeNode("blendtime"))this.F.tf=1*f.nodeValue;if(f=y.getAttributeNode("blendcolor"))this.F.hg=f.nodeValue.toString();if(f=y.getAttributeNode("type"))this.F.type=f.nodeValue.toString();if(f=y.getAttributeNode("delay"))this.F.delay=f.nodeValue.toString();if(f=y.getAttributeNode("softedge"))this.F.dd=1*f.nodeValue;if(f=y.getAttributeNode("zoomin"))this.F.ea=1*f.nodeValue;if(f=y.getAttributeNode("zoomout"))this.F.Vc=
1*f.nodeValue;if(f=y.getAttributeNode("zoomfov"))this.F.fl=1*f.nodeValue;if(f=y.getAttributeNode("zoomafterinfov"))this.F.cl=1*f.nodeValue;if(f=y.getAttributeNode("zoomafteroutfov"))this.F.el=1*f.nodeValue;if(f=y.getAttributeNode("zoomspeed"))this.F.qh=1*f.nodeValue;if(f=y.getAttributeNode("zoomoutpause"))this.F.ph=1==f.nodeValue;"cut"==this.F.type&&(this.F.tf=0);"mesh"==this.F.type&&(this.F.Vc=0)}if("soundstransition"==y.nodeName){if(f=y.getAttributeNode("enabled"))this.ab.enabled=1==f.nodeValue;
if(f=y.getAttributeNode("transitiontime"))this.ab.Kb=1*f.nodeValue;if(f=y.getAttributeNode("crossfade"))this.ab.Bo=1==f.nodeValue}if("flyintransition"==y.nodeName){if(f=y.getAttributeNode("enabled"))this.Da.enabled=1==f.nodeValue&&this.pa;if(f=y.getAttributeNode("speed"))this.Da.speed=1*f.nodeValue}"userdata"==y.nodeName&&(this.userdata=this.dg=this.tg(y),this.ff.hasOwnProperty(h)||(this.ff[h]=this.dg),this.Oc[a.id]||(this.Oc[a.id]=this.Ko(y),this.Fg[a.id]=this.Lo(y),this.eg[a.id]=this.Mo(y)));"projectuserdata"==
y.nodeName&&(this.ff._master=this.tg(y));"customproperty"==y.nodeName&&this.Jo(y);if("translations"==y.nodeName)for((f=y.getAttributeNode("json"))&&this.rh(f.nodeValue.toString()),this.tm||this.hn(window.navigator.language),z=y.firstChild;z;){"json"==z.nodeName&&this.rh(z.textContent);if("projecttranslation"==z.nodeName){cb={};if(f=z.getAttributeNode("langcode"))cb.langCode=f.nodeValue;if(f=z.getAttributeNode("langname"))cb.langName=f.nodeValue;this.Nm.push(cb)}z=z.nextSibling}if("hotspots"==y.nodeName){if(f=
y.getAttributeNode("smoothmovement"))this.qr=1==f.nodeValue;z=y.firstChild;for(this.P.count=0;z;){if("label"==z.nodeName&&this.Gd){cb=this.P.Mk;if(f=z.getAttributeNode("enabled"))cb.enabled=1==f.nodeValue;if(f=z.getAttributeNode("width"))cb.width=1*f.nodeValue;if(f=z.getAttributeNode("height"))cb.height=1*f.nodeValue;if(f=z.getAttributeNode("textcolor"))cb.Nk=1*f.nodeValue;if(f=z.getAttributeNode("textalpha"))cb.Lk=1*f.nodeValue;if(f=z.getAttributeNode("background"))cb.background=1==f.nodeValue;if(f=
z.getAttributeNode("backgroundalpha"))cb.ec=1*f.nodeValue;if(f=z.getAttributeNode("backgroundcolor"))cb.fc=1*f.nodeValue;if(f=z.getAttributeNode("border"))cb.lj=1*f.nodeValue;if(f=z.getAttributeNode("bordercolor"))cb.ic=1*f.nodeValue;if(f=z.getAttributeNode("borderalpha"))cb.hc=1*f.nodeValue;if(f=z.getAttributeNode("borderradius"))cb.nl=1*f.nodeValue;if(f=z.getAttributeNode("wordwrap"))cb.gj=1==f.nodeValue}if("polystyle"==z.nodeName&&this.Gd){if(f=z.getAttributeNode("mode"))this.P.mode=1*f.nodeValue;
if(f=z.getAttributeNode("bordercolor"))this.P.ic=1*f.nodeValue;if(f=z.getAttributeNode("backgroundcolor"))this.P.fc=1*f.nodeValue;if(f=z.getAttributeNode("borderalpha"))this.P.hc=1*f.nodeValue;if(f=z.getAttributeNode("backgroundalpha"))this.P.ec=1*f.nodeValue;if(f=z.getAttributeNode("handcursor"))this.P.Ke=1==f.nodeValue}"hotspot"==z.nodeName&&(f=new Rb(this),f.type="point",f.wb(z),this.L.push(f),this.Nl(a.id,z));"polyhotspot"==z.nodeName&&(f=new Rb(this),f.type="poly",f.wb(z),this.L.push(f),this.P.count++,
this.Nl(a.id,z));z=z.nextSibling}}if("sounds"==y.nodeName||"media"==y.nodeName)for(f=y.firstChild;f;){if("sound"==f.nodeName&&!this.Am)for(z=new Bb(this),z.wb(f),z.addElement(),cb=0;cb<this.Yf.length;cb++)z.id==this.Yf[cb].id&&(this.Yf.splice(cb,1),cb--);"video"==f.nodeName&&(z=new Qb(this),z.wb(f),z.addElement());"image"==f.nodeName&&(z=new id(this),z.wb(f),z.addElement());"webelement"==f.nodeName&&(z=new jd(this),z.wb(f),z.addElement());"lensflare"==f.nodeName&&this.Nc&&(z=new kd(this),z.wb(f),
this.Nc.ei.push(z));f=f.nextSibling}y=y.nextSibling}this.A.Jd=0;for(a=0;a<this.Yf.length;a++){w=this.Yf[a];if(this.ga&&this.ab.enabled&&this.isPlaying(w.id))this.ab.$i.push(w);else{try{w.ta?w.ig():w.j.pause()}catch(db){}w.We()}this.X.splice(this.X.indexOf(w),1)}(1!=this.F.ea&&2!=this.F.ea&&6!=this.F.ea&&7!=this.F.ea||"mesh"==this.F.type)&&this.Oo();this.ef||(this.ob.Ci=!0);b&&("object"===typeof b&&null!==b?(b.hasOwnProperty("pan")&&this.Nd(Number(b.pan)),b.hasOwnProperty("tilt")&&this.Od(Number(b.tilt)),
b.hasOwnProperty("projection")&&this.$a(Number(b.projection)),b.hasOwnProperty("fov")&&this.Jb(Number(b.fov))):""!=b&&(b=b.toString().split("/"),4<b.length&&this.$a(Number(b[4])),0<b.length&&(f=String(b[0]),"N"==f.charAt(0)?this.Fi(Number(f.substring(1))):"S"==f.charAt(0)?this.Fi(-180+Number(f.substring(1))):this.Nd(Number(f))),1<b.length&&this.Od(Number(b[1])),2<b.length&&this.Jb(Number(b[2]))),e.pan.open=e.pan.m,this.Da.Fd.pan=e.pan.m,this.Da.Fd.u=e.u.m,this.Da.Fd.fov=e.fov.m,this.Da.Fd.rb=this.Z(),
this.Da.Fd.Xr=!0);if(r){f=r.getAttributeNode("stereo");this.pc=!1;f&&1==f.nodeValue&&(this.pc=!0);f=r.getAttributeNode("fliplr");this.Zf=!1;f&&1==f.nodeValue&&(this.Zf=!0);for(b=0;b<(this.pc?12:6);b++)(f=r.getAttributeNode("tile"+b+"url"))&&(this.Ah[b]=String(f.nodeValue));for(b=0;6>b;b++)(f=r.getAttributeNode("prev"+b+"url"))&&(this.Bh[b]=String(f.nodeValue));if(f=r.getAttributeNode("tilevrurl"))this.El=String(f.nodeValue);if(f=r.getAttributeNode("tilesize"))this.bf=1*f.nodeValue;f=r.getAttributeNode("canvassize");
if(f=r.getAttributeNode("tilescale"))this.dh=1*f.nodeValue;if(f=r.getAttributeNode("leveltileurl"))x.xm=f.nodeValue;if(f=r.getAttributeNode("leveltilesize"))x.W=Number(f.nodeValue);if(f=r.getAttributeNode("levelbias"))x.hq=Number(f.nodeValue);if(f=r.getAttributeNode("levelbiashidpi"))x.iq=Number(f.nodeValue);f=r.getAttributeNode("overlap");e.qb.Y=0;e.qb.pitch=0;f&&(x.ib=Number(f.nodeValue));if(f=r.getAttributeNode("levelingroll"))e.qb.Y=Number(f.nodeValue);if(f=r.getAttributeNode("levelingpitch"))e.qb.pitch=
Number(f.nodeValue);this.cd=0;(f=r.getAttributeNode("flat"))&&1==f.nodeValue&&(this.cd=2);e.cd=this.cd;f=r.getAttributeNode("width");x.width=1*(f?f.nodeValue:1);f=r.getAttributeNode("height");x.height=1*(f?f.nodeValue:x.width);this.B.src=[];x.levels=[];for(r=r.firstChild;r;){if("preview"==r.nodeName){if(f=r.getAttributeNode("color"))x.nk=f.nodeValue;if(f=r.getAttributeNode("strip"))x.Hq=1==f.nodeValue}if("video"==r.nodeName){this.B.format=1;this.pc=!1;if(f=r.getAttributeNode("format"))"3x2"==f.nodeValue&&
(this.B.format=14),"video3x2"==f.nodeValue&&(this.B.format=14),"equirectangular"==f.nodeValue&&(this.B.format=1),"vr180"==f.nodeValue&&(this.B.format=15),"vr180stereo"==f.nodeValue&&(this.B.format=15,this.pc=!0);if(f=r.getAttributeNode("flipy"))this.B.flipY=Number(f.nodeValue);if(f=r.getAttributeNode("startonload"))this.B.Ik=1==f.nodeValue;if(f=r.getAttributeNode("startmutedmobile"))this.B.Hk=1==f.nodeValue;if(f=r.getAttributeNode("level"))this.B.level=Number(f.nodeValue);if(f=r.getAttributeNode("bleed"))this.B.rf=
Number(f.nodeValue);if(f=r.getAttributeNode("endaction"))this.B.Ce=String(f.nodeValue);if(f=r.getAttributeNode("targetview"))this.B.Kk=String(f.nodeValue);if(f=r.getAttributeNode("width"))this.B.width=Number(f.nodeValue);if(f=r.getAttributeNode("height"))this.B.height=Number(f.nodeValue);for(b=r.firstChild;b;)"source"==b.nodeName&&(f=b.getAttributeNode("url"))&&this.B.src.push(f.nodeValue.toString()),b=b.nextSibling}if("level"==r.nodeName){b=new Vb;f=r.getAttributeNode("width");b.width=1*(f?f.nodeValue:
1);f=r.getAttributeNode("height");b.height=1*(f?f.nodeValue:b.width);if(f=r.getAttributeNode("preload"))b.cache=1==f.nodeValue;if(f=r.getAttributeNode("preview"))b.Mg=1==f.nodeValue;b.$=Math.floor((b.width+x.W-1)/x.W);b.sa=Math.floor((b.height+x.W-1)/x.W);x.levels.push(b)}r=r.nextSibling}x.wm=x.levels.length}this.ee=!0;this.kh&&(this.pa=this.jh=!1,this.Gc||(this.Gc=document.createElement("canvas"),this.Gc.width=100,this.Gc.height=100,this.Gc.id="dummycanvas",this.ia.appendChild(this.Gc)),this.fd());
this.pa&&this.N&&this.da.forEach(db=>{db.Tp(this.dh);db.Vp()});let ab=this;0<x.levels.length&&x.Hq&&0==this.cd&&(r=new Image,b=new Vb,b.Mg=!0,b.cache=!0,b.$=b.sa=0,b.height=b.width=0,x.levels.push(b),r.crossOrigin=this.crossOrigin,r.onload=e.Ig.qu(r),r.setAttribute("src",e.bh(6,x.wm-1,0,0)));0<this.B.src.length&&this.pa&&(this.dj?(x=this.B.j=document.createElement("video"),x.crossOrigin=this.crossOrigin,x.setAttribute("style","display:none; max-width:none;"),x.setAttribute("playsinline","playsinline"),
x.preload="metadata",x.volume=this.fa*this.B.level,this.T.appendChild(x),this.B.Mc=!1,this.B.Bn=!1,this.B.j.oncanplay=function(){if(!ab.B.Mc){ab.B.Eg=!0;let db,gb,bb,fb,ib,hb,mb=[],ob=new lb,jb=ab.N,sb=ab.B.j.videoWidth/3;ab.B.width=ab.B.j.videoWidth;ab.B.height=ab.B.j.videoHeight;for(db=0;6>db;db++)for(bb=db%3*sb+ab.B.rf,ib=bb+sb-2*ab.B.rf,hb=4,3>db&&(hb+=sb),fb=hb+sb-2*ab.B.rf,gb=0;4>gb;gb++){ob.x=-1;ob.y=-1;ob.z=1;for(let vb=0;vb<gb;vb++)ob.Sq();mb.push((0<ob.x?bb:ib)/(3*sb),(0<ob.y?fb:hb)/(2*
sb))}jb.bindBuffer(jb.ARRAY_BUFFER,ab.B.Wi);jb.bufferData(jb.ARRAY_BUFFER,new Float32Array(mb),jb.STATIC_DRAW);ab.F.ed&&"videopano"==ab.F.delay&&ab.Jk(null)}},this.B.gb=function(){let db=ab.Wa("_videopanorama");return 0<db.length&&db[0].gb?db[0].gb:0},this.B.cn=function(db){let gb=ab.Wa("_videopanorama");0<gb.length&&(gb[0].gb=db)},this.B.loop=function(){return 0<ab.B.gb()?(ab.B.cn(ab.B.gb()-1),ab.B.j.play(),!0):!1},"exit"==this.B.Ce?this.B.j.onended=function(){ab.B.Eg=!1;ab.B.Mc=!1;ab.T.removeChild(ab.B.j);
ab.B.j=null;ab.update()}:"stop"==this.B.Ce?ab.B.j.onended=function(){ab.B.loop()||ab.update()}:"{"==this.B.Ce.charAt(0)?this.B.j.onended=function(){ab.Rc(ab.B.Ce,ab.B.Kk)}:this.B.j.loop=!0,this.Yn(x,this.B.src),x=this.Wa("_videopanorama"),0<x.length?x[0].j=this.B.j:this.Jq("_videopanorama",this.B.j),this.B.Ik&&(x=!1,this.yc&&(this.B.Hk?this.B.j.muted=!0:x=!0),x||(x=this.B.j.play(),void 0!==x&&x.then(()=>{}).catch(()=>{this.B.Hk&&(this.B.j.muted=!0,this.B.j.play())})))):"{"==this.B.Ce.charAt(0)&&ab.Rc(ab.B.Ce,
ab.B.Kk));this.us(h);this.Xn();this.F.Qd||this.rn();this.update();this.Gd&&(this.Gd=!1,this.G("viewerinit",{}),this.aa&&this.aa.ggViewerInit&&this.aa.ggViewerInit(),this.Da.enabled&&0==this.cd&&this.pa&&(this.$a(9),e.pan.m=e.Zc.pan,e.u.m=e.Zc.u,e.fov.m=e.Zc.fov,this.$a(e.Zc.rb),this.S=this.Tl(!1),this.F.Ud=this.A.cg,e.pan.m=this.Wb(0,0).value,e.u.m=this.Wb(0,1).value,e.fov.m=this.Wb(0,2).value,e=this.Wb(0,3).value,3==e&&this.$a(e),this.wc=this.S.eb,this.zk(!0),this.A.active=!1,this.A.Jh=!0));this.fd();
this.G("changenodeid",n)}Np(){return Nb()&&Nb().hasOwnProperty("isSupported")}Yn(a,b,e){if(1<=b.length){var f=this.Ha(this.Ka(b[0]));if(this.Np()&&0<f.indexOf(".m3u8")&&!a.canPlayType("application/vnd.apple.mpegurl")&&Nb().isSupported())b=new (Nb()),b.attachMedia(a),b.loadSource(f),e&&e.cj.push(f);else for(f=0;f<b.length;f++){let h=this.Ha(this.Ka(b[f])),n=document.createElement("source");n.setAttribute("src",h);a.appendChild(n);e&&e.cj.push(h)}}}kk(a,b){0<a.length&&(".xml"==a.slice(-4)||".swf"==
a.slice(-4)||"{"==a.charAt(0)?this.Rc(this.Ha(a),b):window.open(this.Ha(a),b))}Nv(){this.ee=this.isLoaded=!1;this.checkLoaded=this.md=[];this.ri=0;this.aa&&this.aa.ggReLoaded&&this.aa.ggReLoaded();this.G("beforechangenode",{})}Ot(a){let b="";0<this.Bm.length&&(b=this.Bm.pop());""!=b&&(this.em=!0);this.Rc("{"+b+"}",a)}Rc(a,b){var e=this.D;if(""!=a&&"{}"!=a){this.Nv();this.Rl&&!this.kh&&(this.If(0),this.fd());this.ka&&this.ka.hotspotProxyOut&&this.ka.hotspotProxyOut(this.O.id,this.O.url);this.G("hsproxyout",
{id:this.O.id,url:this.O.url});".swf"==a.substring(a.length-4)&&(a=a.substring(0,a.length-4)+".xml");var f="",h=null;"object"===typeof b&&null!==b?h=b:b&&(f=b.toString());b="/"+e.u.m+"/"+e.fov.m+"//"+e.Z();f=f.replace("$cur",e.pan.m+b);f=f.replace("$(cur)",e.pan.m+b);f=f.replace("$fwd","N"+e.ce()+b);f=f.replace("$(fwd)","N"+e.ce()+b);f=f.replace("$bwd","S"+e.ce()+b);f=f.replace("$(bwd)","S"+e.ce()+b);f=f.replace("$ap",String(e.pan.m));f=f.replace("$(ap)",String(e.pan.m));f=f.replace("$an",String(e.ce()));
f=f.replace("$(an)",String(e.ce()));f=f.replace("$at",String(e.u.m));f=f.replace("$(at)",String(e.u.m));f=f.replace("$af",String(e.fov.m));f=f.replace("$(af)",String(e.fov.m));f=f.replace("$ar",String(e.Z()));f=f.replace("$(ar)",String(e.Z()));""!=f&&(e=f.split("/"),3<e.length&&""!=e[3]&&(this.startNode=e[3]));f=null!==h?h:f;this.ra(!1);if("{"==a.charAt(0)){h=a.substring(1,a.length-1);if(this.ua==h&&this.yh){this.ee=this.isLoaded=!0;return}(a=this.ye[h])&&(h=a);a=this.F;if(this.df[h]){this.yh=!0;
this.F.enabled&&this.pa&&(a.Mi=!0,"mesh"==a.type?(e=this.da[0],this.da[0]=this.da[1],this.da[1]=e,this.D=this.da[0].D):this.F.Pb&&a.setup());e=new Gb;e.pan=this.be();a.xd&&1<this.da.length&&this.da[1]&&this.da[1].D&&(e.pan=this.da[1].D.pan.m);let n=b=!0;7==this.F.ea&&(n=!1);6==this.F.ea&&(n=b=!1);if(this.O){"point"==this.O.type&&(b&&(e.pan=this.O.pan),n&&(e.u=this.O.u));if("poly"==this.O.type){let r=this.Hd();b&&(e.pan=-r.pan);n&&(e.u=r.tilt)}if(b=this.zf(this.qd(),this.O.id,"__mesh_pan"))e.pan=parseFloat(b);
if(b=this.zf(this.qd(),this.O.id,"__mesh_tilt"))e.u=parseFloat(b)}this.Qm(this.df[h],f);a.enabled&&this.pa&&(a.Su(),"mesh"==a.type&&(f=this.da[0].D,h=this.da[1].D,a.Xg.pan=f.pan.m,a.Xg.u=h.u.m,a.Xg.fov=h.fov.m,a.qg.pan=f.pan.m,a.qg.u=f.u.m,a.qg.fov=f.fov.m,f.fov.m=h.fov.m,f.u.m=h.u.m,h.$a(f.Z()),a.offset=h.pan.m-f.pan.m,a.Fv(e)));a.Mi=!1;this.ef&&this.ja.hj();this.F.Qd||this.F.xd||this.F.ed||(this.ha&&this.Bl(),this.yh=!1)}else{this.Yb("invalid node id: "+h);return}}else this.Iq(a,null,f);this.G("changenode",
{});this.update(5)}}sp(){if(this.ee){if(this.sd)return this.bb.slice(0);let a=[];a.push(this.ua);return a}return[]}tg(a){var b;let e;e={title:"",description:"",author:"",datetime:"",copyright:"",source:"",information:"",comment:"",latitude:0,longitude:0,altitude:-1E4,heading:0,customnodeid:"",streetviewlink:"",tags:[]};if(a&&((b=a.getAttributeNode("title"))&&(e.title=b.nodeValue.toString()),(b=a.getAttributeNode("description"))&&(e.description=b.nodeValue.toString()),(b=a.getAttributeNode("author"))&&
(e.author=b.nodeValue.toString()),(b=a.getAttributeNode("datetime"))&&(e.datetime=b.nodeValue.toString()),(b=a.getAttributeNode("copyright"))&&(e.copyright=b.nodeValue.toString()),(b=a.getAttributeNode("source"))&&(e.source=b.nodeValue.toString()),(b=a.getAttributeNode("info"))&&(e.information=b.nodeValue.toString()),(b=a.getAttributeNode("comment"))&&(e.comment=b.nodeValue.toString()),(b=a.getAttributeNode("latitude"))&&(e.latitude=Number(b.nodeValue)),(b=a.getAttributeNode("longitude"))&&(e.longitude=
Number(b.nodeValue)),(b=a.getAttributeNode("nodeid"))&&(e.nodeid=b.nodeValue.toString()),(b=a.getAttributeNode("altitude"))&&(e.altitude=Number(b.nodeValue)),(b=a.getAttributeNode("heading"))&&(e.heading=Number(b.nodeValue)),(b=a.getAttributeNode("customnodeid"))&&(e.customnodeid=b.nodeValue.toString()),(b=a.getAttributeNode("streetviewlink"))&&(e.streetviewlink=b.nodeValue.toString()),b=a.getAttributeNode("tags"))){a=b.nodeValue.toString().split("|");for(b=0;b<a.length;b++)""==a[b]&&(a.splice(b,
1),b--);e.tags=a}return e}Ms(a){let b="",e=a.firstChild;for(;e;){if("sequence"==e.nodeName){if(a=e.getAttributeNode("title"))b=a.nodeValue;let f=[],h=e.firstChild;for(;h;){if("sequenceelement"==h.nodeName){let n={};if(a=h.getAttributeNode("index"))n.index=a.nodeValue;if(a=h.getAttributeNode("nodeid"))n.nodeid=a.nodeValue;if(a=h.getAttributeNode("cliptitle"))n.cliptitle=a.nodeValue;if(a=h.getAttributeNode("startview"))n.startview=a.nodeValue;f.push(n)}h=h.nextSibling}this.co[b]=f}e=e.nextSibling}}Ko(a){let b=
{},e=a.firstChild;for(;e;){if("mapcoords"==e.nodeName){let f={x:0,y:0};a=e.getAttributeNode("x");f.x=Number(a.nodeValue);a=e.getAttributeNode("y");f.y=Number(a.nodeValue);a=e.getAttributeNode("mapid");b[a.nodeValue.toString()]=f}e=e.nextSibling}return b}Lo(a){let b={},e=a.firstChild;for(;e;){if("mapcoords"==e.nodeName){let f={x:0,y:0};a=e.getAttributeNode("x_floorplan_percent");f.x=Number(a.nodeValue);a=e.getAttributeNode("y_floorplan_percent");f.y=Number(a.nodeValue);a=e.getAttributeNode("mapid");
b[a.nodeValue.toString()]=f}e=e.nextSibling}return b}Ph(a){return a?this.ff[a]?this.ff[a]:this.tg():this.dg}st(a,b){""!==a||this.sd||(a="node1");a||(a=this.ua);return this.Ep(a,b)}jv(a,b,e){""!==a||this.sd||(a="node1");a||(a=this.ua);this.fr(a,b,e)}$l(a){a=this.Ph(a);let b=[];""!=a.latitude&&0!=a.latitude&&0!=a.longitude&&(b.push(a.latitude),b.push(a.longitude));return b}tp(a){let b=[];for(let e=0;e<this.bb.length;e++){let f=this.bb[e],h=this.Ph(f);h&&h.tags.includes(a)&&b.push(f)}return b}rt(a){return this.Ph(a).title}nt(a,
b){var e=-1;a=this.$l(a);b=this.$l(b);if(2==a.length&&2==b.length){e=Math.PI/180*(b[0]-a[0]);let f=Math.PI/180*(b[1]-a[1]);a=Math.sin(e/2)*Math.sin(e/2)+Math.sin(f/2)*Math.sin(f/2)*Math.cos(Math.PI/180*a[0])*Math.cos(Math.PI/180*b[0]);e=12742E3*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))}return e}Wb(a,b,e){let f;for(f=0;f<this.S.Ja.length;f++){let h=this.S.Ja[f];if(h.time==a&&h.sb==b&&(!e||h.Ad==e))return h}return!1}Cj(a,b){let e;for(e=0;e<b.Ja.length;e++)if(0==b.Ja[e].time&&b.Ja[e].sb==a)return b.Ja[e];
return!1}yp(a,b,e){for(a=Math.floor(a);!this.Wb(a,b,e)&&0<a;)a--;return a}qp(a){let b,e=1E5,f=a,h=!1;for(b=0;b<this.S.Ja.length;b++){let n=this.S.Ja[b];n.sb==a.sb&&n.Ad==a.Ad&&n.time>a.time&&n.time<e&&(f=this.S.Ja[b],e=f.time,h=!0)}return h?f:!1}Ss(a){let b=[];for(let e=0;e<this.S.Ja.length;e++){let f=this.S.Ja[e];if(f.time<=a&&4==f.sb){let h=!1;for(let n=0;n<b.length;n++)if(b[n].Ad==this.S.Ja[e].Ad){b[n].time<f.time?b.splice(n,1):h=!0;break}h||b.push(f)}}return b}$o(a,b,e){var f=new tb(a.time,a.value);
let h=new tb(b.time,b.value);var n=(e-a.time)/(b.time-a.time);if(0!=a.type||0!=b.type&&3!=b.type)if(3==a.type)f=a.value;else{n=new tb;let r=new tb,w=b.time-a.time;0==a.type?r.init(a.time+.3*w,a.value):r.init(a.pf,a.qf);0==b.type||3==b.type?n.init(b.time-.3*w,b.value):n.init(b.mf,b.nf);a=new tb;a.ml(f,h,r,n,e);f=a.y}else e=new tb,e.ge(f,h,n),f=e.y;return f}Tl(a,b,e,f){const h=this.D;for(var n=0;n<this.za.length;n++)if(this.za[n].eb&&0==this.za[n].eb.indexOf("__FlyIn"))return this.za[n];n=new Ub;n.eb=
"__FlyIn";n.af=h.pan.m;n.Ri=h.u.m;n.me=h.fov.m;n.Qi=this.Z();n.ne=this.Qh();a?(n.og=!1,n.vf=!1,n.speed=this.F.qh,n.Pd=b,n.oe=e,n.ah=f):(n.og=!0,n.vf=!0,n.speed=this.Da.speed,this.Da.Fd.Xr?(n.Pd=this.Da.Fd.pan,n.oe=this.Da.Fd.u,n.ah=this.Da.Fd.fov,n.ne=this.Da.Fd.rb):(n.Pd=h.pan.Ua,n.oe=h.u.Ua,n.ah=h.fov.Ua));return this.Xo(n)}Xo(a){var b=this.D;let e=new Tb;e.eb=a.eb;e.jf="";e.Ja=[];let f=a.Qi!=a.ne&&-1!=a.ne;for(;-180>a.Pd;)a.Pd+=360;for(;180<a.Pd;)a.Pd-=360;let h=a.Pd-a.af;if(360==b.pan.max-b.pan.min&&
!this.Ia()){for(;-180>h;)h+=360;for(;180<h;)h-=360}b=a.oe-a.Ri;let n=a.ah-a.me,r=Math.round(Math.sqrt(h*h+b*b+n*n)/a.speed*.33);f&&(r=Math.max(10,r));e.length=r;let w,x;a.wf&&(w=Math.ceil(.7*r),w=Math.min(15,w),w=Math.max(5,w),e.length=r+w,x=.33*w);let y=a.ah,z=r,cb=0,ab=r-1;if(f){var db=a.me;var gb=4==a.ne?120:this.Nh(a.ne);n=y-a.me;var bb=new tb(0,a.me);let fb=new tb(r,y),ib=new tb,hb=new tb;hb.init(r/3,a.me+n/3);ib.init(2*r/3,y-n/3);if(db>gb)for(;cb<=r&&db>gb;)db=new tb,db.ml(bb,fb,hb,ib,cb),db=
db.y,cb++;else cb=1;cb>=.8*r&&(z=cb=Math.round(.8*r));0==cb&&(cb=1);gb=4==a.Qi?120:this.Nh(a.Qi);db=a.ah;if(db>gb)for(;ab>cb&&db>gb;)db=new tb,db.ml(bb,fb,hb,ib,ab),db=db.y,ab--}bb=new Ab;bb.time=0;bb.sb=0;bb.value=a.af;bb.type=1;bb.pf=r/3;bb.qf=a.og?a.af:a.af+h/3;e.Ja.push(bb);bb=new Ab;bb.time=0;bb.sb=1;bb.value=a.Ri;bb.type=1;bb.pf=r/3;bb.qf=a.og?a.Ri:a.Ri+b/3;e.Ja.push(bb);bb=new Ab;bb.time=0;bb.sb=2;bb.value=a.me;bb.type=1;bb.pf=r/3;bb.qf=a.og?a.me:a.me+n/3;e.Ja.push(bb);bb=new Ab;bb.time=0;
bb.sb=3;bb.value=a.Qi;bb.type=0;bb.Kb=0;e.Ja.push(bb);f&&(bb=new Ab,bb.time=cb,bb.sb=3,bb.value=a.ne,bb.type=0,bb.Kb=ab-cb,e.Ja.push(bb));bb=new Ab;bb.time=r;bb.sb=0;bb.value=a.af+h;bb.type=1;bb.mf=2*r/3;bb.nf=a.vf&&!a.wf?bb.value:bb.value-h/3;a.wf&&(bb.pf=r+x,bb.qf=bb.value+x/r*h);e.Ja.push(bb);bb=new Ab;bb.time=r;bb.sb=1;bb.value=a.oe;bb.type=1;bb.mf=2*r/3;bb.nf=a.vf&&!a.wf?a.oe:a.oe-b/3;a.wf&&(bb.pf=r+x,bb.qf=bb.value+x/r*b);e.Ja.push(bb);bb=new Ab;bb.time=z;bb.sb=2;bb.value=y;bb.type=1;bb.mf=
2*z/3;bb.nf=a.vf?y:y-n/3;e.Ja.push(bb);a.wf&&(bb=new Ab,bb.time=r+w,bb.sb=0,bb.value=a.af+h,bb.type=1,bb.mf=r+w-x,bb.nf=a.af+h,e.Ja.push(bb),bb=new Ab,bb.time=r+w,bb.sb=1,bb.value=a.oe,bb.type=1,bb.mf=r+w-x,bb.nf=a.oe,e.Ja.push(bb));this.za.push(e);return e}nw(){this.B.j&&this.B.j.play()}ow(){this.B.j&&(this.B.j.pause(),this.B.j.currentTime=0)}mw(){this.B.j&&this.B.j.pause()}Cv(a){this.B.j&&(0>a&&(a=0),a>this.B.j.duration&&(a=this.B.j.duration-.1),this.B.j.currentTime=a,this.update())}Jt(){return this.B.j?
this.B.j.currentTime:0}It(){if(this.B.j)return this.B.j}Bv(a){const b=this.B.j;if(b){let e=!this.B.j.paused&&!this.B.j.ended,f=this.B.j.currentTime;b.pause();b.src=isNaN(parseInt(a,10))?String(a):this.B.src[parseInt(a,10)];e&&(this.B.j.onloadedmetadata=function(){b.currentTime=f;b.play();b.onloadedmetadata=null});this.B.j.currentTime=f}}Go(){this.Am=!0}zf(a,b,e){""!==a||this.sd||(a="node1");a||(a=this.ua);return super.zf(a,b,e)}Ei(a,b,e,f){""!==a||this.sd||(a="node1");a||(a=this.ua);return super.Ei(a,
b,e,f)}wp(){let a={};var b=this.D;a.currentNode=this.qd();a.position={pan:b.pan.m,tilt:b.u.m,fov:b.fov.m,projection:this.Z()};b=a.variables={};for(var e of Object.keys(this.yb))this.yb[e].Sp||"undefined"!==this.bm(e)&&((b[e]={}).value=this.bm(e));e=a.hsprops={};for(var f of Object.keys(this.pb)){b=e[f]={};for(var h of Object.keys(this.pb[f])){let r=b[h]={};for(var n of Object.keys(this.pb[f][h]))r[n]=this.zf(f,h,n)}}f=a.polyhotspots=[];for(h=0;h<this.L.length;h++)"poly"==this.L[h].type&&(n=this.L[h],
e={},e.id=n.id,e.bordercolor=n.ic,e.borderalpha=n.hc,e.backgroundcolor=n.fc,e.backgroundalpha=n.ec,f.push(e));return a}er(a){var b=this.D;if(a.hasOwnProperty("position")){var e=a.position;b.pan.m=e.pan;b.u.m=e.tilt;b.fov.m=e.fov;this.$a(e.projection)}for(const r of Object.keys(a.variables))this.Ze(r,a.variables[r].value);for(var f of Object.keys(a.hsprops)){b=a.hsprops[f];for(var h of Object.keys(b)){e=b[h];for(var n of Object.keys(e))this.Ei(f,h,n,e[n])}}for(f=0;f<a.polyhotspots.length;f++)for(n=
a.polyhotspots[f],h=0;h<this.L.length;h++)"poly"==this.L[h].type&&this.L[h].id==n.id&&(this.L[h].ic=n.bordercolor,this.L[h].hc=n.borderalpha,this.L[h].ec=n.backgroundalpha,this.L[h].fc=n.backgroundcolor);return this.Ma=!0}}window.ggHasHtml5Css3D=pb;window.ggHasWebGL=qb;window.THREE=eb;window.pano2vrPlayer=Eb;let t=Eb.prototype;t._=t.Ka;t.setContainer=t.Xq;t.setLanguage=t.hn;t.getLanguage=t.ft;t.addTranslations=t.rh;t.getProjectTranslations=t.yt;t.getVersion=t.cm;t.readConfigString=t.Rm;t.readConfigUrl=
t.Iq;t.readConfigUrlAsync=t.Uu;t.readConfigXml=t.Sm;t.openUrl=t.kk;t.openNext=t.Rc;t.goBack=t.Ot;t.setMargins=t.hv;t.addListener=t.addListener;t.on=t.addListener;t.removeEventListener=t.removeEventListener;t.off=t.removeEventListener;t.detectBrowser=t.Fo;t.initWebGL=t.If;t.getPercentLoaded=t.vt;t.setBasePath=t.cv;t.getBasePath=t.bp;t.setViewerSize=t.Li;t.getViewerSize=t.Lt;t.updateViewerSizeNow=t.fd;t.setSkinObject=t.sv;t.changeViewMode=t.po;t.getViewMode=t.Gp;t.changePolygonMode=t.oo;t.setPolygonMode=
t.oo;t.getPolygonMode=t.xp;t.showOnePolyHotspot=t.mr;t.hideOnePolyHotspot=t.Pp;t.changePolyHotspotColor=t.ys;t.setPolyHotspotHandcursor=t.lv;t.toggleOnePolyHotspot=t.Uv;t.changeViewState=t.qo;t.getViewState=t.Hp;t.setRenderFlags=t.nv;t.getRenderFlags=t.zt;t.setMaxTileCount=t.dr;t.updatePanorama=t.bj;t.isTouching=t.rm;t.getIsMobile=t.Yl;t.setIsMobile=t.gv;t.getHasTouch=t.hp;t.getIsTour=t.et;t.getNodesCount=t.tt;t.getIsAutorotating=t.ct;t.getIsLoading=t.kp;t.getIsLoaded=t.Oh;t.getIsTileLoading=t.Zl;
t.getLastActivity=t.gt;t.getPan=t.be;t.getPanNorth=t.ce;t.getPanDest=t.vp;t.getPanN=t.Dj;t.setPan=t.Nd;t.setPanNorth=t.Fi;t.changePan=t.wh;t.changePanLog=t.sl;t.getTilt=t.Bf;t.getTiltDest=t.Fp;t.setTilt=t.Od;t.changeTilt=t.xh;t.changeTiltLog=t.ul;t.getFov=t.rd;t.getFovDest=t.Ys;t.setFov=t.Jb;t.setFovMode=t.en;t.changeFov=t.vh;t.changeFovLog=t.mj;t.getVFov=t.Eb;t.setVFov=t.Ji;t.getHFov=t.Wl;t.setHFov=t.gn;t.getDFov=t.Vl;t.setDFov=t.dn;t.getRoll=t.Gj;t.setRoll=t.Tg;t.setPanTilt=t.Dk;t.setPanTiltFov=
t.Sg;t.setDefaultView=t.xk;t.getViewerLimits=t.Kt;t.setViewerLimits=t.Dv;t.setLocked=t.setLocked;t.setLockedMouse=t.ar;t.setLockedKeyboard=t.$q;t.getLockedKeyboard=t.it;t.setLockedWheel=t.cr;t.moveTo=t.moveTo;t.moveToEx=t.mi;t.moveToDefaultView=t.nq;t.moveToDefaultViewEx=t.oq;t.addHotspotElements=t.Xn;t.playSound=t.Md;t.playPauseSound=t.Jm;t.playStopSound=t.Fq;t.pauseSound=t.Jg;t.activateSound=t.Un;t.soundGetTime=t.sr;t.soundSetTime=t.ur;t.soundSetPlaybackRate=t.tr;t.setMediaVisibility=t.Bk;t.isPlaying=
t.isPlaying;t.stopSound=t.Ti;t.setVolume=t.setVolume;t.changeVolume=t.ro;t.mute=t.ru;t.unmute=t.bw;t.toggleMuted=t.Tv;t.getPlayerMuted=t.wt;t.getHasSounds=t.$s;t.getSoundsPermitted=t.At;t.startAutoplayMedia=t.zr;t.removeHotspots=t.Ym;t.getHotspotsVisible=t.Xl;t.getHotspotPropValue=t.zf;t.setHotspotPropValue=t.Ei;t.getCustomPropertyDefaultValue=t.Ws;t.getCurrentPerspective=t.ae;t.addHotspot=t.Wn;t.updateHotspot=t.Rr;t.removeHotspot=t.Lq;t.setActiveHotspot=t.Ye;t.getPointHotspotIds=t.xt;t.getHotspot=
t.ip;t.setFullscreen=t.Di;t.toggleFullscreen=t.Pk;t.enterFullscreen=t.Ls;t.exitFullscreen=t.exitFullscreen;t.getIsFullscreen=t.dt;t.startAutorotate=t.Ar;t.stopAutorotate=t.Dr;t.toggleAutorotate=t.Ir;t.pauseAutorotate=t.Dq;t.resumeAutorotate=t.Qq;t.setAutorotateNodeFilter=t.Vq;t.startAnimation=t.vr;t.resumeAnimation=t.Yu;t.createLayers=t.Cl;t.removePanorama=t.Uf;t.getScreenResolution=t.Cp;t.getMaxScreenResolution=t.np;t.getNodeIds=t.sp;t.getNodeUserdata=t.Ph;t.getNodeUserdataPropValue=t.st;t.setNodeUserdataPropValue=
t.jv;t.getNodeLatLng=t.$l;t.getNodeTitle=t.rt;t.getNodeDistance=t.nt;t.getCurrentNode=t.qd;t.getNextNode=t.rp;t.getPrevNode=t.zp;t.getLastVisitedNode=t.ht;t.getStartNode=t.Bt;t.getNodesWithTag=t.tp;t.getCurrentPointHotspots=t.ep;t.getPositionAngles=t.Hd;t.getPositionRawAngles=t.Ej;t.nodeVisited=t.uu;t.clearVisitedNodes=t.As;t.setElementIdPrefix=t.dv;t.videoPanoPlay=t.nw;t.videoPanoStop=t.ow;t.videoPanoPause=t.mw;t.getVideoPanoTime=t.Jt;t.setVideoPanoTime=t.Cv;t.getVideoPanoObject=t.It;t.setVideoPanoSource=
t.Bv;t.getMediaObject=t.pp;t.getMediaBufferSourceObject=t.op;t.registerVideoElement=t.Jq;t.disableSoundLoading=t.Go;t.setCrossOrigin=t.setCrossOrigin;t.setProjection=t.$a;t.getProjection=t.Z;t.changeProjection=t.tl;t.changeProjectionEx=t.tl;t.changeLensflares=t.xs;t.setTransition=t.vv;t.getMapType=t.lt;t.getMapDetails=t.kt;t.getNodeMapCoords=t.pt;t.getNodeMapCoordsInPercent=t.qt;t.getMapContainingNode=t.jt;t.getMapsContainingNode=t.mp;t.getMapIDs=t.lp;t.getFloorplanIDs=t.Xs;t.getFirstMap=t.gp;t.getFirstFloorplan=
t.Bj;t.hasMap=t.Ut;t.hasFloorplan=t.Tt;t.addVariable=t.ms;t.setVariableOptions=t.jr;t.setVariableValue=t.Ze;t.getVariableValue=t.bm;t.setSuperCookie=t.tv;t.getGyroAvailable=t.Zs;t.setUseGyro=t.gr;t.getUseGyro=t.Dt;t.getOS=t.ut;t.getBrowser=t.Us;t.getBrowserTheme=t.Vs;t.triggerEvent=t.G;t.requestRedraw=t.Wu;t.getWebGlContext=t.Mt;t.getHasTouch=t.hp;t.updateViewerSizeNow=t.fd;t.getApiVersion=t.Ts;t.setApiVersion=t.bv;t.getInertia=t.bt;t.setInertia=t.fv;t.getZoomCenterCursor=t.Ip;t.setZoomCenterCursor=
t.kr;t.getPlayerState=t.wp;t.setPlayerState=t.er;t.getQueryParameter=t.getQueryParameter;t.setQueryParameter=t.mv;t.enterVR=t.rg;t.exitVR=t.sg;t.toggleVR=t.Jr;t.isInVR=t.Ne;t.hasVR=t.Bg;t.getVRDisplayName=t.Ft;t.getVRCamera=t.Et;t.getVRRenderer=t.Gt;t.setVRFrameBufferScaleFactor=t.xv;t.getSkinGroup=t.Hj;t.setVRShowSkinButton=t.zv;t.setVRHideSkinButton=t.yv;t.setVRSkinVisibility=t.ir;t.setVRColorSpace=t.wv;t.getVRTextureColorSpace=t.Ht})()})();
