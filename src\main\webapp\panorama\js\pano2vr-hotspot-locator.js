/**
 * Pano2VR热点定位与节点切换监听脚本
 *
 * 此脚本用于：
 * 1. 接收来自父页面的定位指令并调用pano2vr的JavaScript API实现热点定位功能
 * 2. 监听全景图节点切换事件，支持多节点全景图功能
 *
 * <AUTHOR>
 * @date 2025-06-06
 * @version 2.0 - 增加多节点支持
 */

(function () {
    'use strict';

    // 全局变量
    var currentNodeId = null; // 当前节点ID
    var isNodeSwitchListenerInitialized = false; // 节点切换监听器是否已初始化
    var isInteractiveHotspotEnabled = false; // 交互式热点添加是否启用
    var isHotspotClickListenerInitialized = false; // 全景图点击监听器是否已初始化

    // 等待pano2vr加载完成
    var waitForPano = function (callback) {
        var checkInterval = setInterval(function () {
            // 检查pano对象是否存在且已初始化
            if (typeof pano !== 'undefined' && pano && typeof pano.moveTo === 'function') {
                clearInterval(checkInterval);
                callback();
            }
        }, 100); // 每100ms检查一次

        // 10秒超时
        setTimeout(function () {
            clearInterval(checkInterval);
        }, 10000);
    };

    // 获取当前节点ID
    var getCurrentNodeId = function () {
        try {
            // 尝试多种方法获取当前节点ID
            if (typeof pano !== 'undefined' && pano) {
                // 方法1: 直接获取当前节点ID（如果Pano2VR提供此API）
                if (typeof pano.getCurrentNode === 'function') {
                    return pano.getCurrentNode();
                }

                // 方法2: 通过URL参数获取
                if (typeof pano.getURL === 'function') {
                    var url = pano.getURL();
                    var match = url.match(/node=([^&]+)/);
                    if (match) {
                        return match[1];
                    }
                }

                // 方法3: 通过变量获取（如果Pano2VR设置了全局变量）
                if (typeof window.currentNode !== 'undefined') {
                    return window.currentNode;
                }

                // 方法4: 检查DOM中的节点信息
                var nodeElements = document.querySelectorAll('[data-node-id]');
                if (nodeElements.length > 0) {
                    return nodeElements[0].getAttribute('data-node-id');
                }
            }

            // 默认返回node1
            return 'node1';
        } catch (error) {
            return 'node1';
        }
    };

    // 初始化节点切换监听器
    var initNodeSwitchListener = function () {
        if (isNodeSwitchListenerInitialized) {
            return; // 避免重复初始化
        }

        try {
            // 获取初始节点ID
            currentNodeId = getCurrentNodeId();

            // 向父页面发送初始节点信息
            notifyParentNodeSwitch(currentNodeId, 'initial');

            // 方法1: 监听Pano2VR的节点切换事件（如果提供）
            if (typeof pano !== 'undefined' && pano && typeof pano.addEventListener === 'function') {
                pano.addEventListener('nodechange', function (event) {
                    handleNodeSwitch(event.nodeId || getCurrentNodeId());
                });
            }

            // 方法2: 定期检查节点变化（轮询方式）
            var lastNodeId = currentNodeId;

            // 方法3: 监听URL变化（如果节点切换会改变URL）
            var lastUrl = window.location.href;

            isNodeSwitchListenerInitialized = true;

        } catch (error) {
            // 初始化节点切换监听器失败
        }
    };

    // 初始化热点点击监听器
    var initHotspotClickListener = function () {
        try {
            // 监听Pano2VR的节点切换事件，在每次节点切换时重新绑定热点点击事件
            if (typeof pano !== 'undefined' && pano && typeof pano.addListener === 'function') {
                pano.addListener('changenode', function () {
                    // 延迟一下确保热点已经加载完成
                    setTimeout(function () {
                        bindHotspotClickEvents();
                    }, 500);
                });
            }

            // 初始绑定热点点击事件
            setTimeout(function () {
                bindHotspotClickEvents();
            }, 1000);

        } catch (error) {
            // 初始化热点点击监听器失败
        }
    };

    // 绑定热点点击事件
    var bindHotspotClickEvents = function () {
        try {
            // 获取当前页面的所有热点
            if (typeof pano !== 'undefined' && pano && pano.P) {
                var hotspots = pano.P;

                for (var i = 0; i < hotspots.length; i++) {
                    var hotspot = hotspots[i];

                    // 获取热点的DOM元素
                    if (hotspot.b && hotspot.b.__div) {
                        var divElement = hotspot.b.__div;

                        // 移除之前的点击事件监听器（避免重复绑定）
                        divElement.removeEventListener('click', hotspotClickHandler);

                        // 将热点信息存储到DOM元素的data属性中
                        divElement.hotspotData = {
                            id: hotspot.id || '',
                            title: hotspot.title || '',
                            description: hotspot.description || '',
                            skinid: hotspot.skinid || '',
                            url: hotspot.url || '',
                            target: hotspot.target || '',
                            pan: hotspot.pan || '',
                            tilt: hotspot.tilt || ''
                        };

                        // 绑定点击事件
                        divElement.addEventListener('click', hotspotClickHandler);

                        // 绑定右键菜单事件（用于删除热点）
                        divElement.addEventListener('contextmenu', hotspotContextMenuHandler);
                    }
                }
            }
        } catch (error) {
            // 绑定热点点击事件失败
        }
    };

    // 热点点击事件处理器
    var hotspotClickHandler = function (event) {
        try {
            // 阻止事件冒泡，避免触发其他点击事件
            event.stopPropagation();

            // 获取热点数据
            var hotspotData = this.hotspotData;
            if (!hotspotData) {
                return;
            }

            // 向父页面发送热点点击消息
            var message = {
                type: 'hotspotClick',
                hotspot: hotspotData,
                nodeId: currentNodeId,
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');

        } catch (error) {
            // 热点点击处理失败
        }
    };

    // 热点右键菜单事件处理器
    var hotspotContextMenuHandler = function (event) {
        try {
            // 阻止默认右键菜单
            event.preventDefault();
            event.stopPropagation();

            // 检查是否启用交互式热点管理
            if (!isInteractiveHotspotEnabled) {
                return;
            }

            // 获取热点数据
            var hotspotData = this.hotspotData;
            if (!hotspotData || !hotspotData.id) {
                return;
            }

            // 向父页面发送删除热点请求
            var message = {
                type: 'deleteHotspotRequest',
                hotspot: hotspotData,
                nodeId: currentNodeId,
                screenPosition: {
                    x: event.clientX,
                    y: event.clientY
                },
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');

        } catch (error) {
            console.error('Error handling hotspot context menu:', error);
        }
    };

    // 处理节点切换
    var handleNodeSwitch = function (newNodeId) {
        if (newNodeId && newNodeId !== currentNodeId) {
            currentNodeId = newNodeId;
            notifyParentNodeSwitch(newNodeId, 'switch');
        }
    };

    // 向父页面发送节点切换消息
    var notifyParentNodeSwitch = function (nodeId, type) {
        try {
            var message = {
                type: 'nodeSwitch',
                nodeId: nodeId,
                switchType: type, // 'initial' 或 'switch'
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');
        } catch (error) {
            // 发送节点切换消息失败
        }
    };

    // 初始化消息监听器
    var initMessageListener = function () {
        window.addEventListener('message', function (event) {
            // 安全检查：验证消息来源（可根据需要调整）
            // if (event.origin !== window.location.origin) return;

            var data = event.data;
            if (!data || typeof data !== 'object') return;

            // 处理热点定位消息
            if (data.type === 'locateHotspot') {
                handleHotspotLocation(data);
            }
            // 处理热点更新消息
            else if (data.type === 'updateHotspot') {
                handleHotspotUpdate(data);
            }
            // 处理启用/禁用交互式热点添加
            else if (data.type === 'toggleInteractiveHotspot') {
                handleToggleInteractiveHotspot(data);
            }
            // 处理热点删除请求
            else if (data.type === 'deleteHotspotRequest') {
                handleDeleteHotspotRequest(data);
            }
            // 处理动态添加热点到预览
            else if (data.type === 'addHotspotToPreview') {
                handleAddHotspotToPreview(data);
            }
        }, false);
    };

    // 处理热点定位
    var handleHotspotLocation = function (data) {
        try {
            var pan = parseFloat(data.pan);
            var tilt = parseFloat(data.tilt);
            var speed = parseFloat(data.speed) || 2.0; // 默认速度

            // 验证数据
            if (isNaN(pan) || isNaN(tilt)) {
                return;
            }

            // 获取当前FOV，保持不变
            var currentFov = pano.getFov();

            // 使用moveTo方法实现平滑过渡
            // moveTo(pan, tilt, fov, speed, roll, projection)
            pano.moveTo(pan, tilt, currentFov, speed);

            // 向父页面发送定位完成消息
            setTimeout(function () {
                try {
                    window.parent.postMessage({
                        type: 'hotspotLocationComplete',
                        pan: pan,
                        tilt: tilt,
                        success: true
                    }, '*');
                } catch (e) {
                    // 静默处理错误
                }
            }, speed * 1000 + 100); // 等待动画完成后发送消息

        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotLocationComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 处理热点更新
    var handleHotspotUpdate = function (data) {
        try {
            if (!data || !data.hotspot) {
                return;
            }

            var updateInfo = data.hotspot;
            var pan = updateInfo.pan;
            var tilt = updateInfo.tilt;
            var newTitle = updateInfo.title;
            var newDescription = updateInfo.description;

            // 获取当前页面的所有热点
            if (typeof pano !== 'undefined' && pano && pano.P) {
                var hotspots = pano.P;
                var updated = false;

                for (var i = 0; i < hotspots.length; i++) {
                    var hotspot = hotspots[i];

                    // 通过坐标匹配热点（最可靠的方式）
                    if (hotspot.pan && hotspot.tilt &&
                        Math.abs(parseFloat(hotspot.pan) - parseFloat(pan)) < 0.01 &&
                        Math.abs(parseFloat(hotspot.tilt) - parseFloat(tilt)) < 0.01) {

                        // 更新热点属性
                        if (newTitle !== undefined && newTitle !== null) {
                            hotspot.title = newTitle;
                        }
                        if (newDescription !== undefined && newDescription !== null) {
                            hotspot.description = newDescription;
                        }

                        // 更新DOM元素中的显示文本
                        if (hotspot.b && hotspot.b.__div) {
                            var divElement = hotspot.b.__div;

                            // 更新存储的数据
                            if (divElement.hotspotData) {
                                if (newTitle !== undefined && newTitle !== null) {
                                    divElement.hotspotData.title = newTitle;
                                }
                                if (newDescription !== undefined && newDescription !== null) {
                                    divElement.hotspotData.description = newDescription;
                                }
                            }

                            // 更新DOM中的文本显示并重新居中
                            var textElement = divElement.querySelector('.ggskin.ggskin_text > div');
                            if (textElement && newTitle !== undefined && newTitle !== null) {
                                textElement.textContent = newTitle;

                                // 重新计算文本居中位置
                                adjustHotspotTextPosition(divElement, textElement);
                            }
                        }

                        updated = true;
                        break; // 找到匹配的热点后退出循环
                    }
                }

                // 向父页面发送更新完成消息
                var message = {
                    type: 'hotspotUpdateComplete',
                    success: updated,
                    pan: pan,
                    tilt: tilt,
                    timestamp: new Date().getTime()
                };

                window.parent.postMessage(message, '*');

            }
        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotUpdateComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 调整热点文本位置，确保居中显示
    var adjustHotspotTextPosition = function (hotspotDiv, textElement) {
        try {
            // 等待DOM更新完成后再计算
            setTimeout(function () {
                // 获取文本容器
                var textContainer = textElement.parentElement;
                if (!textContainer) return;

                // 临时设置文本容器为自动宽度以获取真实文本宽度
                textContainer.style.width = 'auto';
                textContainer.style.whiteSpace = 'nowrap';
                textElement.style.width = 'auto';
                textElement.style.left = '0px';

                // 强制重新计算布局
                textContainer.offsetWidth;

                // 获取文本的实际宽度
                var textWidth = textElement.offsetWidth;

                // 计算居中位置
                // 热点图标是32px宽，居中位置是-16px
                // 文本容器需要相对于热点中心居中
                var containerLeft = -(textWidth / 2);

                // 设置文本容器的最终位置和宽度
                textContainer.style.left = containerLeft + 'px';
                textContainer.style.width = textWidth + 'px';

                // 确保文本在容器内居中
                textElement.style.left = '0px';
                textElement.style.width = textWidth + 'px';
                textElement.style.textAlign = 'center';

            }, 100); // 延迟100ms确保DOM更新完成

        } catch (error) {
            // 调整位置失败，静默处理
        }
    };

    // 初始化交互式热点添加监听器
    var initInteractiveHotspotListener = function () {
        if (isHotspotClickListenerInitialized) {
            return; // 避免重复初始化
        }

        try {
            // 检查Pano2VR API可用性
            if (typeof pano === 'undefined' || !pano) {
                console.warn('Pano2VR API not available for interactive hotspot');
                return;
            }

            // 方法1: 使用Pano2VR官方事件（推荐）
            if (typeof pano.on === 'function') {
                // 尝试多种可能的双击事件名称
                pano.on('playerdblclick', handlePanoramaDoubleClick);
                // pano.on('dblclick', handlePanoramaDoubleClick);
                // 也尝试单击事件，以防双击事件不存在
                // pano.on('playerclick', handlePanoramaDoubleClick);
                console.log('已绑定Pano2VR官方双击事件监听器');
            }
            // // 方法2: 使用addEventListener（备选）
            // else if (typeof pano.addEventListener === 'function') {
            //     pano.addEventListener('playerdblclick', handlePanoramaDoubleClick);
            //     pano.addEventListener('dblclick', handlePanoramaDoubleClick);
            //     pano.addEventListener('playerclick', handlePanoramaDoubleClick);
            //     console.log('已绑定Pano2VR addEventListener双击事件监听器');
            // }

            // // 方法3: 使用DOM事件监听（强制添加，确保有备选方案）
            // var panoContainer = document.getElementById('container') ||
            //                   document.querySelector('.pano-container') ||
            //                   document.querySelector('#pano') ||
            //                   document.body;

            // if (panoContainer) {
            //     // 同时绑定双击和单击事件
            //     panoContainer.addEventListener('dblclick', handleDOMDoubleClick);
            //     panoContainer.addEventListener('click', handleDOMDoubleClick); // 临时用单击测试
            //     console.log('已绑定DOM事件监听器到:', panoContainer.tagName, panoContainer.id);
            // }

            isHotspotClickListenerInitialized = true;

        } catch (error) {
            console.error('Failed to initialize interactive hotspot listener:', error);
        }
    };

    // 处理Pano2VR官方playerdblclick事件（双击添加热点）
    var handlePanoramaDoubleClick = function (obj) {
        try {
            console.log('收到双击事件:', obj.tilt, obj.pan);

            // 检查是否启用交互式热点添加
            if (!isInteractiveHotspotEnabled) {
                console.log('交互式热点功能未启用');
                return;
            }

            // 直接发送模拟坐标
            sendAddHotspotRequest(obj.pan, obj.tilt, 0, 0);

        } catch (error) {
            console.error('Error handling panorama click:', error);
        }
    };

    // 处理DOM事件（备选方案，临时支持单击和双击）

    // 验证坐标有效性（增强版）

    // 标准化pan值到 -180 到 180 范围
    var normalizePan = function (pan) {
        // 将pan值标准化到 -180 到 180 范围
        while (pan > 180) {
            pan -= 360;
        }
        while (pan < -180) {
            pan += 360;
        }
        return pan;
    };

    // 计算小数位数
    var countDecimalPlaces = function (value) {
        if (Math.floor(value) === value) return 0;
        var str = value.toString();
        if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
            return str.split('.')[1].length;
        } else if (str.indexOf('e-') !== -1) {
            var parts = str.split('e-');
            return parseInt(parts[1], 10);
        }
        return 0;
    };

    // 增强的坐标转换功能

    // 备选的数学计算方法（改进版）
    var calculateCoordinatesFromScreen = function (x, y, container) {
        try {
            var rect = container.getBoundingClientRect();
            var containerWidth = rect.width;
            var containerHeight = rect.height;

            console.log('容器信息:', {
                width: containerWidth,
                height: containerHeight,
                x: x,
                y: y
            });

            // 获取当前视角参数
            var currentPan = pano.getPan();
            var currentTilt = pano.getTilt();
            var currentFov = pano.getFov();

            console.log('当前视角参数:', {
                pan: currentPan,
                tilt: currentTilt,
                fov: currentFov
            });

            // 确保坐标在容器范围内
            if (x < 0 || x > containerWidth || y < 0 || y > containerHeight) {
                console.warn('坐标超出容器范围');
            }

            // 计算相对于容器中心的偏移（标准化到-1到1范围）
            var centerX = containerWidth / 2;
            var centerY = containerHeight / 2;
            var normalizedX = (x - centerX) / centerX;  // -1 到 1
            var normalizedY = (y - centerY) / centerY;  // -1 到 1

            console.log('标准化坐标:', {
                normalizedX: normalizedX,
                normalizedY: normalizedY
            });

            // 计算视野角度（考虑纵横比）
            var aspectRatio = containerWidth / containerHeight;
            var fovRad = currentFov * Math.PI / 180;

            // 根据Pano2VR的实际行为调整FOV计算
            var horizontalFovRad = fovRad;
            var verticalFovRad = fovRad / aspectRatio;

            // 计算角度偏移
            var panOffset = normalizedX * (horizontalFovRad / 2) * (180 / Math.PI);
            var tiltOffset = -normalizedY * (verticalFovRad / 2) * (180 / Math.PI);

            console.log('角度偏移:', {
                panOffset: panOffset,
                tiltOffset: tiltOffset
            });

            // 计算目标坐标
            var targetPan = normalizePan(currentPan + panOffset);
            var targetTilt = Math.max(-90, Math.min(90, currentTilt + tiltOffset));

            var result = {
                pan: parseFloat(targetPan.toFixed(6)),
                tilt: parseFloat(targetTilt.toFixed(6))
            };

            console.log('计算结果:', result);
            return result;

        } catch (error) {
            console.error('Mathematical coordinate calculation failed:', error);
            return null;
        }
    };

    // 发送添加热点请求到父页面
    var sendAddHotspotRequest = function (pan, tilt, screenX, screenY) {
        try {
            var message = {
                type: 'addHotspotRequest',
                coordinates: {
                    pan: parseFloat(pan.toFixed(2)),
                    tilt: parseFloat(tilt.toFixed(2))
                },
                screenPosition: {
                    x: screenX || 0,
                    y: screenY || 0
                },
                nodeId: currentNodeId,
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');

        } catch (error) {
            console.error('Error sending add hotspot request:', error);
        }
    };

    // 显示删除热点菜单
    var showDeleteHotspotMenu = function (event, hotspot) {
        try {
            // 移除现有菜单
            var existingMenu = document.querySelector('.hotspot-delete-menu');
            if (existingMenu) {
                existingMenu.remove();
            }

            // 创建菜单
            var menu = document.createElement('div');
            menu.className = 'hotspot-delete-menu';
            menu.style.cssText =
                'position: fixed; ' +
                'left: ' + event.clientX + 'px; ' +
                'top: ' + event.clientY + 'px; ' +
                'background: white; ' +
                'border: 1px solid #ccc; ' +
                'border-radius: 4px; ' +
                'box-shadow: 0 2px 8px rgba(0,0,0,0.15); ' +
                'z-index: 10000; ' +
                'padding: 8px 0; ' +
                'min-width: 120px;';

            // 创建删除选项
            var deleteOption = document.createElement('div');
            deleteOption.style.cssText =
                'padding: 8px 16px; ' +
                'cursor: pointer; ' +
                'color: #ff4d4f; ' +
                'font-size: 14px;';
            deleteOption.textContent = '删除热点';

            deleteOption.addEventListener('click', function () {
                menu.remove();
                // 发送删除请求到父页面
                window.parent.postMessage({
                    type: 'deleteHotspotRequest',
                    hotspotId: hotspot.id,
                    hotspot: hotspot,
                    nodeId: currentNodeId,
                    timestamp: new Date().getTime()
                }, '*');
            });

            deleteOption.addEventListener('mouseenter', function () {
                this.style.backgroundColor = '#f5f5f5';
            });

            deleteOption.addEventListener('mouseleave', function () {
                this.style.backgroundColor = 'transparent';
            });

            menu.appendChild(deleteOption);
            document.body.appendChild(menu);

            // 点击其他地方关闭菜单
            setTimeout(function () {
                document.addEventListener('click', function closeMenu() {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                });
            }, 100);

        } catch (error) {
            console.error('显示删除菜单失败:', error);
        }
    };

    // 初始化所有功能
    var initAllFeatures = function () {
        initMessageListener();
        initNodeSwitchListener();
        initHotspotClickListener();
        initInteractiveHotspotListener();
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function () {
            waitForPano(initAllFeatures);
        });
    } else {
        waitForPano(initAllFeatures);
    }

    // 处理启用/禁用交互式热点添加
    var handleToggleInteractiveHotspot = function (data) {
        try {
            isInteractiveHotspotEnabled = data.enabled === true;

            // 向父页面发送状态确认
            window.parent.postMessage({
                type: 'interactiveHotspotToggled',
                enabled: isInteractiveHotspotEnabled,
                success: true
            }, '*');

        } catch (error) {
            // 向父页面发送错误消息
            window.parent.postMessage({
                type: 'interactiveHotspotToggled',
                enabled: false,
                success: false,
                error: error.message
            }, '*');
        }
    };

    // 处理热点删除请求（使用Pano2VR官方API）
    var handleDeleteHotspotRequest = function (data) {
        try {
            if (!data || !data.hotspotId) {
                return;
            }

            var hotspotId = data.hotspotId;
            var deleted = false;

            console.log('开始使用官方API删除热点:', hotspotId);

            // 使用Pano2VR官方API删除热点
            if (typeof pano !== 'undefined' && pano && typeof pano.removeHotspot === 'function') {
                // 首先检查热点是否存在
                var existingHotspotIds = pano.getPointHotspotIds();
                if (existingHotspotIds && existingHotspotIds.indexOf(hotspotId) !== -1) {
                    pano.removeHotspot(hotspotId);
                    deleted = true;
                    console.log('使用官方API删除热点成功:', hotspotId);
                } else {
                    console.log('热点不存在:', hotspotId, '现有热点:', existingHotspotIds);
                }
            } else {
                console.error('Pano2VR removeHotspot API不可用');
            }

            // 备选方案：通过DOM查找并删除热点元素
            if (!deleted) {
                var hotspotElement = document.querySelector('[data-hotspot-id="' + hotspotId + '"]');
                if (hotspotElement) {
                    hotspotElement.remove();
                    deleted = true;
                    console.log('通过DOM删除热点成功:', hotspotId);
                }
            }

            // 向父页面发送删除结果
            window.parent.postMessage({
                type: 'hotspotDeleteComplete',
                hotspotId: hotspotId,
                success: deleted,
                timestamp: new Date().getTime()
            }, '*');

        } catch (error) {
            console.error('删除热点失败:', error);
            // 向父页面发送错误消息
            window.parent.postMessage({
                type: 'hotspotDeleteComplete',
                success: false,
                error: error.message
            }, '*');
        }
    };

    // 处理动态添加热点请求（使用Pano2VR官方API）
    var handleAddHotspotToPreview = function (data) {
        try {
            if (!data || !data.hotspot) {
                console.error('无效的热点数据');
                return;
            }

            var hotspot = data.hotspot;
            console.log('开始使用官方API动态添加热点到预览:', hotspot);

            // 检查Pano2VR API可用性
            if (typeof pano === 'undefined' || !pano || typeof pano.addHotspot !== 'function') {
                throw new Error('Pano2VR addHotspot API不可用');
            }

            // 创建热点DOM元素
            var hotspotElement = createHotspotElement(hotspot);
            if (!hotspotElement) {
                throw new Error('创建热点元素失败');
            }

            // 使用Pano2VR官方API添加热点
            pano.addHotspot(hotspot.id, hotspot.pan, hotspot.tilt, hotspotElement);

            // 向父页面发送成功消息
            window.parent.postMessage({
                type: 'hotspotAddedToPreview',
                hotspotId: hotspot.id,
                success: true,
                timestamp: new Date().getTime()
            }, '*');

            console.log('使用官方API动态添加热点成功:', hotspot.id);

        } catch (error) {
            console.error('动态添加热点失败:', error);
            // 向父页面发送错误消息
            window.parent.postMessage({
                type: 'hotspotAddedToPreview',
                success: false,
                error: error.message
            }, '*');
        }
    };

    // 创建热点DOM元素（简化版，位置由Pano2VR API管理）
    var createHotspotElement = function (hotspot) {
        try {
            // 创建主容器
            var hotspotDiv = document.createElement('div');
            hotspotDiv.className = 'ggskin ggskin_hotspot stand-alone';
            hotspotDiv.setAttribute('data-hotspot-id', hotspot.id);
            hotspotDiv.style.cssText =
                'height: 0px; ' +
                'position: absolute; ' +
                'visibility: inherit; ' +
                'width: 0px; ' +
                'pointer-events: auto; ' +
                'cursor: pointer; ' +
                'transform-origin: 50% 50%; ' +
                'transition: none;';

            // 创建简单的圆形图标（更可靠的显示方式）
            var iconDiv = document.createElement('div');
            iconDiv.className = 'hotspot-icon';
            iconDiv.style.cssText =
                'width: 20px; ' +
                'height: 20px; ' +
                'left: -10px; ' +
                'top: -10px; ' +
                'position: absolute; ' +
                'background-color: #1296db; ' +
                'border: 2px solid #ffffff; ' +
                'border-radius: 50%; ' +
                'box-shadow: 0 2px 6px rgba(0,0,0,0.3); ' +
                'cursor: pointer; ' +
                'visibility: inherit; ' +
                'pointer-events: auto;';

            // 添加内部小圆点
            var innerDot = document.createElement('div');
            innerDot.style.cssText =
                'width: 6px; ' +
                'height: 6px; ' +
                'background-color: #ffffff; ' +
                'border-radius: 50%; ' +
                'position: absolute; ' +
                'top: 50%; ' +
                'left: 50%; ' +
                'transform: translate(-50%, -50%);';

            iconDiv.appendChild(innerDot);

            // 创建文本标签容器（调整位置以与圆形图标对齐）
            var textDiv = document.createElement('div');
            textDiv.className = 'ggskin ggskin_text';
            textDiv.style.cssText =
                'height: 20px; ' +
                'left: -50px; ' +
                'position: absolute; ' +
                'top: 18px; ' +
                'visibility: inherit; ' +
                'width: 100px; ' +
                'pointer-events: auto; ' +
                'text-shadow: rgb(0, 0, 0) 1px 1px 4px; ' +
                'transform-origin: 50% 50%; ' +
                'transition: none;';

            // 创建文本内容
            var textContent = document.createElement('div');
            textContent.style.cssText =
                'position: absolute; ' +
                'box-sizing: border-box; ' +
                'cursor: default; ' +
                'left: 9.5px; ' +
                'top: 0px; ' +
                'width: auto; ' +
                'height: auto; ' +
                'border: 1px solid rgb(0, 0, 0); ' +
                'color: rgb(255, 255, 255); ' +
                'text-align: center; ' +
                'white-space: nowrap; ' +
                'padding: 2px 5px; ' +
                'overflow: hidden;';
            textContent.textContent = hotspot.title || '新热点';

            textDiv.appendChild(textContent);

            // 组装元素
            hotspotDiv.appendChild(iconDiv);
            hotspotDiv.appendChild(textDiv);

            // 添加右键删除事件
            hotspotDiv.addEventListener('contextmenu', function (e) {
                e.preventDefault();
                if (isInteractiveHotspotEnabled) {
                    showDeleteHotspotMenu(e, hotspot);
                }
            });

            return hotspotDiv;

        } catch (error) {
            console.error('创建热点元素失败:', error);
            return null;
        }
    };

    // 为了兼容性，也在window.onload时尝试初始化
    var originalOnload = window.onload;
    window.onload = function () {
        if (originalOnload) originalOnload();
        waitForPano(initAllFeatures);
    };

})();
