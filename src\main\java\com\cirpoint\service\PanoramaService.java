package com.cirpoint.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.Pano2VRHotspotInjector;
import com.cirpoint.util.Util;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 全景图热点编辑服务
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
public class PanoramaService extends ApplicationConfig {

    /**
     * 获取型号列表
     */
    public JSONObject getModelList() {
        try {
            String sql = "SELECT NODENAME AS model_name, TREEID AS model_id " +
                    "FROM DATAPACKAGETREE " +
                    "WHERE NODETYPE = 'product' " +
                    "ORDER BY NLSSORT(NODENAME,'NLS_SORT=SCHINESE_PINYIN_M')";

            JSONArray modelList = Util.postQuerySql(sql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", modelList != null ? modelList : new JSONArray());
        } catch (Exception e) {
            log.error("获取型号列表失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取型号列表失败: " + e.getMessage())
                    .set("data", new JSONArray());
        }
    }

    /**
     * 检查热点标题是否重复
     */
    public JSONObject checkHotspotTitle(Long taskId, String title, Long hotspotId) {
        try {
            // 构建SQL查询，检查同一任务下是否有相同的编辑后标题
            String checkSql = "SELECT COUNT(*) AS cnt, " +
                    "LISTAGG(HOTSPOT_ID, ',') WITHIN GROUP (ORDER BY HOTSPOT_ID) AS hotspot_ids " +
                    "FROM PANORAMA_HOTSPOT " +
                    "WHERE TASK_ID = " + taskId + " " +
                    "AND EDITED_TITLE = '" + title.replace("'", "''") + "' ";

            // 如果是编辑现有热点，排除自己
            if (hotspotId != null) {
                checkSql += "AND HOTSPOT_ID != " + hotspotId;
            }

            JSONArray checkResult = Util.postQuerySql(checkSql);

            if (checkResult != null && checkResult.size() > 0) {
                Object firstRowObj = checkResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    int count = firstRow.getInt("CNT");
                    String hotspotIds = firstRow.getStr("HOTSPOT_IDS");

                    return JSONUtil.createObj()
                            .set("success", true)
                            .set("isDuplicate", count > 0)
                            .set("count", count)
                            .set("conflictHotspotIds", hotspotIds);
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("isDuplicate", false)
                    .set("count", 0);
        } catch (Exception e) {
            log.error("检查热点标题失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "检查热点标题失败: " + e.getMessage());
        }
    }

    /**
     * 查询型号下的单机数据并保存到PANORAMA_DEVICE表
     */
    private void queryAndSaveStandAloneDevices(Long taskId, String modelId, String modelName) {
        try {
            // 构建调用参数
            JSONObject params = new JSONObject();
            params.put("modelId", modelId);

            // 调用Thing.Fn.SecondTable.QueryStandAloneByModel获取单机数据
            JSONObject res = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryStandAloneByModel", params);

            if (res != null && res.containsKey("data")) {
                // 根据实际返回格式，data字段直接是JSONArray
                Object dataObj = res.get("data");

                if (dataObj instanceof JSONArray) {
                    JSONArray dataArray = (JSONArray) dataObj;

                    if (dataArray != null && dataArray.size() > 0) {
                        log.info("查询到型号 {} 下的单机数据 {} 条", modelName, dataArray.size());

                        // 遍历单机数据并保存到数据库
                        for (int i = 0; i < dataArray.size(); i++) {
                            Object rowObj = dataArray.get(i);
                            if (rowObj instanceof JSONObject) {
                                JSONObject deviceData = (JSONObject) rowObj;
                                saveStandAloneDeviceToDatabase(taskId, deviceData, modelId, modelName, i + 1);
                            }
                        }
                    } else {
                        log.info("型号 {} 下没有找到单机数据", modelName);
                    }
                } else {
                    log.warn("调用QueryStandAloneByModel返回的数据格式异常，data字段不是JSONArray类型: {}", dataObj.getClass().getSimpleName());
                }
            } else {
                log.warn("调用QueryStandAloneByModel失败或返回数据为空");
            }
        } catch (Exception e) {
            log.error("查询型号下的单机数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 保存单机数据到PANORAMA_DEVICE表
     */
    private void saveStandAloneDeviceToDatabase(Long taskId, JSONObject deviceData, String modelId, String modelName, int sequenceNo) {
        try {
            // 从返回的数据中提取字段
            String deviceCode = deviceData.getStr("devicecode");
            String deviceName = deviceData.getStr("devicename");
            String deviceBatch = deviceData.getStr("devicebatch");

            // 检查设备是否已存在 (基于任务ID和组合键，与Excel上传逻辑保持一致)
            String checkSql = "SELECT DEVICE_ID FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId +
                    " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                    " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                    " AND " + (deviceBatch != null ? "BATCH_NO = '" + deviceBatch.replace("'", "''") + "'" : "BATCH_NO IS NULL");

            JSONArray checkResult = Util.postQuerySql(checkSql);

            if (checkResult != null && !checkResult.isEmpty()) {
                // 设备已存在，执行更新操作
                Long deviceId = checkResult.getJSONObject(0).getLong("DEVICE_ID");
                String updateSql = "UPDATE PANORAMA_DEVICE SET " +
                        "SEQUENCE_NO = " + sequenceNo + ", " +
                        "MODEL_ID = '" + modelId.replace("'", "''") + "', " +
                        "MODEL_NAME = '" + modelName.replace("'", "''") + "', " +
                        "UPDATE_TIME = SYSDATE " +
                        "WHERE DEVICE_ID = " + deviceId;

                Util.postCommandSql(updateSql);
                log.debug("更新自动导入的单机数据: taskId={}, deviceId={}, deviceName={}",
                         taskId, deviceId, deviceName);
            } else {
                // 设备不存在，执行插入操作
                String insertSql = "INSERT INTO PANORAMA_DEVICE " +
                        "(DEVICE_ID, TASK_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, MODEL_ID, MODEL_NAME, CREATE_TIME, UPDATE_TIME) " +
                        "VALUES (SEQ_PANORAMA_DEVICE.NEXTVAL, " + taskId + ", " +
                        (deviceName != null ? "'" + deviceName.replace("'", "''") + "'" : "NULL") + ", " +
                        (deviceCode != null ? "'" + deviceCode.replace("'", "''") + "'" : "NULL") + ", " +
                        (deviceBatch != null ? "'" + deviceBatch.replace("'", "''") + "'" : "NULL") + ", " +
                        sequenceNo + ", " +
                        (modelId != null ? "'" + modelId.replace("'", "''") + "'" : "NULL") + ", " +
                        (modelName != null ? "'" + modelName.replace("'", "''") + "'" : "NULL") + ", SYSDATE, SYSDATE)";

                Util.postCommandSql(insertSql);
                log.debug("插入新的自动导入单机数据: taskId={}, deviceName={}, deviceCode={}, deviceBatch={}",
                         taskId, deviceName, deviceCode, deviceBatch);
            }
        } catch (Exception e) {
            log.error("保存自动导入的单机数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建新任务
     */
    public JSONObject createTask(String taskName, String modelId, String modelName, String description, String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "用户名不能为空");
            }

            // 检查任务名称是否重复
            String checkSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_TASK WHERE TASK_NAME = '" + taskName.replace("'", "''") + "'";
            JSONArray checkResult = Util.postQuerySql(checkSql);

            if (checkResult != null && checkResult.size() > 0) {
                Object firstRowObj = checkResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    int count = firstRow.getInt("CNT");
                    if (count > 0) {
                        return JSONUtil.createObj()
                                .set("success", false)
                                .set("msg", "任务名称已存在，请使用其他名称");
                    }
                }
            }

            // 生成任务ID
            String taskIdSql = "SELECT SEQ_PANORAMA_TASK.NEXTVAL FROM DUAL";
            JSONArray taskIdResult = Util.postQuerySql(taskIdSql);
            Long taskId = null;
            if (taskIdResult != null && taskIdResult.size() > 0) {
                Object firstRowObj = taskIdResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    taskId = ((JSONObject) firstRowObj).getLong("NEXTVAL");
                }
            }

            // 插入任务记录，使用传入的用户名作为创建者
            String insertSql = "INSERT INTO PANORAMA_TASK (TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS) " +
                    "VALUES (" + taskId + ", '" + taskName.replace("'", "''") + "', " +
                    (modelId != null ? "'" + modelId.replace("'", "''") + "'" : "NULL") + ", " +
                    (modelName != null ? "'" + modelName.replace("'", "''") + "'" : "NULL") + ", " +
                    (description != null ? "'" + description.replace("'", "''") + "'" : "NULL") + ", '" + username.replace("'", "''") + "', SYSDATE, SYSDATE, 0)";

            Util.postCommandSql(insertSql);

            // 如果提供了型号ID，自动查询并添加单机数据
            if (modelId != null && !modelId.trim().isEmpty()) {
                try {
                    queryAndSaveStandAloneDevices(taskId, modelId, modelName);
                    log.info("任务创建成功，已自动添加型号 {} 下的单机数据", modelName);
                } catch (Exception e) {
                    log.warn("自动添加单机数据失败，但不影响任务创建: {}", e.getMessage());
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务创建成功")
                    .set("data", JSONUtil.createObj().set("taskId", taskId));
        } catch (Exception e) {
            log.error("创建任务失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务列表（根据用户权限过滤）
     */
    public JSONObject getTaskList(String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "用户名不能为空");
            }

            // 查询用户创建的任务列表
            String sql = "SELECT TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS " +
                    "FROM PANORAMA_TASK WHERE CREATE_USER = '" + username.replace("'", "''") + "' ORDER BY CREATE_TIME DESC";
            JSONArray taskList = Util.postQuerySql(sql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", taskList != null ? taskList : new JSONArray());
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    public JSONObject getTaskDetail(Long taskId) {
        try {
            String sql = "SELECT * FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            // 安全获取第一行数据
            Object firstRowObj = result.get(0);
            JSONObject taskData = null;
            if (firstRowObj instanceof JSONObject) {
                taskData = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", taskData);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务管理列表（分页，根据用户权限过滤）
     */
    public JSONObject getTaskManagementList(Integer page, Integer limit, String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("code", 1)
                        .set("msg", "用户名不能为空")
                        .set("count", 0)
                        .set("data", new JSONArray());
            }

            // 计算分页参数
            int offset = (page - 1) * limit;

            // 查询总数（仅统计用户创建的任务）
            String countSql = "SELECT COUNT(*) as total FROM PANORAMA_TASK WHERE CREATE_USER = '" + username.replace("'", "''") + "'";
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            // 查询分页数据，包含状态文本转换（仅查询用户创建的任务）
            String dataSql = "SELECT * FROM (" +
                    "SELECT ROWNUM rn, t.* FROM (" +
                    "SELECT TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, " +
                    "CASE STATUS " +
                    "  WHEN 0 THEN '创建中' " +
                    "  WHEN 1 THEN '已完成' " +
                    "  WHEN 2 THEN '已导出' " +
                    "  ELSE '未知' " +
                    "END AS STATUS_TEXT, " +
                    "STATUS, CREATE_USER, " +
                    "TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as CREATE_TIME, " +
                    "TO_CHAR(UPDATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as UPDATE_TIME " +
                    "FROM PANORAMA_TASK WHERE CREATE_USER = '" + username.replace("'", "''") + "' ORDER BY CREATE_TIME DESC" +
                    ") t WHERE ROWNUM <= " + (offset + limit) +
                    ") WHERE rn > " + offset;

            JSONArray dataResult = Util.postQuerySql(dataSql);

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("count", total)
                    .set("data", dataResult != null ? dataResult : new JSONArray());

        } catch (Exception e) {
            log.error("获取任务管理列表失败", e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取任务管理列表失败: " + e.getMessage())
                    .set("count", 0)
                    .set("data", new JSONArray());
        }
    }

    /**
     * 更新任务信息（仅任务名称和描述）
     */
    public JSONObject updateTask(Long taskId, String taskName, String description) {
        try {
            // 检查任务是否存在
            String checkSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray checkResult = Util.postQuerySql(checkSql);
            if (checkResult == null || checkResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            Object firstRowObj = checkResult.get(0);
            if (firstRowObj instanceof JSONObject) {
                int count = ((JSONObject) firstRowObj).getInt("CNT");
                if (count == 0) {
                    return JSONUtil.createObj()
                            .set("success", false)
                            .set("msg", "任务不存在");
                }
            }

            // 检查任务名称是否与其他任务重复
            String duplicateCheckSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_TASK " +
                    "WHERE TASK_NAME = '" + taskName.replace("'", "''") + "' AND TASK_ID != " + taskId;
            JSONArray duplicateResult = Util.postQuerySql(duplicateCheckSql);
            if (duplicateResult != null && duplicateResult.size() > 0) {
                Object duplicateRowObj = duplicateResult.get(0);
                if (duplicateRowObj instanceof JSONObject) {
                    int duplicateCount = ((JSONObject) duplicateRowObj).getInt("CNT");
                    if (duplicateCount > 0) {
                        return JSONUtil.createObj()
                                .set("success", false)
                                .set("msg", "任务名称已存在，请使用其他名称");
                    }
                }
            }

            // 更新任务信息
            String updateSql = "UPDATE PANORAMA_TASK SET " +
                    "TASK_NAME = '" + taskName.replace("'", "''") + "', " +
                    "DESCRIPTION = " + (description != null ? "'" + description.replace("'", "''") + "'" : "NULL") + ", " +
                    "UPDATE_TIME = SYSDATE " +
                    "WHERE TASK_ID = " + taskId;

            Util.postCommandSql(updateSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务信息更新成功");

        } catch (Exception e) {
            log.error("更新任务信息失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "更新任务信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务删除前的统计信息
     */
    public JSONObject getTaskDeleteStats(Long taskId) {
        try {
            // 检查任务是否存在
            String taskSql = "SELECT TASK_NAME, STATUS FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);
            if (taskResult == null || taskResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            Object taskRowObj = taskResult.get(0);
            JSONObject taskInfo = null;
            if (taskRowObj instanceof JSONObject) {
                taskInfo = (JSONObject) taskRowObj;
            }

            // 统计热点数量
            String hotspotCountSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            JSONArray hotspotResult = Util.postQuerySql(hotspotCountSql);
            int hotspotCount = 0;
            if (hotspotResult != null && hotspotResult.size() > 0) {
                Object hotspotRowObj = hotspotResult.get(0);
                if (hotspotRowObj instanceof JSONObject) {
                    hotspotCount = ((JSONObject) hotspotRowObj).getInt("CNT");
                }
            }

            // 统计设备数量
            String deviceCountSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId;
            JSONArray deviceResult = Util.postQuerySql(deviceCountSql);
            int deviceCount = 0;
            if (deviceResult != null && deviceResult.size() > 0) {
                Object deviceRowObj = deviceResult.get(0);
                if (deviceRowObj instanceof JSONObject) {
                    deviceCount = ((JSONObject) deviceRowObj).getInt("CNT");
                }
            }

            // 检查是否有文件
            boolean hasFiles = false;
            if (taskInfo != null) {
                String zipFilePath = taskInfo.getStr("ZIP_FILE_PATH");
                String extractPath = taskInfo.getStr("EXTRACT_PATH");
                hasFiles = (zipFilePath != null && !zipFilePath.trim().isEmpty()) ||
                          (extractPath != null && !extractPath.trim().isEmpty());
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj()
                            .set("taskName", taskInfo != null ? taskInfo.getStr("TASK_NAME") : "")
                            .set("hotspotCount", hotspotCount)
                            .set("deviceCount", deviceCount)
                            .set("hasFiles", hasFiles));

        } catch (Exception e) {
            log.error("获取任务删除统计信息失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除任务（级联删除相关数据）
     */
    public JSONObject deleteTask(Long taskId) {
        try {
            // 检查任务是否存在并获取文件路径信息
            String taskSql = "SELECT TASK_NAME, ZIP_FILE_PATH, EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);
            if (taskResult == null || taskResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            Object taskRowObj = taskResult.get(0);
            JSONObject taskInfo = null;
            if (taskRowObj instanceof JSONObject) {
                taskInfo = (JSONObject) taskRowObj;
            }

            // 开始事务性删除操作
            log.info("开始删除任务: taskId={}, taskName={}", taskId,
                    taskInfo != null ? taskInfo.getStr("TASK_NAME") : "未知");

            // 1. 删除热点数据
            String deleteHotspotSql = "DELETE FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteHotspotSql);
            log.info("已删除任务 {} 的热点数据", taskId);

            // 2. 删除设备数据
            String deleteDeviceSql = "DELETE FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteDeviceSql);
            log.info("已删除任务 {} 的设备数据", taskId);

            // 3. 删除文件系统中的相关文件
            if (taskInfo != null) {
                String zipFilePath = taskInfo.getStr("ZIP_FILE_PATH");
                String extractPath = taskInfo.getStr("EXTRACT_PATH");

                // 删除ZIP文件
                if (zipFilePath != null && !zipFilePath.trim().isEmpty()) {
                    try {
                        File zipFile = new File(zipFilePath);
                        if (zipFile.exists()) {
                            FileUtil.del(zipFile);
                            log.info("已删除ZIP文件: {}", zipFilePath);
                        }
                    } catch (Exception e) {
                        log.warn("删除ZIP文件失败: {}, 错误: {}", zipFilePath, e.getMessage());
                    }
                }

                // 删除解压目录
                if (extractPath != null && !extractPath.trim().isEmpty()) {
                    try {
                        File extractDir = new File(extractPath);
                        if (extractDir.exists()) {
                            FileUtil.del(extractDir);
                            log.info("已删除解压目录: {}", extractPath);
                        }
                    } catch (Exception e) {
                        log.warn("删除解压目录失败: {}, 错误: {}", extractPath, e.getMessage());
                    }
                }
            }

            // 4. 最后删除任务记录
            String deleteTaskSql = "DELETE FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteTaskSql);
            log.info("已删除任务记录: taskId={}", taskId);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务删除成功");

        } catch (Exception e) {
            log.error("删除任务失败: taskId={}", taskId, e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "删除任务失败: " + e.getMessage());
        }
    }

    /**
     * 检查任务是否已存在热点数据
     */
    public JSONObject checkExistingHotspots(Long taskId) {
        try {
            String countSql = "SELECT COUNT(*) AS TOTAL FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("hasData", total > 0)
                    .set("count", total);
        } catch (Exception e) {
            log.error("检查热点数据失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "检查热点数据失败: " + e.getMessage());
        }
    }

    /**
     * 清理任务的热点数据和文件信息
     */
    public JSONObject clearTaskData(Long taskId) {
        try {
            // 删除热点数据
            String deleteHotspotSql = "DELETE FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteHotspotSql);

            // 清空任务的文件路径信息
            String updateTaskSql = "UPDATE PANORAMA_TASK SET ZIP_FILE_PATH = NULL, EXTRACT_PATH = NULL, UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateTaskSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务数据清理成功");
        } catch (Exception e) {
            log.error("清理任务数据失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "清理任务数据失败: " + e.getMessage());
        }
    }

    /**
     * 上传全景图ZIP包
     */
    public JSONObject uploadPanoramaZip(Long taskId, MultipartFile file) throws IOException {
        try {
            // 创建任务专用目录
            String taskDir = fileUploadPath + File.separator + "panorama" + File.separator + taskId;
            FileUtil.mkdir(taskDir);

            // 保存ZIP文件
            String zipFileName = "panorama_" + System.currentTimeMillis() + ".zip";
            String zipFilePath = taskDir + File.separator + zipFileName;
            file.transferTo(new File(zipFilePath));

            // 解压ZIP文件
            String extractPath = taskDir + File.separator + "extracted";
            ZipUtil.unzip(zipFilePath, extractPath);

            // 替换自定义pano2vr_player.js文件（如果存在）
            replaceCustomPlayerScript(extractPath);

            // 查找pano.xml文件
            File panoXmlFile = findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到pano.xml文件");
            }

            // 解析XML并保存热点信息
            parseAndSaveHotspots(taskId, panoXmlFile);

            // 注入热点定位脚本到HTML文件
            try {
                log.info("开始为解压目录注入热点定位脚本: {}", extractPath);
                Pano2VRHotspotInjector.processDirectory(extractPath);
                log.info("热点定位脚本注入完成");
            } catch (Exception e) {
                log.warn("注入热点定位脚本失败，但不影响主流程: {}", e.getMessage());
            }

            // 更新任务信息
            String updateSql = "UPDATE PANORAMA_TASK SET ZIP_FILE_PATH = '" + zipFilePath +
                    "', EXTRACT_PATH = '" + extractPath + "', UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "ZIP文件上传成功")
                    .set("data", JSONUtil.createObj()
                            .set("zipPath", zipFilePath)
                            .set("extractPath", extractPath));
        } catch (Exception e) {
            log.error("上传ZIP文件失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "上传ZIP文件失败: " + e.getMessage());
        }
    }

    /**
     * 查找pano.xml文件
     */
    private File findPanoXmlFile(File dir) {
        if (!dir.exists() || !dir.isDirectory()) {
            return null;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            return null;
        }

        for (File file : files) {
            if (file.isFile() && "pano.xml".equals(file.getName())) {
                return file;
            } else if (file.isDirectory()) {
                File found = findPanoXmlFile(file);
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }

    /**
     * 替换解压目录中的pano2vr_player.js文件为项目自定义版本
     * 
     * @param extractPath 解压路径
     * @return 替换是否成功
     */
    private boolean replaceCustomPlayerScript(String extractPath) {
        try {
            // 定义源文件路径（项目中的自定义版本）
            String customPlayerPath = "src/main/webapp/panorama/js/pano2vr_player.js";
            File customPlayerFile = new File(customPlayerPath);
            
            // 验证自定义文件存在性和可读性
            if (!customPlayerFile.exists()) {
                log.warn("自定义pano2vr_player.js文件不存在，跳过替换: {}", customPlayerPath);
                return false;
            }
            
            if (!customPlayerFile.canRead()) {
                log.error("自定义pano2vr_player.js文件无法读取，权限不足: {}", customPlayerPath);
                return false;
            }
            
            // 构建目标文件路径
            String targetPlayerPath = extractPath + File.separator + "pano2vr_player.js";
            File targetPlayerFile = new File(targetPlayerPath);
            
            // 检查目标文件是否存在（非必要条件）
            if (!targetPlayerFile.exists()) {
                log.info("ZIP中未找到pano2vr_player.js文件，可能是非标准结构: {}", targetPlayerPath);
                return false;
            }
            
            // 执行文件替换（原子操作）
            FileUtil.copy(customPlayerFile, targetPlayerFile, true);
            
            // 验证替换后文件大小 > 0
            if (targetPlayerFile.length() <= 0) {
                log.error("替换后的pano2vr_player.js文件大小为0，替换可能失败: {}", targetPlayerPath);
                return false;
            }
            
            log.info("成功替换pano2vr_player.js文件: {} -> {}", customPlayerPath, targetPlayerPath);
            return true;
            
        } catch (SecurityException e) {
            log.error("文件权限不足，无法替换pano2vr_player.js: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("替换pano2vr_player.js时发生IO错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析XML并保存热点信息
     */
    private void parseAndSaveHotspots(Long taskId, File panoXmlFile) throws Exception {
        SAXReader reader = new SAXReader();
        Document document = reader.read(panoXmlFile);
        Element root = document.getRootElement();

        // 查找所有panorama节点
        List<Element> panoramas = root.elements("panorama");

        log.info("开始解析XML文件，找到 {} 个panorama节点", panoramas.size());

        for (Element panorama : panoramas) {
            // 获取panorama节点的id属性，用于多节点支持
            String panoramaId = panorama.attributeValue("id");
            if (panoramaId == null || panoramaId.trim().isEmpty()) {
                // 如果没有id属性，使用默认值
                panoramaId = "node1";
                log.warn("Panorama节点没有id属性，使用默认值: {}", panoramaId);
            }

            log.debug("处理panorama节点: {}", panoramaId);

            Element hotspotsElement = panorama.element("hotspots");
            if (hotspotsElement != null) {
                List<Element> hotspots = hotspotsElement.elements("hotspot");
                log.debug("在panorama节点 {} 中找到 {} 个热点", panoramaId, hotspots.size());

                for (Element hotspot : hotspots) {
                    String skinid = hotspot.attributeValue("skinid");
                    // 只处理stand-alone类型的热点
                    if ("stand-alone".equals(skinid)) {
                        // 传递panoramaId参数到保存方法
                        saveHotspotToDatabase(taskId, panoramaId, hotspot);
                    }
                }
            } else {
                log.debug("Panorama节点 {} 没有hotspots元素", panoramaId);
            }
        }

        log.info("XML解析完成，已处理 {} 个panorama节点", panoramas.size());
    }

    /**
     * 保存热点信息到数据库
     * @param taskId 任务ID
     * @param panoramaId 所属全景节点ID
     * @param hotspot 热点XML元素
     */
    private void saveHotspotToDatabase(Long taskId, String panoramaId, Element hotspot) {
        try {
            String hotspotXmlId = hotspot.attributeValue("id");
            String originalTitle = hotspot.attributeValue("title");
            String originalDescription = hotspot.attributeValue("description");
            String pan = hotspot.attributeValue("pan");
            String tilt = hotspot.attributeValue("tilt");
            String skinid = hotspot.attributeValue("skinid");
            String url = hotspot.attributeValue("url");
            String target = hotspot.attributeValue("target");

            // 构建包含PANORAMA_ID字段的INSERT语句
            String insertSql = "INSERT INTO PANORAMA_HOTSPOT " +
                    "(HOTSPOT_ID, TASK_ID, PANORAMA_ID, HOTSPOT_XML_ID, ORIGINAL_TITLE, ORIGINAL_DESCRIPTION, PAN, TILT, SKINID, URL, TARGET, IS_EDITED, CREATE_TIME, UPDATE_TIME) " +
                    "VALUES (SEQ_PANORAMA_HOTSPOT.NEXTVAL, " + taskId + ", " +
                    (panoramaId != null ? "'" + panoramaId + "'" : "NULL") + ", " +
                    (hotspotXmlId != null ? "'" + hotspotXmlId + "'" : "NULL") + ", " +
                    (originalTitle != null ? "'" + originalTitle.replace("'", "''") + "'" : "NULL") + ", " +
                    (originalDescription != null ? "'" + originalDescription.replace("'", "''") + "'" : "NULL") + ", " +
                    (pan != null ? "'" + pan + "'" : "NULL") + ", " +
                    (tilt != null ? "'" + tilt + "'" : "NULL") + ", " +
                    (skinid != null ? "'" + skinid + "'" : "NULL") + ", " +
                    (url != null ? "'" + url + "'" : "NULL") + ", " +
                    (target != null ? "'" + target + "'" : "NULL") + ", " +
                    "0, SYSDATE, SYSDATE)";

            log.debug("保存热点到数据库: taskId={}, panoramaId={}, hotspotXmlId={}, title={}",
                     taskId, panoramaId, hotspotXmlId, originalTitle);

            Util.postCommandSql(insertSql);
        } catch (Exception e) {
            log.error("保存热点信息失败", e);
        }
    }

    /**
     * 上传单机信息Excel文件
     */
    public JSONObject uploadDeviceExcel(Long taskId, MultipartFile file) throws IOException {
        try {
            // 保存Excel文件
            String tempDir = tempPath + File.separator + "excel_" + System.currentTimeMillis();
            FileUtil.mkdir(tempDir);
            String excelPath = tempDir + File.separator + file.getOriginalFilename();
            file.transferTo(new File(excelPath));

            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(new File(excelPath));
            List<List<Object>> rows = reader.read();

            // 跳过表头，从第二行开始读取数据
            for (int i = 1; i < rows.size(); i++) {
                List<Object> row = rows.get(i);
                if (row.size() >= 4) {
                    saveDeviceToDatabase(taskId, row);
                }
            }

            // 清理临时文件
            FileUtil.del(tempDir);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "Excel文件上传成功");
        } catch (Exception e) {
            log.error("上传Excel文件失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "上传Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 保存单机信息到数据库（支持去重更新）
     */
    private void saveDeviceToDatabase(Long taskId, List<Object> row) {
        try {
            Object sequenceNo = row.get(0);
            Object deviceName = row.get(1);
            Object deviceCode = row.get(2);
            Object batchNo = row.get(3);
            // 移除型号ID和型号名称的处理，Excel中只有4列数据

            // 检查设备是否已存在（基于任务ID、设备名称、设备代号、批次号）
            // 修复NULL值比较问题，使用IS NULL而不是= NULL
            String checkSql = "SELECT COUNT(*) as cnt FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId +
                    " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.toString().replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                    " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.toString().replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                    " AND " + (batchNo != null ? "BATCH_NO = '" + batchNo.toString().replace("'", "''") + "'" : "BATCH_NO IS NULL");

            JSONArray checkResult = Util.postQuerySql(checkSql);
            JSONArray dataArray = checkResult;

            boolean deviceExists = false;
            if (dataArray != null && dataArray.size() > 0) {
                // 修复类型转换错误：从JSONArray中获取JSONObject
                Object firstRowObj = dataArray.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    int count = firstRow.getInt("CNT");
                    deviceExists = count > 0;
                }
            }

            if (deviceExists) {
                // 设备已存在，执行更新操作（移除型号ID和型号名称）
                String updateSql = "UPDATE PANORAMA_DEVICE SET " +
                        "SEQUENCE_NO = " + (sequenceNo != null ? sequenceNo.toString() : "NULL") + ", " +
                        "UPDATE_TIME = SYSDATE " +
                        "WHERE TASK_ID = " + taskId +
                        " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.toString().replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                        " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.toString().replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                        " AND " + (batchNo != null ? "BATCH_NO = '" + batchNo.toString().replace("'", "''") + "'" : "BATCH_NO IS NULL");

                Util.postCommandSql(updateSql);
                log.info("更新设备信息: " + deviceName);
            } else {
                // 设备不存在，执行插入操作（移除型号ID和型号名称）
                String insertSql = "INSERT INTO PANORAMA_DEVICE " +
                        "(DEVICE_ID, TASK_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, CREATE_TIME, UPDATE_TIME) " +
                        "VALUES (SEQ_PANORAMA_DEVICE.NEXTVAL, " + taskId + ", " +
                        (deviceName != null ? "'" + deviceName.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (deviceCode != null ? "'" + deviceCode.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (batchNo != null ? "'" + batchNo.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (sequenceNo != null ? sequenceNo.toString() : "NULL") + ", SYSDATE, SYSDATE)";

                Util.postCommandSql(insertSql);
                log.info("新增设备信息: " + deviceName);
            }
        } catch (Exception e) {
            log.error("保存单机信息失败", e);
        }
    }

    /**
     * 获取任务下的设备列表（分页）
     */
    public JSONObject getDeviceList(Long taskId, int page, int limit) {
        try {
            // 计算分页参数
            int offset = (page - 1) * limit;

            // 查询总数
            String countSql = "SELECT COUNT(*) as total FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId;
            JSONArray countResult = Util.postQuerySql(countSql);
            JSONArray countArray = countResult;
            int total = 0;
            if (countArray != null && countArray.size() > 0) {
                // 修复类型转换错误：从JSONArray中获取JSONObject
                Object firstRowObj = countArray.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    total = firstRow.getInt("TOTAL");
                }
            }

            // 查询分页数据
            String dataSql = "SELECT * FROM (" +
                    "SELECT ROWNUM rn, t.* FROM (" +
                    "SELECT DEVICE_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, MODEL_ID, MODEL_NAME, " +
                    "TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as CREATE_TIME, " +
                    "TO_CHAR(UPDATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as UPDATE_TIME " +
                    "FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId + " " +
                    "ORDER BY SEQUENCE_NO ASC, CREATE_TIME DESC" +
                    ") t WHERE ROWNUM <= " + (offset + limit) +
                    ") WHERE rn > " + offset;

            JSONArray dataResult = Util.postQuerySql(dataSql);
            JSONArray dataArray = dataResult;

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("count", total)
                    .set("data", dataArray != null ? dataArray : new JSONArray());

        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取设备列表失败: " + e.getMessage())
                    .set("count", 0)
                    .set("data", new JSONArray());
        }
    }

    /**
     * 导出设备数据为Excel文件
     */
    public File exportDeviceExcel(Long taskId) {
        try {
            // 查询任务信息
            String taskSql = "SELECT TASK_NAME FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);
            String taskName = "未知任务";
            if (taskResult != null && taskResult.size() > 0) {
                Object firstRowObj = taskResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    taskName = ((JSONObject) firstRowObj).getStr("TASK_NAME");
                }
            }

            // 查询设备数据（移除型号ID和型号名称列）
            String deviceSql = "SELECT SEQUENCE_NO, DEVICE_NAME, DEVICE_CODE, BATCH_NO " +
                    "FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId + " ORDER BY SEQUENCE_NO ASC, CREATE_TIME ASC";
            JSONArray deviceData = Util.postQuerySql(deviceSql);

            // 准备表头（移除型号ID和型号名称列）
            JSONArray headers = new JSONArray();
            headers.add("序号");
            headers.add("单机名称");
            headers.add("单机代号");
            headers.add("批次号");

            // 准备数据
            JSONArray data = new JSONArray();
            if (deviceData != null && deviceData.size() > 0) {
                for (int i = 0; i < deviceData.size(); i++) {
                    Object rowObj = deviceData.get(i);
                    if (rowObj instanceof JSONObject) {
                        JSONObject row = (JSONObject) rowObj;
                        JSONArray dataRow = new JSONArray();

                        // 序号字段确保为整数格式
                        Object sequenceNo = row.get("SEQUENCE_NO");
                        if (sequenceNo != null) {
                            if (sequenceNo instanceof Number) {
                                dataRow.add(((Number) sequenceNo).intValue());
                            } else {
                                try {
                                    dataRow.add(Integer.parseInt(sequenceNo.toString()));
                                } catch (NumberFormatException e) {
                                    dataRow.add(sequenceNo.toString());
                                }
                            }
                        } else {
                            dataRow.add("");
                        }

                        dataRow.add(row.getStr("DEVICE_NAME"));
                        dataRow.add(row.getStr("DEVICE_CODE"));
                        dataRow.add(row.getStr("BATCH_NO"));
                        data.add(dataRow);
                    }
                }
            }

            // 设置列宽（移除型号ID和型号名称列）
            JSONArray columnWidths = new JSONArray();
            columnWidths.add(8);   // 序号
            columnWidths.add(25);  // 单机名称（增加宽度）
            columnWidths.add(20);  // 单机代号（增加宽度）
            columnWidths.add(20);  // 批次号（增加宽度）

            // 生成文件名
            String timestamp = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
            String fileName = "设备数据_" + taskName + "_" + timestamp;

            // 使用CommonUtil.createExcelFile创建Excel文件
            File excelFile = CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 25);

            log.info("导出设备Excel成功: " + excelFile.getAbsolutePath());
            return excelFile;
        } catch (Exception e) {
            log.error("导出设备Excel失败", e);
            return null;
        }
    }

    /**
     * 更新XML文件中的热点信息
     */
    private void updateHotspotInXml(String extractPath, String pan, String tilt, String editedTitle, String editedDescription) {
        try {
            // 查找pano.xml文件
            File panoXmlFile = findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                log.warn("未找到pano.xml文件，无法更新XML中的热点信息");
                return;
            }

            // 读取XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 查找所有panorama节点
            List<Element> panoramas = root.elements("panorama");
            boolean updated = false;
            int matchCount = 0;

            log.info("开始更新XML热点信息 - 定位: pan={}, tilt={}, 新标题: {}", pan, tilt, editedTitle);

            for (Element panorama : panoramas) {
                Element hotspotsElement = panorama.element("hotspots");
                if (hotspotsElement != null) {
                    List<Element> hotspots = hotspotsElement.elements("hotspot");

                    for (Element hotspot : hotspots) {
                        String xmlPan = hotspot.attributeValue("pan");
                        String xmlTilt = hotspot.attributeValue("tilt");
                        String skinid = hotspot.attributeValue("skinid");
                        String currentTitle = hotspot.attributeValue("title");
                        String xmlId = hotspot.attributeValue("id");

                        log.debug("检查热点 - ID: {}, skinid: {}, pan: {}, tilt: {}, title: {}",
                                xmlId, skinid, xmlPan, xmlTilt, currentTitle);

                        // 使用pan、tilt和skinid进行精确匹配
                        if ("stand-alone".equals(skinid) &&
                            pan != null && pan.equals(xmlPan) &&
                            tilt != null && tilt.equals(xmlTilt)) {

                            // 更新title和description属性
                            if (editedTitle != null && !editedTitle.trim().isEmpty()) {
                                hotspot.addAttribute("title", editedTitle);
                                log.info("更新热点标题: {} -> {} (定位: pan={}, tilt={})",
                                        currentTitle, editedTitle, pan, tilt);
                            }
                            if (editedDescription != null && !editedDescription.trim().isEmpty()) {
                                hotspot.addAttribute("description", editedDescription);
                                log.info("更新热点描述: {} (定位: pan={}, tilt={})",
                                        editedDescription, pan, tilt);
                            }
                            updated = true;
                            matchCount++;
                            log.info("已更新XML中热点 - 定位: pan={}, tilt={}", pan, tilt);
                            // 理论上pan+tilt组合是唯一的，但为了安全起见，继续检查
                        }
                    }
                }
            }

            if (updated) {
                // 写回XML文件
                writeXmlToFile(document, panoXmlFile);
                log.info("XML文件已更新: {}, 共更新 {} 个热点", panoXmlFile.getAbsolutePath(), matchCount);
            } else {
                log.warn("未找到XML中的热点 - 定位: pan={}, tilt={}", pan, tilt);
            }

        } catch (Exception e) {
            log.error("更新XML文件中的热点信息失败", e);
        }
    }

    /**
     * 将Document写入XML文件
     */
    private void writeXmlToFile(Document document, File xmlFile) throws Exception {
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");

        try (FileOutputStream fos = new FileOutputStream(xmlFile)) {
            XMLWriter writer = new XMLWriter(fos, format);
            writer.write(document);
            writer.close();
        }
    }

    /**
     * 获取热点列表
     * @param taskId 任务ID
     * @param panoramaId 全景节点ID（可选，用于多节点支持）
     * @param page 页码
     * @param limit 每页数量
     * @return 热点列表
     */
    public JSONObject getHotspotList(Long taskId, String panoramaId, Integer page, Integer limit) {
        try {
            int offset = (page - 1) * limit;

            // 构建WHERE条件，支持panoramaId过滤
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);
            if (panoramaId != null && !panoramaId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(panoramaId.replace("'", "''")).append("'");
                log.debug("按panoramaId过滤热点列表: taskId={}, panoramaId={}", taskId, panoramaId);
            } else {
                log.debug("获取任务下所有热点列表: taskId={}", taskId);
            }

            // 查询总数
            String countSql = "SELECT COUNT(*) AS TOTAL FROM PANORAMA_HOTSPOT h " + whereCondition.toString();
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            // 查询分页数据，包含PANORAMA_ID字段
            String dataSql = "SELECT * FROM (" +
                    "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                    "h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, h.EDITED_TITLE, h.EDITED_DESCRIPTION, " +
                    "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                    "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME, " +
                    "ROW_NUMBER() OVER (ORDER BY h.CREATE_TIME) AS RN " +
                    "FROM PANORAMA_HOTSPOT h " +
                    "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                    whereCondition.toString() + ") " +
                    "WHERE RN > " + offset + " AND RN <= " + (offset + limit);

            JSONArray dataResult = Util.postQuerySql(dataSql);

            log.debug("热点列表查询完成: taskId={}, panoramaId={}, total={}, page={}, limit={}",
                     taskId, panoramaId, total, page, limit);

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("data", dataResult)
                    .set("count", total);
        } catch (Exception e) {
            log.error("获取热点列表失败: taskId={}, panoramaId={}", taskId, panoramaId, e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取热点列表失败: " + e.getMessage())
                    .set("data", new JSONArray())
                    .set("count", 0);
        }
    }

    /**
     * 获取当前全景节点ID
     * @param taskId 任务ID
     * @return 当前节点信息
     */
    public JSONObject getCurrentNode(Long taskId) {
        try {
            // 获取任务的XML文件路径
            String taskSql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);

            if (taskResult == null || taskResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            JSONObject taskInfo = (JSONObject) taskResult.get(0);
            String extractPath = taskInfo.getStr("EXTRACT_PATH");

            if (extractPath == null || extractPath.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务没有XML文件路径");
            }

            // 查找并解析XML文件
            File panoXmlFile = findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到pano.xml文件");
            }

            // 解析XML获取当前节点
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 获取tour节点的start属性
            Element tourElement = root.element("tour");
            String currentNodeId = "node1"; // 默认值

            if (tourElement != null) {
                String startAttribute = tourElement.attributeValue("start");
                if (startAttribute != null && !startAttribute.trim().isEmpty()) {
                    currentNodeId = startAttribute;
                }
            }

            log.debug("获取当前节点: taskId={}, currentNodeId={}", taskId, currentNodeId);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj()
                            .set("currentNodeId", currentNodeId)
                            .set("taskId", taskId));

        } catch (Exception e) {
            log.error("获取当前节点失败: taskId={}", taskId, e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取当前节点失败: " + e.getMessage());
        }
    }

    /**
     * 更新热点信息
     */
    public JSONObject updateHotspot(Long hotspotId, String editedTitle, String editedDescription, Long deviceId) {
        try {
            // 首先获取热点信息，包括任务ID、定位信息（pan、tilt）和原始标题
            String querySql = "SELECT h.TASK_ID, h.HOTSPOT_XML_ID, h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, " +
                    "h.PAN, h.TILT, t.EXTRACT_PATH FROM PANORAMA_HOTSPOT h " +
                    "JOIN PANORAMA_TASK t ON h.TASK_ID = t.TASK_ID " +
                    "WHERE h.HOTSPOT_ID = " + hotspotId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到热点信息");
            }

            JSONObject hotspotInfo = (JSONObject) queryResult.get(0);
            Long taskId = hotspotInfo.getLong("TASK_ID");
            String hotspotXmlId = hotspotInfo.getStr("HOTSPOT_XML_ID");
            String originalTitle = hotspotInfo.getStr("ORIGINAL_TITLE");
            String pan = hotspotInfo.getStr("PAN");
            String tilt = hotspotInfo.getStr("TILT");
            String extractPath = hotspotInfo.getStr("EXTRACT_PATH");

            // 更新数据库
            StringBuilder updateSql = new StringBuilder("UPDATE PANORAMA_HOTSPOT SET ");
            boolean hasUpdate = false;

            if (editedTitle != null) {
                updateSql.append("EDITED_TITLE = '").append(editedTitle.replace("'", "''")).append("'");
                hasUpdate = true;
            }

            if (editedDescription != null) {
                if (hasUpdate) updateSql.append(", ");
                updateSql.append("EDITED_DESCRIPTION = '").append(editedDescription.replace("'", "''")).append("'");
                hasUpdate = true;
            }

            if (deviceId != null) {
                if (hasUpdate) updateSql.append(", ");
                updateSql.append("DEVICE_ID = ").append(deviceId);
                hasUpdate = true;
            }

            if (hasUpdate) {
                updateSql.append(", IS_EDITED = 1, UPDATE_TIME = SYSDATE");
                updateSql.append(" WHERE HOTSPOT_ID = ").append(hotspotId);

                Util.postCommandSql(updateSql.toString());

                // 更新XML文件中的热点信息，使用定位信息（pan、tilt）进行精确匹配
                if (extractPath != null && pan != null && tilt != null) {
                    updateHotspotInXml(extractPath, pan, tilt, editedTitle, editedDescription);
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "热点信息更新成功");
        } catch (Exception e) {
            log.error("更新热点信息失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "更新热点信息失败: " + e.getMessage());
        }
    }



    /**
     * 热点定位
     */
    public JSONObject locateHotspot(Long hotspotId) {
        try {
            String sql = "SELECT PAN, TILT FROM PANORAMA_HOTSPOT WHERE HOTSPOT_ID = " + hotspotId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "热点不存在");
            }

            // 安全获取热点数据
            Object firstRowObj = result.get(0);
            JSONObject hotspot = null;
            if (firstRowObj instanceof JSONObject) {
                hotspot = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspot);
        } catch (Exception e) {
            log.error("热点定位失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "热点定位失败: " + e.getMessage());
        }
    }

    /**
     * 根据热点信息查找热点记录
     * 优先使用坐标匹配，确保准确性
     */
    public JSONObject findHotspotByInfo(Long taskId, String nodeId, String hotspotId,
                                       String title, String description, String skinid) {
        try {
            log.debug("查找热点记录: taskId={}, nodeId={}, hotspotId={}, title={}, skinid={}",
                     taskId, nodeId, hotspotId, title, skinid);

            // 构建基础WHERE条件
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);

            // 添加节点ID过滤（如果提供）
            if (nodeId != null && !nodeId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(nodeId.replace("'", "''")).append("'");
            }

            // 构建多重匹配条件（按优先级排序）
            StringBuilder matchConditions = new StringBuilder();

            // 优先级1: 使用原始标题匹配（最可靠，因为标题在节点内通常是唯一的）
            if (title != null && !title.trim().isEmpty()) {
                matchConditions.append("h.ORIGINAL_TITLE = '").append(title.replace("'", "''")).append("'");
            }

            // 优先级2: 使用XML ID匹配（结合节点ID）
            if (hotspotId != null && !hotspotId.trim().isEmpty()) {
                if (matchConditions.length() > 0) {
                    matchConditions.append(" OR ");
                }
                matchConditions.append("h.HOTSPOT_XML_ID = '").append(hotspotId.replace("'", "''")).append("'");
            }

            // 优先级3: 使用皮肤ID匹配（结合其他条件）
            if (skinid != null && !skinid.trim().isEmpty()) {
                if (matchConditions.length() > 0) {
                    matchConditions.append(" OR ");
                }
                matchConditions.append("h.SKINID = '").append(skinid.replace("'", "''")).append("'");
            }

            // 如果没有任何匹配条件，返回错误
            if (matchConditions.length() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "缺少有效的热点标识信息");
            }

            // 构建完整的查询SQL
            String sql = "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                        "h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, h.EDITED_TITLE, h.EDITED_DESCRIPTION, " +
                        "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                        "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME " +
                        "FROM PANORAMA_HOTSPOT h " +
                        "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                        whereCondition.toString() + " AND (" + matchConditions.toString() + ") " +
                        "ORDER BY " +
                        "CASE " +
                        "  WHEN h.ORIGINAL_TITLE = '" + (title != null ? title.replace("'", "''") : "") + "' THEN 1 " +
                        "  WHEN h.HOTSPOT_XML_ID = '" + (hotspotId != null ? hotspotId.replace("'", "''") : "") + "' THEN 2 " +
                        "  ELSE 3 " +
                        "END";

            log.debug("执行热点查找SQL: {}", sql);
            JSONArray result = Util.postQuerySql(sql);

            if (result == null || result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到匹配的热点记录");
            }

            // 返回第一个匹配的记录（优先级最高的）
            Object firstRowObj = result.get(0);
            JSONObject hotspotData = null;
            if (firstRowObj instanceof JSONObject) {
                hotspotData = (JSONObject) firstRowObj;
            }

            log.info("成功找到热点记录: hotspotId={}, title={}",
                    hotspotData.getStr("HOTSPOT_XML_ID"), hotspotData.getStr("ORIGINAL_TITLE"));

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspotData)
                    .set("msg", "查找热点成功");

        } catch (Exception e) {
            log.error("查找热点记录失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "查找热点记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据坐标查找热点记录（最可靠的匹配方式）
     */
    public JSONObject findHotspotByCoordinates(Long taskId, String nodeId, String pan, String tilt) {
        try {
            log.debug("根据坐标查找热点记录: taskId={}, nodeId={}, pan={}, tilt={}",
                     taskId, nodeId, pan, tilt);

            // 构建基础WHERE条件
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);

            // 添加节点ID过滤（如果提供）
            if (nodeId != null && !nodeId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(nodeId.replace("'", "''")).append("'");
            }

            // 添加坐标匹配条件（精确匹配）
            if (pan != null && tilt != null) {
                whereCondition.append(" AND h.PAN = '").append(pan.replace("'", "''")).append("'");
                whereCondition.append(" AND h.TILT = '").append(tilt.replace("'", "''")).append("'");
            } else {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "缺少坐标信息");
            }

            // 构建完整的查询SQL
            String sql = "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                        "h.ORIGINAL_TITLE, h.ORIGINAL_DESCRIPTION, h.EDITED_TITLE, h.EDITED_DESCRIPTION, " +
                        "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                        "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME " +
                        "FROM PANORAMA_HOTSPOT h " +
                        "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                        whereCondition.toString();

            log.debug("执行坐标查找SQL: {}", sql);
            JSONArray result = Util.postQuerySql(sql);

            if (result == null || result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到匹配的热点记录");
            }

            // 返回匹配的记录
            Object firstRowObj = result.get(0);
            JSONObject hotspotData = null;
            if (firstRowObj instanceof JSONObject) {
                hotspotData = (JSONObject) firstRowObj;
            }

            log.info("根据坐标成功找到热点记录: pan={}, tilt={}, title={}",
                    pan, tilt, hotspotData.getStr("ORIGINAL_TITLE"));

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspotData)
                    .set("msg", "查找热点成功");

        } catch (Exception e) {
            log.error("根据坐标查找热点记录失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "根据坐标查找热点记录失败: " + e.getMessage());
        }
    }

    /**
     * 导出修改后的全景图包
     */
    public String exportPanorama(Long taskId) {
        try {
            // 1. 获取任务信息
            String querySql = "SELECT TASK_NAME, EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                throw new RuntimeException("任务不存在");
            }

            JSONObject taskInfo = (JSONObject) queryResult.get(0);
            String taskName = taskInfo.getStr("TASK_NAME");
            String extractPath = taskInfo.getStr("EXTRACT_PATH");

            if (extractPath == null || extractPath.trim().isEmpty()) {
                throw new RuntimeException("任务尚未上传全景图文件");
            }

            File extractDir = new File(extractPath);
            if (!extractDir.exists() || !extractDir.isDirectory()) {
                throw new RuntimeException("全景图文件目录不存在");
            }

            // 2. 创建导出目录和ZIP文件名
            String exportDir = tempPath + File.separator + "panorama_export_" + System.currentTimeMillis();
            FileUtil.mkdir(exportDir);

            String zipFileName = (taskName != null ? taskName : "panorama_task_" + taskId) + "_exported.zip";
            String zipFilePath = exportDir + File.separator + zipFileName;

            try {
                log.info("开始清理热点定位脚本: {}", extractPath);
                Pano2VRHotspotInjector.cleanDirectory(extractPath);
                log.info("热点定位脚本清理完成");
            } catch (Exception e) {
                log.warn("清理热点定位脚本失败，但不影响导出流程: {}", e.getMessage());
            }

            // 4. 将解压目录重新打包成ZIP文件
            log.info("开始打包全景图文件 - 源目录: {}, 目标文件: {}", extractPath, zipFilePath);
            ZipUtil.zip(extractPath, zipFilePath);

            // 5. 验证ZIP文件是否创建成功
            File zipFile = new File(zipFilePath);
            if (!zipFile.exists() || zipFile.length() == 0) {
                throw new RuntimeException("ZIP文件创建失败");
            }

            log.info("全景图包导出成功 - 文件: {}, 大小: {} bytes", zipFilePath, zipFile.length());

            // 6. 重新注入热点定位脚本（保持编辑功能可用）
            try {
                log.info("重新注入热点定位脚本: {}", extractPath);
                Pano2VRHotspotInjector.processDirectory(extractPath);
                log.info("热点定位脚本重新注入完成");
            } catch (Exception e) {
                log.warn("重新注入热点定位脚本失败: {}", e.getMessage());
            }

            // 7. 更新任务状态为已导出
            String updateSql = "UPDATE PANORAMA_TASK SET STATUS = 2, UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateSql);

            return zipFilePath;

        } catch (Exception e) {
            log.error("导出全景图包失败", e);
            throw new RuntimeException("导出全景图包失败: " + e.getMessage());
        }
    }

    /**
     * 获取全景图预览路径
     */
    public JSONObject getPreviewPath(Long taskId) {
        try {
            String sql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            // 安全获取提取路径
            String extractPath = null;
            if (result.size() > 0) {
                Object firstRowObj = result.get(0);
                if (firstRowObj instanceof JSONObject) {
                    extractPath = ((JSONObject) firstRowObj).getStr("EXTRACT_PATH");
                }
            }
            if (extractPath == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "尚未上传全景图文件");
            }

            // 查找index.html文件
            File indexFile = new File(extractPath + File.separator + "index.html");
            if (!indexFile.exists()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到index.html文件");
            }

            // 返回相对于webapps的路径
            String relativePath = extractPath.replace(fileUploadPath, "");
            String previewUrl = "/file/preview" + relativePath + "/index.html";

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj().set("previewUrl", previewUrl));
        } catch (Exception e) {
            log.error("获取预览路径失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取预览路径失败: " + e.getMessage());
        }
    }


    /**
     * 重新导入指定任务的型号单机数据
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    public JSONObject refreshDeviceData(Long taskId) {
        try {
            // 1. 根据taskId获取任务信息，特别是modelId和modelName
            String taskQuerySql = "SELECT MODEL_ID, MODEL_NAME FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskQuerySql);

            if (taskResult == null || taskResult.isEmpty()) {
                return JSONUtil.createObj().set("success", false).set("msg", "未找到指定任务");
            }

            JSONObject taskInfo = taskResult.getJSONObject(0);
            String modelId = taskInfo.getStr("MODEL_ID");
            String modelName = taskInfo.getStr("MODEL_NAME");

            if (modelId == null || modelId.trim().isEmpty()) {
                return JSONUtil.createObj().set("success", false).set("msg", "任务未关联任何型号，无法导入数据");
            }

            // 2. 调用私有方法来执行去重更新
            log.info("正在为任务ID {} 刷新/导入型号 {} 的单机数据...", taskId, modelName);
            queryAndSaveStandAloneDevices(taskId, modelId, modelName);
            log.info("任务ID {} 的单机数据已成功刷新/导入", taskId);

            return JSONUtil.createObj().set("success", true).set("msg", "单机数据刷新成功");

        } catch (Exception e) {
            log.error("重新导入单机数据失败, taskId={}", taskId, e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "重新导入单机数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建新热点（交互式热点添加）
     */
    public JSONObject createHotspot(JSONObject requestData) {
        try {
            // 提取参数
            Long taskId = requestData.getLong("taskId");
            String nodeId = requestData.getStr("nodeId");
            String title = requestData.getStr("title").trim();
            String description = requestData.getStr("description");
            Double pan = requestData.getDouble("pan");
            Double tilt = requestData.getDouble("tilt");
            String skinid = requestData.getStr("skinid", "stand-alone");
            String url = requestData.getStr("url", "");
            String target = requestData.getStr("target", "");

            log.info("开始创建热点: taskId={}, nodeId={}, title={}, pan={}, tilt={}",
                    taskId, nodeId, title, pan, tilt);

            // 验证任务是否存在
            String taskCheckSql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskCheckSql);
            if (taskResult == null || taskResult.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("message", "任务不存在");
            }

            String extractPath = taskResult.getJSONObject(0).getStr("EXTRACT_PATH");
            if (extractPath == null || extractPath.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("message", "任务尚未上传全景图文件");
            }

            // 检查热点标题是否重复
            JSONObject titleCheckResult = checkHotspotTitle(taskId, title, null);
            if (!titleCheckResult.getBool("success")) {
                return titleCheckResult;
            }
            if (titleCheckResult.getBool("isDuplicate")) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("message", "热点标题已存在，请使用其他标题");
            }

            // 检查坐标是否重复（容差0.01度）
            String coordinateCheckSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_HOTSPOT " +
                    "WHERE TASK_ID = " + taskId + " " +
                    "AND PANORAMA_ID = '" + nodeId.replace("'", "''") + "' " +
                    "AND ABS(TO_NUMBER(PAN) - " + pan + ") < 0.01 " +
                    "AND ABS(TO_NUMBER(TILT) - " + tilt + ") < 0.01";

            JSONArray coordinateResult = Util.postQuerySql(coordinateCheckSql);
            if (coordinateResult != null && !coordinateResult.isEmpty()) {
                int count = coordinateResult.getJSONObject(0).getInt("CNT");
                if (count > 0) {
                    return JSONUtil.createObj()
                            .set("success", false)
                            .set("message", "该位置已存在热点，请选择其他位置");
                }
            }

            // 生成唯一的热点XML ID
            String hotspotXmlId = generateUniqueHotspotId(taskId, nodeId);

            // 插入热点记录到数据库
            String insertSql = "INSERT INTO PANORAMA_HOTSPOT " +
                    "(HOTSPOT_ID, TASK_ID, PANORAMA_ID, HOTSPOT_XML_ID, " +
                    "ORIGINAL_TITLE, ORIGINAL_DESCRIPTION, EDITED_TITLE, EDITED_DESCRIPTION, " +
                    "PAN, TILT, SKINID, URL, TARGET, IS_EDITED, CREATE_TIME, UPDATE_TIME) " +
                    "VALUES (SEQ_PANORAMA_HOTSPOT.NEXTVAL, " + taskId + ", " +
                    "'" + nodeId.replace("'", "''") + "', " +
                    "'" + hotspotXmlId.replace("'", "''") + "', " +
                    "'" + title.replace("'", "''") + "', " +
                    (description != null ? "'" + description.replace("'", "''") + "'" : "NULL") + ", " +
                    "'" + title.replace("'", "''") + "', " +
                    (description != null ? "'" + description.replace("'", "''") + "'" : "NULL") + ", " +
                    "'" + pan + "', '" + tilt + "', " +
                    "'" + skinid.replace("'", "''") + "', " +
                    "'" + url.replace("'", "''") + "', " +
                    "'" + target.replace("'", "''") + "', " +
                    "1, SYSDATE, SYSDATE)";

            Util.postCommandSql(insertSql);

            // 更新XML文件
            boolean xmlUpdateSuccess = updateXmlFileForHotspotCreation(
                    extractPath, nodeId, hotspotXmlId, title, description,
                    pan.toString(), tilt.toString(), skinid, url, target);

            if (!xmlUpdateSuccess) {
                // XML更新失败，回滚数据库操作
                String rollbackSql = "DELETE FROM PANORAMA_HOTSPOT " +
                        "WHERE TASK_ID = " + taskId + " " +
                        "AND HOTSPOT_XML_ID = '" + hotspotXmlId.replace("'", "''") + "'";
                Util.postCommandSql(rollbackSql);

                return JSONUtil.createObj()
                        .set("success", false)
                        .set("message", "XML文件更新失败，热点创建失败");
            }

            log.info("热点创建成功: taskId={}, hotspotXmlId={}, title={}",
                    taskId, hotspotXmlId, title);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("message", "热点创建成功")
                    .set("data", JSONUtil.createObj()
                            .set("hotspotXmlId", hotspotXmlId)
                            .set("title", title)
                            .set("pan", pan)
                            .set("tilt", tilt));

        } catch (Exception e) {
            log.error("创建热点失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("message", "创建热点失败: " + e.getMessage());
        }
    }

    /**
     * 删除热点（交互式热点删除）
     */
    public JSONObject deleteHotspot(JSONObject requestData) {
        try {
            Long taskId = requestData.getLong("taskId");
            String nodeId = requestData.getStr("nodeId");
            String hotspotId = requestData.getStr("hotspotId");
            Double pan = requestData.getDouble("pan");
            Double tilt = requestData.getDouble("tilt");

            log.info("开始删除热点: taskId={}, nodeId={}, hotspotId={}, pan={}, tilt={}",
                    taskId, nodeId, hotspotId, pan, tilt);

            // 验证任务是否存在
            String taskCheckSql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskCheckSql);
            if (taskResult == null || taskResult.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("message", "任务不存在");
            }

            String extractPath = taskResult.getJSONObject(0).getStr("EXTRACT_PATH");

            // 查找要删除的热点记录
            String findSql = "SELECT HOTSPOT_ID, HOTSPOT_XML_ID, ORIGINAL_TITLE FROM PANORAMA_HOTSPOT " +
                    "WHERE TASK_ID = " + taskId;

            // 优先使用热点ID匹配
            if (hotspotId != null && !hotspotId.trim().isEmpty()) {
                findSql += " AND HOTSPOT_XML_ID = '" + hotspotId.replace("'", "''") + "'";
            }
            // 备选使用坐标匹配
            else if (pan != null && tilt != null) {
                findSql += " AND ABS(TO_NUMBER(PAN) - " + pan + ") < 0.01 " +
                          "AND ABS(TO_NUMBER(TILT) - " + tilt + ") < 0.01";
                if (nodeId != null && !nodeId.trim().isEmpty()) {
                    findSql += " AND PANORAMA_ID = '" + nodeId.replace("'", "''") + "'";
                }
            } else {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("message", "必须提供热点ID或坐标信息");
            }

            JSONArray findResult = Util.postQuerySql(findSql);
            if (findResult == null || findResult.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("message", "未找到要删除的热点");
            }

            JSONObject hotspotRecord = findResult.getJSONObject(0);
            Long dbHotspotId = hotspotRecord.getLong("HOTSPOT_ID");
            String xmlHotspotId = hotspotRecord.getStr("HOTSPOT_XML_ID");
            String title = hotspotRecord.getStr("ORIGINAL_TITLE");

            // 从数据库删除热点记录
            String deleteSql = "DELETE FROM PANORAMA_HOTSPOT WHERE HOTSPOT_ID = " + dbHotspotId;
            Util.postCommandSql(deleteSql);

            // 更新XML文件
            boolean xmlUpdateSuccess = updateXmlFileForHotspotDeletion(extractPath, nodeId, xmlHotspotId);

            if (!xmlUpdateSuccess) {
                log.warn("XML文件更新失败，但数据库删除已完成: hotspotId={}", xmlHotspotId);
                // 注意：这里不回滚数据库操作，因为XML更新失败不应该影响数据库的一致性
                // 用户可以通过刷新预览来重新生成XML文件
            }

            log.info("热点删除成功: taskId={}, hotspotId={}, title={}",
                    taskId, xmlHotspotId, title);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("message", "热点删除成功")
                    .set("data", JSONUtil.createObj()
                            .set("hotspotId", xmlHotspotId)
                            .set("title", title));

        } catch (Exception e) {
            log.error("删除热点失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("message", "删除热点失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的热点XML ID
     */
    private String generateUniqueHotspotId(Long taskId, String nodeId) {
        try {
            // 查询当前任务和节点下的最大热点编号
            String maxIdSql = "SELECT MAX(CASE " +
                    "WHEN REGEXP_LIKE(HOTSPOT_XML_ID, '^Point[0-9]+$') " +
                    "THEN TO_NUMBER(SUBSTR(HOTSPOT_XML_ID, 6)) " +
                    "ELSE 0 END) AS max_num " +
                    "FROM PANORAMA_HOTSPOT " +
                    "WHERE TASK_ID = " + taskId + " " +
                    "AND PANORAMA_ID = '" + nodeId.replace("'", "''") + "'";

            JSONArray result = Util.postQuerySql(maxIdSql);
            int maxNum = 0;
            if (result != null && !result.isEmpty()) {
                Object maxNumObj = result.getJSONObject(0).get("MAX_NUM");
                if (maxNumObj != null) {
                    maxNum = Integer.parseInt(maxNumObj.toString());
                }
            }

            // 生成新的ID
            return String.format("Point%02d", maxNum + 1);

        } catch (Exception e) {
            log.error("生成热点ID失败", e);
            // 降级方案：使用时间戳
            return "Point" + System.currentTimeMillis() % 100000;
        }
    }

    /**
     * 更新XML文件以添加新热点
     */
    private boolean updateXmlFileForHotspotCreation(String extractPath, String nodeId,
            String hotspotXmlId, String title, String description, String pan, String tilt,
            String skinid, String url, String target) {
        try {
            String xmlFilePath = extractPath + File.separator + "pano.xml";
            File xmlFile = new File(xmlFilePath);

            if (!xmlFile.exists()) {
                log.error("XML文件不存在: {}", xmlFilePath);
                return false;
            }

            // 解析XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(xmlFile);
            Element root = document.getRootElement();

            // 查找对应的panorama节点
            Element panoramaElement = null;
            List<Element> panoramaElements = root.elements("panorama");
            for (Element element : panoramaElements) {
                if (nodeId.equals(element.attributeValue("id"))) {
                    panoramaElement = element;
                    break;
                }
            }

            if (panoramaElement == null) {
                log.error("未找到节点ID为 {} 的panorama元素", nodeId);
                return false;
            }

            // 查找hotspots元素
            Element hotspotsElement = panoramaElement.element("hotspots");
            if (hotspotsElement == null) {
                log.error("未找到hotspots元素");
                return false;
            }

            // 创建新的hotspot元素
            Element newHotspotElement = hotspotsElement.addElement("hotspot");
            newHotspotElement.addAttribute("tilt", tilt);
            newHotspotElement.addAttribute("pan", pan);
            newHotspotElement.addAttribute("url", url);
            newHotspotElement.addAttribute("title", title);
            newHotspotElement.addAttribute("target", target);
            newHotspotElement.addAttribute("skinid", skinid);
            newHotspotElement.addAttribute("id", hotspotXmlId);
            newHotspotElement.addAttribute("description", description != null ? description : "");

            // 保存XML文件
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setEncoding("UTF-8");
            XMLWriter writer = new XMLWriter(new FileOutputStream(xmlFile), format);
            writer.write(document);
            writer.close();

            log.info("XML文件更新成功，已添加热点: {}", hotspotXmlId);
            return true;

        } catch (Exception e) {
            log.error("更新XML文件失败", e);
            return false;
        }
    }

    /**
     * 更新XML文件以删除热点
     */
    private boolean updateXmlFileForHotspotDeletion(String extractPath, String nodeId, String hotspotXmlId) {
        try {
            String xmlFilePath = extractPath + File.separator + "pano.xml";
            File xmlFile = new File(xmlFilePath);

            if (!xmlFile.exists()) {
                log.error("XML文件不存在: {}", xmlFilePath);
                return false;
            }

            // 解析XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(xmlFile);
            Element root = document.getRootElement();

            // 查找对应的panorama节点
            Element panoramaElement = null;
            List<Element> panoramaElements = root.elements("panorama");
            for (Element element : panoramaElements) {
                if (nodeId.equals(element.attributeValue("id"))) {
                    panoramaElement = element;
                    break;
                }
            }

            if (panoramaElement == null) {
                log.error("未找到节点ID为 {} 的panorama元素", nodeId);
                return false;
            }

            // 查找hotspots元素
            Element hotspotsElement = panoramaElement.element("hotspots");
            if (hotspotsElement == null) {
                log.error("未找到hotspots元素");
                return false;
            }

            // 查找并删除指定的hotspot元素
            List<Element> hotspotElements = hotspotsElement.elements("hotspot");
            boolean found = false;
            for (Element hotspotElement : hotspotElements) {
                if (hotspotXmlId.equals(hotspotElement.attributeValue("id"))) {
                    hotspotsElement.remove(hotspotElement);
                    found = true;
                    break;
                }
            }

            if (!found) {
                log.warn("未找到要删除的热点元素: {}", hotspotXmlId);
                return false;
            }

            // 保存XML文件
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setEncoding("UTF-8");
            XMLWriter writer = new XMLWriter(new FileOutputStream(xmlFile), format);
            writer.write(document);
            writer.close();

            log.info("XML文件更新成功，已删除热点: {}", hotspotXmlId);
            return true;

        } catch (Exception e) {
            log.error("更新XML文件失败", e);
            return false;
        }
    }
}
